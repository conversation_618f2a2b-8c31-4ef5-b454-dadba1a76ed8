package com.wzc.common.event.bus;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.eventbus.EventBus;
import com.wzc.be.common.kafka.service.KafkaService;
import com.wzc.common.event.model.EventBusDTO;
import com.wzc.common.util.SpringUtil;
import com.wzc.common.util.spring.PropertyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.annotation.Async;

import java.lang.reflect.InvocationTargetException;
import java.util.Iterator;

@Slf4j
public class KafkaEventBus extends EventBus {

    private final KafkaService kafkaService;
    private final SubscriberRegistry subscribers = new SubscriberRegistry(this);

    public KafkaEventBus(KafkaService kafkaService) {
        super("kafka-event-bus");
        this.kafkaService = kafkaService;
    }

    @Override
    public void post(Object event) {
        if (ObjectUtil.isNull(event)) {
            log.error("事件参数为空，跳过！事件参数一定要有值");
            return;
        }
        EventBusDTO eventBusDTO = new EventBusDTO();
        eventBusDTO.setEvent(event);
        eventBusDTO.setClassName(event.getClass().getName());
        String topic = "TOPIC_EVENT_" + PropertyUtil.getProperty("spring.application.name").toUpperCase();
        kafkaService.send(topic, event.toString(), JSONUtil.toJsonStr(eventBusDTO));
    }

    @Override
    public void register(Object event) {
        if (ObjectUtil.isNull(event)) {
            log.error("事件参数为空，不注册。");
            return;
        }
        subscribers.register(event);
    }

    @KafkaListener(topics = "TOPIC_EVENT_#{environment.getProperty('spring.application.name').toUpperCase()}",
            groupId = "GROUP_#{environment.getProperty('spring.application.name').toUpperCase()}_PRO"
    )
    public void customerMessage(String message) {
        log.info("event-bus消费层，消费数据 ==>{}", message);
        try {
            EventBusDTO eventBusDTO = JSONUtil.toBean(message, EventBusDTO.class);
            Object obj = JSONUtil.toBean(JSONUtil.toJsonStr(eventBusDTO.getEvent()), Class.forName(eventBusDTO.getClassName()));
            Iterator<Subscriber> eventSubscribers = subscribers.getSubscribers(obj);
            while (eventSubscribers.hasNext()) {
                Subscriber subscriber = eventSubscribers.next();
                if (subscriber == null) {
                    log.info("未获取到事件消费端，不处理 === 》事件类名：{}", eventBusDTO.getClassName());
                    continue;
                }
                SpringUtil.getBean(KafkaEventBus.class).execute(subscriber, obj);
            }
        } catch (Exception e) {
            log.error("事件中心消异常：", e);
        }
    }

    @Async
    public void execute(Subscriber subscriber,Object obj) {
        try {
            subscriber.getMethod().invoke(subscriber.getTarget(), obj);
        } catch (IllegalAccessException | InvocationTargetException e) {
            log.error("事件中心消异常===>异步任务：", e);
        }
    }

    @Override
    public void unregister(Object object) {
        if(ObjectUtil.isNull(object)){
            return;
        }
        subscribers.unregister(object);
    }
}
