<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wzc</groupId>
        <artifactId>wzc-common-parent-base3</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>

    <artifactId>wzc-common-web-base3</artifactId>
    <version>1.2.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>web-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-log-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-utils-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-openfeign-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-user-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-validation-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>jasypt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-easy-trans</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>