package com.wzc.common.web.enmus;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务代码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ReplayGuardCode implements BizError {

	/**
	 * 重放请求 - 授权过期
	 */
	REPLAY_UNAUTH_EXPIRED(10651, "请求已过期"),
	/**
	 * 重放请求 - 参数缺失
	 */
	REPLAY_PARAM_MISSING(10652, "缺少关键参数"),
	/**
	 * 重放请求 - 重复操作
	 */
	REPLAY_DUPLICATE_OPERATION(10653, "请勿重复点击，请稍后重试");

	/**
	 * code编码
	 */
	final Integer code;
	/**
	 * 中文信息描述
	 */
	final String msg;
}