package com.wzc.common.web.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzc.common.web.introspector.CustomDateAnnotationRead;
import com.wzc.common.web.introspector.CustomDateAnnotationWrite;
import com.wzc.common.web.introspector.CustomHtmlParameterResolver;
import com.wzc.common.web.introspector.ReplayGuardHandlerInterceptor;
import com.wzc.common.web.jackson.CustomReadModule;
import com.wzc.common.web.jackson.CustomWriteModule;
import com.wzc.common.web.spring.MappingApiJackson2HttpMessageConverter;
import feign.codec.Decoder;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.*;
import org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AllArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE)
public class MessageConfig implements WebMvcConfigurer {

    private final ObjectMapper objectMapper;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new ReplayGuardHandlerInterceptor()).addPathPatterns("/**");
    }
    /**
     * 使用 JACKSON 作为JSON MessageConverter
     * 消息转换，内置断点续传，下载和字符串
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.removeIf(x -> x instanceof StringHttpMessageConverter || x instanceof AbstractJackson2HttpMessageConverter);
        converters.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
        converters.add(new ByteArrayHttpMessageConverter());
        converters.add(new ResourceHttpMessageConverter());
        converters.add(new ResourceRegionHttpMessageConverter());
        converters.add(messageConverter());
    }

    @Override
    public void addArgumentResolvers(@NotNull List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new CustomHtmlParameterResolver());
    }

    private MappingApiJackson2HttpMessageConverter messageConverter(){
        return new MappingApiJackson2HttpMessageConverter(objectMapper);
    }

    @Bean
    public Decoder feignDecoder(List<HttpMessageConverter<?>> converter) {
        converter.removeIf(x -> x instanceof StringHttpMessageConverter || x instanceof AbstractJackson2HttpMessageConverter);
        converter.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
        converter.add(messageConverter());
        HttpMessageConverters httpMessageConverters = new HttpMessageConverters(converter);
        return new CustomSpringDecoder(() -> httpMessageConverters);
    }

    @Bean("customerObjectMapper")
    public ObjectMapper objectMapper(ObjectMapper objectMapper){
        ObjectMapper customerObjectMapper = objectMapper.copy();
        customerObjectMapper.registerModules(CustomReadModule.INSTANCE);
        customerObjectMapper.setAnnotationIntrospector(CustomDateAnnotationRead.INSTANCE);
        customerObjectMapper.registerModule(CustomWriteModule.INSTANCE);
        customerObjectMapper.setAnnotationIntrospector(CustomDateAnnotationWrite.INSTANCE);
        return customerObjectMapper;
    }

    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}
