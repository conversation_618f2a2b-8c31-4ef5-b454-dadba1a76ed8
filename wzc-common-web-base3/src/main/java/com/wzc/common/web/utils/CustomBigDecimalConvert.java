package com.wzc.common.web.utils;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wzc.common.string.StringPool;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

@UtilityClass
public class CustomBigDecimalConvert {

    private final DecimalFormat decimalFormat = new DecimalFormat("");

    public String toStr(BigDecimal bigDecimal, String pattern) {
        return toStr(bigDecimal, pattern, RoundingMode.HALF_UP);
    }

    public String toStr(BigDecimal bigDecimal){
        return toStr(bigDecimal,"#,##0.###",RoundingMode.HALF_UP);
    }

    public String toStr(BigDecimal bigDecimal, String pattern, RoundingMode roundingMode) {
        if (ObjUtil.isNull(bigDecimal)) {
            return StringPool.ZERO;
        }
        if (StrUtil.isNotBlank(pattern)) {
            decimalFormat.applyPattern(pattern);
            decimalFormat.setGroupingUsed(true);
            if (ObjUtil.isNotNull(roundingMode)) {
                decimalFormat.setRoundingMode(roundingMode);
            }
            return decimalFormat.format(bigDecimal);
        }
        return bigDecimal.stripTrailingZeros().toPlainString();
    }
}
