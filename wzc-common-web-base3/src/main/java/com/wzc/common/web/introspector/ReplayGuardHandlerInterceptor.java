package com.wzc.common.web.introspector;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.mapcloud.cloudnative.common.model.BizError;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.baidu.mapcloud.cloudnative.web.wrapper.CustomRequestWrapper;
import com.wzc.common.cache.InMemoryKeyValueStore;
import com.wzc.common.cache.redis.CustomRedis;
import com.wzc.common.util.spring.PropertyUtil;
import com.wzc.common.web.utils.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;

import static com.wzc.common.web.enmus.ReplayGuardCode.*;
import static java.util.concurrent.TimeUnit.MILLISECONDS;

/**
 * 防重放，相关业务逻辑处理
 *
 * <AUTHOR>
 */
@Slf4j
public class ReplayGuardHandlerInterceptor implements HandlerInterceptor {

    public static final String CACHE_LOCK_REPLAY_REDIS_PREFIX = "expiration:related:time";
    public static final String CACHE_LOCK_KEY = "expiration-related-time";
    private static final long EXPIRE_TIME = 5 * 60 * 1000; // 5分钟有效期

    // 请求处理前调用
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        //开关为true 以及是登录状态才去处理
        boolean replayGuardEnabled = Convert.toBool(PropertyUtil.getProperty("wzc.replayGuard.enable"), false) && UserContext.userId().isPresent();
        if (!replayGuardEnabled) {
            // 如果防重放开关为 false，直接跳过防重放逻辑
            return true;
        }
        if (request instanceof CustomRequestWrapper) {
            String timestampStr = request.getHeader(CACHE_LOCK_KEY);
            Boolean unTime = Convert.toBool(PropertyUtil.getProperty("wzc.replayGuard.time.enable"), false);
            if (unTime) {
                if (timestampStr == null) {
                    render(REPLAY_PARAM_MISSING, response);
                    return false;
                }
                long timestamp = Long.parseLong(timestampStr);
                long currentTime = System.currentTimeMillis();
                if (Math.abs(currentTime - timestamp) > EXPIRE_TIME) {
                    render(REPLAY_UNAUTH_EXPIRED, response);
                    return false;
                }
            }
            Long timeout = Convert.toLong(PropertyUtil.getProperty("wzc.replayGuard.timeout"), 200L);
            String requestHash = generateRequestHash(request, timestampStr);
            String redisKey = CACHE_LOCK_REPLAY_REDIS_PREFIX + requestHash;
            try {
                Boolean success = CustomRedis.setNx(redisKey, timeout, MILLISECONDS);
                if (Boolean.FALSE.equals(success)) {
                    render(REPLAY_DUPLICATE_OPERATION, response);
                    return false;
                }
            } catch (Exception e) {
                log.info("redis出现异常，走本地逻辑：{}", ExceptionUtil.stacktraceToString(e));
                Boolean success = InMemoryKeyValueStore.setNxLocal(redisKey, timeout, MILLISECONDS);
                if (Boolean.FALSE.equals(success)) {
                    render(REPLAY_DUPLICATE_OPERATION, response);
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) throws Exception {
    }

    private String generateRequestHash(HttpServletRequest request, String timestamp) throws Exception {
        String url = getUrl(request);
        String body = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);
        String data = StrUtil.concat(false, url, body, timestamp, UserContext.userId().get().toString());
        return md5(data);
    }

    public void render(BizError resultCode, HttpServletResponse response) {
        RestResponse<String> restResponse = new RestResponse<>();
        restResponse.setCode(resultCode.getCode());
        restResponse.setMsg(resultCode.getMsg());
        WebUtil.renderJson(response, restResponse);
    }

    private String getUrl(HttpServletRequest request) {
        String query = request.getQueryString();
        String url = request.getRequestURL().toString();
        return query == null ? url : url + "?" + query;
    }

    private String md5(String data) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] hashBytes = md.digest(data.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            hexString.append(Integer.toHexString(0xFF & b));
        }
        return hexString.toString();
    }
}
