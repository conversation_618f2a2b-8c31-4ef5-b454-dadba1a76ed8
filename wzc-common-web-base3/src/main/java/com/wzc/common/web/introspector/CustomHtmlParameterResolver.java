package com.wzc.common.web.introspector;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.wzc.common.web.utils.WebUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.context.request.NativeWebRequest;

public class CustomHtmlParameterResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(RequestParam.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, @NotNull NativeWebRequest webRequest, WebDataBinderFactory binderFactory){
        if(StrUtil.isBlank(parameter.getParameterName())){
            return null;
        }
        String value = webRequest.getParameter(parameter.getParameterName());
        return value != null ? WebUtil.escapeHtml4(value) : null;
    }
}
