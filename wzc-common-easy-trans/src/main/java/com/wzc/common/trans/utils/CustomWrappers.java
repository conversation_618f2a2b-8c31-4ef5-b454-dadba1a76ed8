package com.wzc.common.trans.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.LambdaUtils;
import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wzc.common.trans.annotation.CustomTrans;
import com.wzc.common.trans.enums.TransType;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

@UtilityClass
public class CustomWrappers {

    private static final Map<String, List<Field>> FIELD_MAP_CACHE = Maps.newHashMap();

    /**
     * 名称精确匹配
     * @param lambdaQueryWrapper 参数表达式
     * @param column 字段表达式
     * @param value 查询值
     * @param <T>
     */
    public static <T> LambdaQueryWrapper<T> eq(LambdaQueryWrapper<T> lambdaQueryWrapper, SFunction<T, ?> column,Object value) {
        if(ObjUtil.isNull(value)){
            return lambdaQueryWrapper;
        }
        if(StrUtil.isBlank(value.toString())){
            return  lambdaQueryWrapper;
        }
        CustomLambadaWrapper<T> customLambadaWrapper = new CustomLambadaWrapper<>();
        String selectCol = customLambadaWrapper.colToString(column);
        String fieldString = getFieldToString(column);
        List<Field> customTransFields = getFieldsWithCustomTransAnnotation(lambdaQueryWrapper.getEntityClass());
        // 现在你可以处理 customTransFields 列表，例如打印字段名
        for (Field field : customTransFields) {
            if (field.getName().equals(fieldString)) {
                CustomTrans annotation = field.getAnnotation(CustomTrans.class);
                sqlEq(lambdaQueryWrapper,annotation,selectCol,value);
            }
        }
        return lambdaQueryWrapper;
    }

    public static <T> LambdaQueryWrapper<T> eqOr(LambdaQueryWrapper<T> lambdaQueryWrapper, SFunction<T, ?> column,Object value,SFunction<T, ?> column2,Object value2) {
        if(ObjUtil.isNull(value)){
            return lambdaQueryWrapper;
        }
        if(StrUtil.isBlank(value.toString())){
            return  lambdaQueryWrapper;
        }
        CustomLambadaWrapper<T> customLambadaWrapper = new CustomLambadaWrapper<>();
        String selectCol = customLambadaWrapper.colToString(column);
        String fieldString = getFieldToString(column);
        String fieldString2 = getFieldToString(column2);
        String selectCol2 = customLambadaWrapper.colToString(column2);
        List<Field> customTransFields = getFieldsWithCustomTransAnnotation(lambdaQueryWrapper.getEntityClass());
        String sql1 = "";
        String sql2 = "";
        // 现在你可以处理 customTransFields 列表，例如打印字段名
        for (Field field : customTransFields) {
            if (field.getName().equals(fieldString)) {
                CustomTrans annotation = field.getAnnotation(CustomTrans.class);
                sql1 = SQLUtils.eqString(selectCol,annotation.type());
            }
            if(field.getName().equals(fieldString2)){
                CustomTrans annotation = field.getAnnotation(CustomTrans.class);
                sql2 = SQLUtils.eqString(selectCol2,annotation.type());
            }
        }
        String finalSql = sql1;
        String finalSql1 = sql2;
        lambdaQueryWrapper.and(ws->ws.apply(finalSql,Convert.toStr(value)).or().apply(finalSql1,Convert.toStr(value2)));
        return lambdaQueryWrapper;
    }

    /**
     * 手机号精确匹配
     * @param lambdaQueryWrapper 参数表达式
     * @param column 字段表达式
     * @param value 查询值
     * @param <T>
     */
    public static <T> LambdaQueryWrapper<T> eqPhone(LambdaQueryWrapper<T> lambdaQueryWrapper, SFunction<T, ?> column,Object value) {
        if(ObjUtil.isNull(value)){
            return lambdaQueryWrapper;
        }
        if(StrUtil.isBlank(value.toString())){
            return  lambdaQueryWrapper;
        }
        CustomLambadaWrapper<T> customLambadaWrapper = new CustomLambadaWrapper<>();
        String selectCol = customLambadaWrapper.colToString(column);
        String fieldString = getFieldToString(column);
        List<Field> customTransFields = getFieldsWithCustomTransAnnotation(lambdaQueryWrapper.getEntityClass());
        // 现在你可以处理 customTransFields 列表，例如打印字段名
        for (Field field : customTransFields) {
            if (field.getName().equals(fieldString)) {
                CustomTrans annotation = field.getAnnotation(CustomTrans.class);
                sqlEqPhone(lambdaQueryWrapper,annotation,selectCol,value);
            }
        }
        return lambdaQueryWrapper;
    }
    /**
     * 冗余字段添加查询逻辑
     *
     * @param lambdaQueryWrapper
     * @param <T>
     * @return
     */
    public static <T> LambdaQueryWrapper<T> like(LambdaQueryWrapper<T> lambdaQueryWrapper, SFunction<T, ?> column,Object value) {
        if(ObjUtil.isNull(value)){
            return lambdaQueryWrapper;
        }
        if(StrUtil.isBlank(value.toString())){
            return  lambdaQueryWrapper;
        }
        CustomLambadaWrapper<T> customLambadaWrapper = new CustomLambadaWrapper<>();
        String selectCol = customLambadaWrapper.colToString(column);
        String fieldString = getFieldToString(column);
        List<Field> customTransFields = getFieldsWithCustomTransAnnotation(lambdaQueryWrapper.getEntityClass());
        // 现在你可以处理 customTransFields 列表，例如打印字段名
        for (Field field : customTransFields) {
            if (field.getName().equals(fieldString)) {
                CustomTrans annotation = field.getAnnotation(CustomTrans.class);
                sqlLike(lambdaQueryWrapper,annotation,selectCol,value);
            }
        }
        return lambdaQueryWrapper;
    }

    /**
     * 冗余字段like场景
     * @param lambdaQueryWrapper
     * @param annotation
     * @param selectCol
     * @param <T>
     */
    private static <T> void sqlLike(LambdaQueryWrapper<T> lambdaQueryWrapper,CustomTrans annotation,String selectCol,Object value) {
        lambdaQueryWrapper.apply(SQLUtils.likeString(selectCol,annotation.type()),Convert.toStr(value));
    }

    /**
     * 冗余字段 等于 场景
     * @param lambdaQueryWrapper
     * @param annotation
     * @param selectCol
     * @param <T>
     */
    private <T> void sqlEq(LambdaQueryWrapper<T> lambdaQueryWrapper,CustomTrans annotation,String selectCol,Object value){
        lambdaQueryWrapper.apply(SQLUtils.eqString(selectCol,annotation.type()), Convert.toStr(value));
    }

    private  <T> void sqlEqPhone(LambdaQueryWrapper<T> lambdaQueryWrapper,CustomTrans annotation,String selectCol,Object value){
        if(StrUtil.isNotBlank(annotation.transPhone())){
            lambdaQueryWrapper.apply(SQLUtils.eqPhoneString(selectCol,Convert.toStr(value)));
        }
    }

    private <T> String getFieldToString(SFunction<T, ?> column){
        LambdaMeta lambdaMeta = LambdaUtils.extract(column);
        return BeanUtil.getFieldName(lambdaMeta.getImplMethodName());
    }


    private <T> List<Field> getFieldsWithCustomTransAnnotation(Class<T> clazz) {
        if (clazz == null) {
            return Lists.newArrayList();
        }
        List<Field> annotatedFields = FIELD_MAP_CACHE.get(clazz.getName());
        if (CollUtil.isNotEmpty(annotatedFields)) {
            return annotatedFields;
        }
        annotatedFields = Lists.newArrayList();
        // 获取类中声明的所有字段（包括私有、保护和默认访问权限的）
        Field[] fields = clazz.getDeclaredFields();
        // 遍历所有字段，检查是否有 @CustomTrans 注解
        for (Field field : fields) {
            if (field.isAnnotationPresent(CustomTrans.class)) {
                annotatedFields.add(field);
            }
        }
        FIELD_MAP_CACHE.put(clazz.getName(), annotatedFields);
        return annotatedFields;
    }
}
