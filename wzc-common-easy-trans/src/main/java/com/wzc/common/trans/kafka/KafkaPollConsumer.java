package com.wzc.common.trans.kafka;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.eventsender.entity.Event;
import com.wzc.common.trans.common.TransSet;
import com.wzc.common.trans.model.CommonEventEntity;
import com.wzc.common.trans.thread.CustomThreadFactory;
import com.wzc.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import java.time.Duration;
import java.util.Collections;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class KafkaPollConsumer {


    private final ThreadPoolExecutor executor;
    public KafkaPollConsumer() {
        int threadPoolSize = Runtime.getRuntime().availableProcessors() * 2;
        int threadPoolQueueSize = 10000;
        executor =  new ThreadPoolExecutor(threadPoolSize, threadPoolSize, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(threadPoolQueueSize),
                new CustomThreadFactory("KafkaConsumer"),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

    }

    public void start(KafkaConsumer<String, byte[]> consumer,String topic){
        consumer.subscribe(Collections.singletonList(topic));
        executor.submit(() -> runTask(consumer));
    }


    private void runTask(final KafkaConsumer<String, byte[]> consumer) {
        while (true) {
            try {
                ConsumerRecords<String, byte[]> consumerRecords = consumer.poll(Duration.ofMillis(500L));
                if (!consumerRecords.isEmpty()) {
                    for (final ConsumerRecord<String, byte[]> record : consumerRecords) {
                        handleRecord(record);
                    }
                }
            } catch (Exception e) {
                log.error("Kafka handle message error.", e);
                break;
            }
        }
    }

    private void handleRecord(ConsumerRecord<String, byte[]> record) {
        byte[] value = record.value();
        if (ObjectUtil.isNull(record.value())) {
            return;
        }
        Object obj = SerializationUtils.deserialize(value);
        if (obj instanceof Event) {
            Event event = (Event) obj;
            String json = SerializationUtils.deserialize(event.getEventBody());
            log.info("更新冗余字段数据:{}", json);
            CommonEventEntity commonEventEntity = JSON.parseObject(json, CommonEventEntity.class);
            if(StrUtil.isBlank(commonEventEntity.getDiff())){
                return;
            }
            try {
                SpringUtil.getBean(TransSet.class).refresh(commonEventEntity);
            }catch (Exception e){
                log.info("handle event error.", e);
            }
        }
    }
}
