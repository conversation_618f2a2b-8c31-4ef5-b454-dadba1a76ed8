package com.wzc.common.trans.config;


import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.wzc.common.trans.common.TransSet;
import com.wzc.common.trans.exec.mybatis.TransResultInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@ConditionalOnClass({MybatisPlusAutoConfiguration.class, Interceptor.class})
public class MybatisTransConfig {


    @Bean
    public TransResultInterceptor transResultInterceptor(TransSet transSet) {
        return new TransResultInterceptor(transSet);
    }
}