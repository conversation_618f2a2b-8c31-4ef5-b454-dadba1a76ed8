package com.wzc.common.trans.cache;

import cn.hutool.core.util.ObjUtil;
import com.google.common.collect.Lists;
import com.wzc.common.user.rpc.MateDataClientApi;
import com.wzc.common.user.rpc.UserClientApi;
import com.wzc.common.user.rpc.dto.CompanyBaseDTO;
import com.wzc.common.user.rpc.dto.MateDataListDTO;
import com.wzc.common.user.rpc.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;


/**
 * 缓存器  接口各个环境缓存问题，依赖redis缓存
 */
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class Caches {

    private final User<PERSON><PERSON><PERSON><PERSON> userClientApi;
    private final MateDataClientA<PERSON> mateDataClientApi;

    private final static String USER_BY_ID = "USER_TYPE:";
    private final static String DRIVER_BY_ID = "USER_TYPE_DRIVER:";
    private final static String MARINER_BY_ID = "USER_TYPE_MARINER:";
    private final static String COMPANY_BY_ID = "COMPANY_TYPE:";
    private final static String MATE_DATE_CAR_BY_ID = "MATE_DATA_TYPE_SHIP:";
    private final static String MATE_DATE_SHIP_BY_ID = "MATE_DATA_CAR_TYPE:";

    /** ================用户信息=================》 */
    /**
     * 获取用户信息缓存
     * @param id
     * @return
     */
    @Cacheable(value = USER_BY_ID, key = "#id")
    public UserDTO getUserDTOById(Long id){
        UserDTO userDTO = userClientApi.getBaseUser(id);
        if (ObjUtil.isNotNull(userDTO) && ObjUtil.isNotNull(userDTO.getUserId())) {
            userDTO.setId(userDTO.getUserId());
        }
        return userDTO;
    }

    /**
     * 获取用户信息  司机
     * @param id
     * @return
     */
    @Cacheable(value = DRIVER_BY_ID, key = "#id")
    public UserDTO getDriverById(Long id){
        List<UserDTO> list = userClientApi.batchQuerySourceDriver(Lists.newArrayList(id));
        UserDTO userDTO = !list.isEmpty() ? list.get(0) : new UserDTO();
        if (ObjUtil.isNotNull(userDTO) && ObjUtil.isNotNull(userDTO.getUserId())) {
            userDTO.setId(userDTO.getUserId());
        }
        return userDTO;
    }

    /**
     * 获取用户信息 船员
     * @param id
     * @return
     */
    @Cacheable(value = MARINER_BY_ID, key = "#id")
    public UserDTO getUserMariner(Long id){
        List<UserDTO> list = userClientApi.batchQuerySourceMariner(Lists.newArrayList(id));
        UserDTO userDTO = !list.isEmpty() ? list.get(0) : new UserDTO();
        if (ObjUtil.isNotNull(userDTO) && ObjUtil.isNotNull(userDTO.getUserId())) {
            userDTO.setId(userDTO.getUserId());
        }
        return userDTO;
    }


    /**===========组织机构=============》*/
    @Cacheable(value = COMPANY_BY_ID, key = "#id")
    public CompanyBaseDTO getCompanyBaseDTOById(Long id){
        return userClientApi.batchQuerySourceShipper(Lists.newArrayList(id));
    }


    @Cacheable(value = MATE_DATE_CAR_BY_ID, key = "#id")
    public MateDataListDTO getMateDataListDTOCarById(Long id) {
        List<MateDataListDTO> mateDataListDTOS = mateDataClientApi.queryCarListByIds(Lists.newArrayList(id));
        return mateDataListDTOS.stream().findFirst().orElse(new MateDataListDTO());
    }

    @Cacheable(value = MATE_DATE_SHIP_BY_ID, key = "#id")
    public MateDataListDTO getMateDataListDTOShipById(Long id) {
        List<MateDataListDTO> mateDataListDTOS = mateDataClientApi.queryShipList(Lists.newArrayList(id));
        return mateDataListDTOS.stream().findFirst().orElse(new MateDataListDTO());
    }

    /**=============刷新缓存=========*/
    /**
     * 刷新缓存 基础数据
     * @return
     */
    @CachePut(value = MATE_DATE_CAR_BY_ID, key = "#mateDataListDTO.id", condition = "#result?.id != null")
    public MateDataListDTO refreshMateDataCar(MateDataListDTO mateDataListDTO){
        log.info("更新缓存={}", mateDataListDTO.toString());
        return mateDataListDTO;
    }

    @CachePut(value = MATE_DATE_SHIP_BY_ID, key = "#mateDataListDTO.id", condition = "#result?.id != null")
    public MateDataListDTO refreshMateDataShip(MateDataListDTO mateDataListDTO){
        log.info("更新缓存={}", mateDataListDTO.toString());
        return mateDataListDTO;
    }
    /**
     * 刷新缓存 用户信息
     * @return
     */
    @CachePut(value = USER_BY_ID, key = "#userDTO.id", condition = "#result?.id != null")
    public UserDTO refreshUserDTO(UserDTO userDTO){
        log.info("更新缓存={}", userDTO.toString());
        return userDTO;
    }

    @CachePut(value = DRIVER_BY_ID, key = "#userDTO.id", condition = "#result?.id != null")
    public UserDTO refreshUseDriverDTO(UserDTO userDTO) {
        log.info("更新缓存={}", userDTO.toString());
        return userDTO;
    }

    @CachePut(value = MARINER_BY_ID, key = "#userDTO.id", condition = "#result?.id != null")
    public UserDTO refreshUseMarinerDTO(UserDTO userDTO) {
        log.info("更新缓存={}", userDTO.toString());
        return userDTO;
    }
    /**
     * 刷新缓存 组织架构
     * @return
     */
    @CachePut(value = COMPANY_BY_ID, key = "#companyBaseDTO.id", condition = "#result?.id != null")
    public CompanyBaseDTO refreshCompanyDTO(CompanyBaseDTO companyBaseDTO){
        log.info("更新缓存={}", companyBaseDTO.toString());
        return companyBaseDTO;
    }
}
