package com.wzc.common.crypto;

import com.wzc.common.crypto.config.CryptoProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(CryptoProperties.class)
@ConditionalOnProperty(value = CryptoProperties.PREFIX + ".enabled", havingValue = "true", matchIfMissing = true)
public class CryptoConfiguration {

}