package com.wzc.common.base.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BizUserEntity extends BizEntity{

    /**
     * 创建人
     */
    @TableField(value = "creator_id",fill = FieldFill.INSERT)
    private Long creatorId;

    /**
     * 修改人
     */
    @TableField(value = "operator_id",fill = FieldFill.INSERT_UPDATE)
    private Long operatorId;
}
