package com.wzc.common.qr.enums;

import com.baidu.mapcloud.cloudnative.file.qrcode.enums.QrEventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum QrEventEnum implements QrEventTypeEnum {

    LOGIN(1, "扫码登录事件"),
    DRIVER_CARD(2, "司机个⼈名⽚"),
    ORDER_SENDER_CAR(3, "订单扫码派⻋"),
    TRANSPORT_SENDER_CAR(4, "运输计划扫码派⻋"),
    SEAL(5, "封铅"),
    COMPANY_BUSINESS_CARD(6,"公司名片")
    ;

    private final Integer code;
    private final String name;
}
