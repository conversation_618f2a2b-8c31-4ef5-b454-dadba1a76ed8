package com.wzc.common.export.feign;


import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.common.export.model.ExportDataServerQO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(url = "${base3.sys.url}", name = "ExportServerFeign")
public interface ExportServerFeign {

    @PostMapping("/inner/sys/v1/createExportTask")
    RestResponse<String> createExportTask(@RequestBody ExportDataServerQO exportDataServerQo);
}
