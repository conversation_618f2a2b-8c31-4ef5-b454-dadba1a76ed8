package com.wzc.common.export.utils;

import cn.hutool.core.util.StrUtil;
import com.wzc.common.export.web.ExportClientAction;
import com.wzc.common.export.web.ExportDataController;
import com.wzc.common.string.StringPool;
import com.wzc.common.util.spring.PropertyUtil;
import lombok.experimental.UtilityClass;

/**
 * 生成URL
 */
@UtilityClass
public class AppNameUtils {


    /**
     * 生成当前服务URL，无前缀，获取数据
     *
     * @return
     */
    public String getExportDataUrl() {
        return ExportClientAction.API_PREFIX + getAppName(PropertyUtil.getProperty("spring.application.name")) + ExportClientAction.GET_DATA;
    }

    public String getExportDataUrl(String appName) {
        return ExportClientAction.API_PREFIX + getAppName(appName) + ExportClientAction.GET_DATA;
    }


    /**
     * 生成当前服务URL 无前缀  创建任务
     *
     * @return
     */
    public String getCreateTaskUrl() {
        return ExportDataController.API + getAppName(PropertyUtil.getProperty("spring.application.name")) + ExportDataController.CRATE_EXPORT_TASK;
    }

    /**
     * 生成指定服务URL，k8s内部完整路径 获取数据接口
     * 服务命名规范，可以写死逻辑
     * @param appName 应用名称
     * @return 返回一个完整获取数据的路径
     */
    public String getExportDataPath(String appName) {
        appName = appName + "-service";
        String k8sName = PropertyUtil.getProperty("K8S_NAMESPACE");
        if(StrUtil.isNotBlank(k8sName)){
            appName = appName + StringPool.DOT + k8sName;
        }
        return StringPool.HTTP + appName;
    }

    /**
     * 获取服务中间的名称
     *
     * @return
     */
    public String getAppName(String appName) {
        appName = appName.replaceAll("wzc-", "");
        appName = appName.replaceAll("-base3", "");
        return appName.trim();
    }

}
