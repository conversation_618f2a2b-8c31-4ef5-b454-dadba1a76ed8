package com.wzc.common.export.feign;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.wzc.be.validation.asserts.AssertsUtils;
import com.wzc.common.core.api.utils.ApiResultUtils;
import com.wzc.common.exception.ServiceException;
import com.wzc.common.export.annotation.ContentMerge;
import com.wzc.common.export.annotation.Sheet;
import com.wzc.common.export.constant.ConstantsExport;
import com.wzc.common.export.enums.ExportEnums;
import com.wzc.common.export.enums.FileTypeEnums;
import com.wzc.common.export.enums.ResultCode;
import com.wzc.common.export.handler.*;
import com.wzc.common.export.model.ExcelClassInfo;
import com.wzc.common.export.model.ExcelInterfaceDTO;
import com.wzc.common.export.model.ExportDataQO;
import com.wzc.common.export.model.ExportDataServerQO;
import com.wzc.common.util.SSRFUtils;
import com.wzc.common.util.spring.PropertyUtil;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.wzc.common.export.enums.ResultCode.*;


/**
 * 服务端请求，任务中心创建任务
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExportServerClientApi {

    private final ExportServerFeign exportServerFeign;
    private final ObjectMapper objectMapper;

    /**
     * 将类序列化成一个json，方便服务端导出
     *
     * @return
     */
    @SuppressWarnings("deprecation")
    private static String generateJson(Class<?> clazz) {
        Map<String, Object> jsonObject = Maps.newHashMap();
        // 获取类的所有字段
        Field[] fields = clazz.getDeclaredFields();
        AtomicInteger index = new AtomicInteger(0);
        // 遍历所有字段
        Arrays.stream(fields).parallel().forEach(field -> {
            // 获取字段的名称
            String fieldName = field.getName();
            ExcelInterfaceDTO builder = new ExcelInterfaceDTO();
            ExcelIgnore excelIgnore = field.getAnnotation(ExcelIgnore.class);
            if(field.getType().isAssignableFrom(BigDecimal.class)) {
                builder.setClassType(field.getType().getName());
            }else if (ClassUtil.isBasicType(field.getClass())) {
                builder.setClassType(field.getType().getName());
            }else {
                builder.setClassType(String.class.getName());
            }
            //如果设置了字段排查，这边不处理导出逻辑
            if(ObjectUtil.isNull(excelIgnore)){
                // 获取字段的注解信息
                ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);
                if (ObjectUtil.isNotNull(excelProperty)) {
                    builder.setOrder(excelProperty.order());
                    builder.setIndex(excelProperty.index() == -1 ? index.get() : excelProperty.index());
                    builder.setConverter(excelProperty.converter());
                    builder.setValue(excelProperty.value());
                    builder.setFormat(excelProperty.format());
                }
                ColumnWidth columnWidth = field.getAnnotation(ColumnWidth.class);
                if (ObjectUtil.isNotNull(columnWidth)) {
                    builder.setWidth(columnWidth.value());
                }
                ContentMerge contentMerge = field.getAnnotation(ContentMerge.class);
                //记录那些列需要合并
                if (ObjectUtil.isNotNull(contentMerge) && contentMerge.isMerge()) {
                    builder.setContentMerge(true);
                    builder.setGroup(contentMerge.isGroup());
                }
                String json = JSONUtil.toJsonStr(builder);
                jsonObject.put(fieldName, json);
                index.getAndIncrement();
            }
        });
        ExcelClassInfo infoBuilder = ExcelClassInfo.builder().build();
        Sheet sheet = clazz.getAnnotation(Sheet.class);
        if (ObjectUtil.isNotNull(sheet)) {
            infoBuilder.setSheetName(sheet.sheetName());
            infoBuilder.setMergeStartRowIndex(sheet.mergeStartRowIndex());
        }
        infoBuilder.setClassName(clazz.getName());
        //增加类名称，sys特殊地方要排除这个key
        jsonObject.put(ConstantsExport.CLASS_INFO, JSONUtil.toJsonStr(infoBuilder));
        return JSONUtil.toJsonStr(jsonObject);
    }

    public String putTask(ExportDataServerQO qo) {
        String taskId = ApiResultUtils.getDataIgnoreNull(exportServerFeign.createExportTask(qo));
        if (StrUtil.isBlank(taskId)) {
            throw new ServiceException(ResultCode.EXPORT_REST_ERROR);
        }
        return taskId;
    }

    @SneakyThrows
    @SuppressWarnings({"all"})
    public String crateExportTask(@NonNull BaseHandler baseHandler, ExportDataQO exportDataQo) {
        String type = baseHandler.type();
        String json = JSONUtil.toJsonStr(exportDataQo.getParams());
        ExportDataServerQO exportDataServer = new ExportDataServerQO();
        exportDataServer.setAppName(PropertyUtil.getProperty("spring.application.name"));
        exportDataServer.setType(type);
        ExportEnums enums = baseHandler.exportType();
        exportDataServer.setParams(json);
        exportDataServer.setCallbackUrl(baseHandler.callbackUrl());
        exportDataServer.setExportType(baseHandler.exportType());
        exportDataServer.setReportName(baseHandler.reportName());
        //如果取不到，就用EXPORT 代表非三期的，非三期的不区分
        exportDataServer.setAppCode(UserContext.appCode().orElse("EXPORT"));
        //如果taskName 没有传递，则使用默认时间戳用作文件名称
        exportDataServer.setTaskName(Convert.toStr(exportDataQo.getTaskName(), System.currentTimeMillis() + ""));
        exportDataServer.setUserId(baseHandler.userId());
        Long total = 0L;
        switch (enums) {
            case EXPORT_EXCEL_SHEET:
                ExportExcelSheetHandler exportExcelSheetHandler = (ExportExcelSheetHandler) baseHandler;
                List<String> listJson = new LinkedList<>();
                Arrays.stream(exportExcelSheetHandler.getExcelSheetTemplate()).forEach(aClass -> {
                    listJson.add(generateJson(aClass));
                });
                exportDataServer.setExcelJson(JSONUtil.toJsonStr(listJson));
                exportDataServer.setFileType(FileTypeEnums.EXCEL);
                exportDataServer.setPageTotal(JSONUtil.toJsonStr(exportExcelSheetHandler.sheetTotal(objectMapper.readValue(json, exportExcelSheetHandler.currentMapperClass(exportExcelSheetHandler.getClass())))));
                break;
            case EXPORT_REPORT:
                log.warn("暂时不支持该导出方式，请检查！");
                AssertsUtils.isFalse(baseHandler.exportType().equals(ExportEnums.EXPORT_REPORT) && StrUtil.isBlank(baseHandler.reportName()), EXPORT_TYPE_REPORT);
                break;
            case EXPORT_ZIP:
                log.info("导出ZIP文件");
                ExportZipHandler exportZipHandler = (ExportZipHandler) baseHandler;
                total = exportZipHandler.total(objectMapper.readValue(json, exportZipHandler.currentMapperClass(exportZipHandler.getClass())));
                exportDataServer.setFileType(FileTypeEnums.ZIP);
                exportDataServer.setPageTotal(Convert.toStr(total));
                break;
            case EXPORT_PDF_HTML:
                log.info("导出pdf_html单个文件");
                ExportPdfHtmlHandler exportPdfHtmlHandler = (ExportPdfHtmlHandler) baseHandler;
                total = exportPdfHtmlHandler.total(objectMapper.readValue(json, exportPdfHtmlHandler.currentMapperClass(exportPdfHtmlHandler.getClass())));
                exportDataServer.setPageTotal(Convert.toStr(total));
                exportDataServer.setFileType(FileTypeEnums.PDF);
                break;
            case EXPORT_CUSTOM:
            case EXPORT_TEMPLATE:
                log.info("导出excel单个文件");
                ExportExcelHandler exportExcelHandler = null;
                ExportDataHandler exportDataHandler = null;
                ExportExcelTemplateHandler exportExcelTemplateHandler = null;
                //兼容旧的方式。
                try {
                    exportExcelHandler = (ExportExcelHandler) baseHandler;
                } catch (Exception e) {
                    exportDataHandler = (ExportDataHandler) baseHandler;
                }
                AssertsUtils.isFalse(ObjectUtil.isNull(exportDataHandler) && ObjectUtil.isNull(exportExcelHandler), EXPORT_REST_HANDLER_ERROR);
                if (ObjectUtil.isNotNull(exportExcelHandler)) {
                    total = exportExcelHandler.total(objectMapper.readValue(json, exportExcelHandler.currentMapperClass(exportExcelHandler.getClass())));
                    exportDataServer.setPageTotal(Convert.toStr(total));
                    JSONObject excelJsonObject = JSONUtil.parseObj(generateJson(exportExcelHandler.getExcelTemplate()));
                    //模版导出
                    if (enums.equals(ExportEnums.EXPORT_TEMPLATE)) {
                        try {
                            exportExcelTemplateHandler = (ExportExcelTemplateHandler) exportExcelHandler;
                        } catch (Exception e) {
                            throw new ServiceException(ResultCode.EXPORT_REST_HANDLER_ERROR);
                        }
                        log.info("根据模版导出excel单个文件");
                        String fileUrl = exportExcelTemplateHandler.templateUrl();
                        if (StrUtil.isBlank(fileUrl)) {
                            throw new ServiceException(ResultCode.EXPORT_ANNOTATION_TEMPLATE_NULL, fileUrl);
                        }
                        excelJsonObject.set(exportExcelTemplateHandler.getTemplateNameKey(), SSRFUtils.normalizeUrl(fileUrl));
                    }
                    exportDataServer.setExcelJson(JSONUtil.toJsonStr(excelJsonObject));
                }
                if (ObjectUtil.isNotNull(exportDataHandler)) {
                    total = exportDataHandler.total(objectMapper.readValue(json, exportDataHandler.currentMapperClass(exportDataHandler.getClass())));
                    exportDataServer.setPageTotal(Convert.toStr(total));
                    String excelJsonStr = generateJson(exportDataHandler.getExcelTemplate());
                    JSONObject excelJsonObject = JSONUtil.parseObj(excelJsonStr);
                    //动态标题头设置
                    JSONObject dynamicHeaderObject = exportDataHandler.dynamicHeaderTitle(objectMapper.readValue(json, exportDataHandler.currentMapperClass(exportDataHandler.getClass())));
                    this.setDynamicHeaderTitle(excelJsonObject, dynamicHeaderObject);
                    //设置动态标题头
                    List<String> ignoreColumnList = exportDataHandler.ignoreColumn(objectMapper.readValue(json, exportDataHandler.currentMapperClass(exportDataHandler.getClass())));
                    this.setIgnoreColumn(excelJsonObject, ignoreColumnList);
                    exportDataServer.setExcelJson(JSONUtil.toJsonStr(excelJsonObject));
                }
                exportDataServer.setFileType(FileTypeEnums.EXCEL);
                break;
            case EXPORT_EXCEL_TAB:
                ExportExcelTabHandler exportExcelTabHandler = (ExportExcelTabHandler) baseHandler;
                List<String> jsons = new LinkedList<>();
                Arrays.stream(exportExcelTabHandler.getExcelSheetTemplate()).forEach(aClass -> {
                    jsons.add(generateJson(aClass));
                });
                exportDataServer.setExcelJson(JSONUtil.toJsonStr(jsons));
                exportDataServer.setFileType(FileTypeEnums.EXCEL);
                exportDataServer.setPageTotal(JSONUtil.toJsonStr(exportExcelTabHandler.sheetTotal(objectMapper.readValue(json, exportExcelTabHandler.currentMapperClass(exportExcelTabHandler.getClass())))));
                break;
            default:
                log.warn("获取配置异常，不支持的导出类型！type={}", baseHandler.exportType());
                throw new ServiceException(EXPORT_ERROR);
        }
        return putTask(exportDataServer);
    }


    /**
     * <AUTHOR>
     * @Description 设置动态标题头
     * @Date 13:54 2024/8/14
     * @Param [excelJsonObject, dynamicHeaderObject]
     * @return void
     **/
    private void setDynamicHeaderTitle(JSONObject excelJsonObject, JSONObject dynamicHeaderObject) {
        if (ObjectUtil.isNotEmpty(dynamicHeaderObject)){
            for (String key : dynamicHeaderObject.keySet()) {
                String value = dynamicHeaderObject.getStr(key);
                if (excelJsonObject.containsKey(key)) {
                    JSONObject subscribeConditionObject = excelJsonObject.getJSONObject(key);
                    subscribeConditionObject.set("value", value);
                    excelJsonObject.set(key, JSONUtil.toJsonStr(subscribeConditionObject));
                }
            }
        }
    }

    /**
     * <AUTHOR>
     * @Description 设置隐藏列
     * @Date 13:55 2024/8/14
     * @Param [excelJsonObject, ignoreColumnList]
     * @return void
     **/
    private void setIgnoreColumn(JSONObject excelJsonObject, List<String> ignoreColumnList){
        if (CollUtil.isNotEmpty(ignoreColumnList)){
            for (String string : ignoreColumnList) {
                excelJsonObject.remove(string);
            }
        }
    }
}
