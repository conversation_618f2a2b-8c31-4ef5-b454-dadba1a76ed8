package com.wzc.common.export.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExportEnums {
    /**
     * 基于html的pdf生成方式，这个方式基于无头浏览器打开html生成pdf，默认调用data
     */
    EXPORT_TEMPLATE(7,"EXCEL_TEMPLATE",FileTypeEnums.EXCEL),
    EXPORT_EXCEL_TAB(6,"EXCEL_TAB",FileTypeEnums.EXCEL),
    EXPORT_EXCEL_SHEET(5,"EXCEL_SHEET",FileTypeEnums.EXCEL),
    EXPORT_REPORT(4,"REPORT",FileTypeEnums.EXCEL),
    EXPORT_PDF_HTML(3, "PDF_HTML",FileTypeEnums.PDF),
    EXPORT_ZIP(2, "ZIP",FileTypeEnums.ZIP),
    EXPORT_CUSTOM(1, "EXCEL",FileTypeEnums.EXCEL);

    final Integer code;
    final String msg;
    final FileTypeEnums fileTypeEnums;
}
