package com.wzc.common.export.handler.easy;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.MapUtils;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.wzc.common.string.StringPool;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

/**
 * 自定义 自适应宽度
 * <AUTHOR>
 **/
public class CustomCellWriteWidthConfig extends AbstractColumnWidthStyleStrategy {

    private static final int MAX_COLUMN_WIDTH = 255;

    private final Map<Integer, Map<Integer, Integer>> cache = MapUtils.newHashMapWithExpectedSize(8);

    private final Map<Integer, Map<Integer, Map<Integer, Integer>>> widthCache = MapUtils.newHashMapWithExpectedSize(8);
    private final Map<Integer, Map<Integer, Map<Integer, String>>> headerCache = MapUtils.newHashMapWithExpectedSize(8);

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell,
                                  Head head,
                                  Integer relativeRowIndex, Boolean isHead) {
        boolean needSetWidth = isHead || !CollectionUtils.isEmpty(cellDataList);
        if (!needSetWidth) {
            return;
        }
        Map<Integer, Integer> maxColumnWidthMap = cache.computeIfAbsent(writeSheetHolder.getSheetNo(), key -> new HashMap<>(16));
        // 取当前sheet页数据
        Map<Integer, Map<Integer, Integer>> currentSheetRowColumnWidthMap = widthCache.computeIfAbsent(writeSheetHolder.getSheetNo(), key -> new HashMap<>(16));
        Map<Integer, Map<Integer, String>> currentSheetRowColumnKeyMap =
                headerCache.computeIfAbsent(writeSheetHolder.getSheetNo(), key -> new HashMap<>(16));
        // 取当前行数据
        Map<Integer, Integer> currentRowColumnWidthMap = currentSheetRowColumnWidthMap.computeIfAbsent(cell.getRowIndex(), key -> new HashMap<>(16));
        Map<Integer, String> currentRowColumnKeyMap = currentSheetRowColumnKeyMap.computeIfAbsent(cell.getRowIndex(), key -> new HashMap<>(16));
        Integer columnWidth = dataLength(cellDataList, cell, isHead);
        if (columnWidth > MAX_COLUMN_WIDTH) {
            columnWidth = MAX_COLUMN_WIDTH;
        }
        if (isHead) {
            currentRowColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
            currentRowColumnKeyMap.put(cell.getColumnIndex(), cell.getStringCellValue());
        }
        if (columnWidth < 0) {
            return;
        }
        Integer maxColumnWidth = maxColumnWidthMap.get(cell.getColumnIndex());
        if (maxColumnWidth == null || columnWidth > maxColumnWidth) {
            //处理表头多行 换行符行高问题
            if (cell.getCellType() == CellType.STRING && cell.getStringCellValue().contains(StringPool.NEWLINE)) {
                int height = cell.getStringCellValue().split(StringPool.NEWLINE).length * 20 * 20 + 4;
                writeSheetHolder.getSheet().getRow(cell.getRowIndex()).setHeight((short) height);
            }
            maxColumnWidthMap.put(cell.getColumnIndex(), columnWidth);
            writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), columnWidth * 256);
        }
        //处理头部多行表头的问题
        if (isHead && cell.getRowIndex() > 0) {
            Map<Integer, String> previousRowColumnKeyMap = currentSheetRowColumnKeyMap.get(cell.getRowIndex() - 1);
            Map<Integer, Integer> previousRowColumnWidthMap = currentSheetRowColumnWidthMap.get(cell.getRowIndex() - 1);
            if(CollUtil.isEmpty(previousRowColumnKeyMap) || CollUtil.isEmpty(previousRowColumnWidthMap)){
                return;
            }
            if (previousRowColumnKeyMap.size() - 1 == cell.getColumnIndex()) {
                List<List<Integer>> sameParentColumnIndices = getParentColumnIndex(previousRowColumnKeyMap);
                for (List<Integer> sameParentColumnIndex : sameParentColumnIndices) {
                    Integer firstIndex = sameParentColumnIndex.get(0);
                    Integer parentColumnMinWidth = previousRowColumnWidthMap.get(firstIndex);
                    int parentColumnMaxWidth = previousRowColumnWidthMap.get(firstIndex) * sameParentColumnIndex.size();
                    Integer childrenColumnsWidth = 0;
                    for (Integer index : sameParentColumnIndex) {
                        childrenColumnsWidth += currentRowColumnWidthMap.get(index);
                    }
                    if (parentColumnMinWidth < childrenColumnsWidth && parentColumnMaxWidth > childrenColumnsWidth) {
                        for (Integer index : sameParentColumnIndex) {
                            writeSheetHolder.getSheet().setColumnWidth(index, currentRowColumnWidthMap.get(index) * 256);
                        }
                    } else if (parentColumnMinWidth > childrenColumnsWidth) {
                        for (Integer index : sameParentColumnIndex) {
                            int newWidth = parentColumnMinWidth * currentRowColumnWidthMap.get(index) / childrenColumnsWidth;
                            if (newWidth > currentRowColumnWidthMap.get(index)) {
                                writeSheetHolder.getSheet().setColumnWidth(index, newWidth * 256);
                                currentRowColumnWidthMap.put(index, newWidth);
                            }
                        }
                    } else if (childrenColumnsWidth > parentColumnMaxWidth) {
                        for (Integer index : sameParentColumnIndex) {
                            writeSheetHolder.getSheet().setColumnWidth(index, currentRowColumnWidthMap.get(index) * 256);
                        }
                    }
                }
            }
        }
    }

    private List<List<Integer>> getParentColumnIndex(Map<Integer, String> previousRowColumnKeyMap) {
        List<List<Integer>> result = new ArrayList<>();
        int calcIndex = 0;
        for (Integer columnIndex : previousRowColumnKeyMap.keySet()) {
            if (calcIndex >= columnIndex && columnIndex != 0) {
                continue;
            }
            String headerValue = previousRowColumnKeyMap.get(columnIndex);
            List<Integer> sameIndex = new ArrayList<>();
            sameIndex.add(columnIndex);
            for (Integer nextColumnIndex : previousRowColumnKeyMap.keySet()) {
                if (columnIndex >= nextColumnIndex) {
                    continue;
                }
                String nextHeaderValue = previousRowColumnKeyMap.get(nextColumnIndex);
                if (ObjectUtil.equals(headerValue, nextHeaderValue)) {
                    sameIndex.add(nextColumnIndex);
                    calcIndex = nextColumnIndex;
                } else {
                    break;
                }
            }
            if (sameIndex.size() > 1) {
                result.add(sameIndex);
            }
        }
        return result;
    }

    private Integer dataLength(List<WriteCellData<?>> cellDataList, Cell cell, Boolean isHead) {
        if (isHead) {
            //处理title宽度
            AtomicReference<Integer> integer = new AtomicReference<>(0);
            if (cell.getStringCellValue().contains(StringPool.NEWLINE)) {
                Arrays.asList(cell.getStringCellValue().split(StringPool.NEWLINE)).forEach(str -> {
                    if (str.getBytes().length > integer.get()) {
                        integer.set(str.getBytes().length);
                    }
                });
            } else {
                integer.set(cell.getStringCellValue().getBytes().length);
            }
            return cell.getStringCellValue().getBytes().length;
        }
        WriteCellData<?> cellData = cellDataList.get(0);
        CellDataTypeEnum type = cellData.getType();
        if (type == null) {
            return -1;
        }
        switch (type) {
            case STRING:
                // 换行符（数据需要提前解析好）
                int index = cellData.getStringValue().indexOf(StringPool.NEWLINE);
                return index != -1 ?
                        cellData.getStringValue().substring(0, index).getBytes().length + 1 : cellData.getStringValue().getBytes().length + 1;
            case BOOLEAN:
                return cellData.getBooleanValue().toString().getBytes().length + 1;
            case NUMBER:
                return cellData.getNumberValue().toString().getBytes().length + 1;
            default:
                return -1;
        }
    }
}

