package com.wzc.common.export.handler;

import com.wzc.common.export.enums.ExportEnums;

/**
 * 模版导出
 *
 * @param <T>
 * @param <E>
 */
public interface ExportExcelTemplateHandler<T, E> extends ExportExcelHandler<T, E> {


    String TEMPLATE_NAME_KEY = "templateUrl";

    @Override
    String templateUrl();

    default ExportEnums exportType() {
        return ExportEnums.EXPORT_TEMPLATE;
    }

    default String getTemplateNameKey() {
        return TEMPLATE_NAME_KEY;
    }
}
