package com.wzc.common.export.web;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzc.common.export.enums.ExportEnums;
import com.wzc.common.export.enums.ResultCode;
import com.wzc.common.export.handler.BaseHandler;
import com.wzc.common.export.handler.ExportExcelSheetHandler;
import com.wzc.common.export.handler.ExportExcelTabHandler;
import com.wzc.common.export.model.ExportDataQO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExportClientAction {
    public static final String API_PREFIX = "/inner/";

    public static final String GET_DATA = "/v1/getData";

    private final List<BaseHandler<?, ?>> exportDataHandlerList;

    private final ObjectMapper objectMapper;

    @SuppressWarnings({"all"})
    public RestResponse<Object> getDate(@RequestBody ExportDataQO exportDataQo) throws JsonProcessingException {
        if (null == exportDataQo) {
            return RestResponse.error(ResultCode.EXPORT_ERROR);
        }
        BaseHandler exportDataHandler = exportDataHandlerList.stream().filter(item -> item.type().equals(exportDataQo.getType())).findFirst().orElse(null);
        if (ObjectUtil.isNull(exportDataHandler)) {
            return RestResponse.error(ResultCode.EXPORT_REST_HANDLER_ERROR);
        }
        String json = JSONUtil.toJsonStr(exportDataQo.getParams());
        Class clazz = exportDataHandler.currentMapperClass(exportDataHandler.getClass());
        if (exportDataHandler.exportType().equals(ExportEnums.EXPORT_EXCEL_SHEET)) {
            ExportExcelSheetHandler exportExcelSheetHandler = (ExportExcelSheetHandler) exportDataHandler;
            return RestResponse.success(((ExportExcelSheetHandler) exportDataHandler).getSheetDate(objectMapper.readValue(json, clazz)));
        }
        if (exportDataHandler.exportType().equals(ExportEnums.EXPORT_EXCEL_TAB)) {
            ExportExcelTabHandler exportExcelTabHandler = (ExportExcelTabHandler) exportDataHandler;
            return RestResponse.success(((ExportExcelTabHandler) exportDataHandler).getSheetDate(objectMapper.readValue(json, clazz)));
        }
        return RestResponse.success(exportDataHandler.getDate(objectMapper.readValue(json, clazz)));
    }
}
