package com.wzc.common.export.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzc.common.export.feign.ExportServerClientApi;
import com.wzc.common.export.feign.ExportServerFeign;
import com.wzc.common.export.handler.BaseHandler;
import com.wzc.common.export.handler.ExportDataHandler;
import com.wzc.common.export.listener.ExportDataListener;
import com.wzc.common.export.mapping.RegisterMappingBean;
import com.wzc.common.export.web.ExportClientAction;
import com.wzc.common.export.web.ExportDataController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.List;

/**
 * 任务中心导出数据
 *
 * <AUTHOR>
 */
@Order(Integer.MIN_VALUE)
@Configuration
@Slf4j
@ConditionalOnProperty(name = "wzc.common.export.enabled", havingValue = "true")
@EnableFeignClients(clients = {ExportServerFeign.class})
public class ExportDataConfig {


    @Bean
    public ExportClientAction exportClientAction(List<BaseHandler<?,?>> exportDataHandlerList,
                                                 @Qualifier("customerObjectMapper")ObjectMapper objectMapper) {
        return new ExportClientAction(exportDataHandlerList,objectMapper);
    }

    @Bean
    public ExportServerClientApi exportServerClientApi(ExportServerFeign exportServerFeign,
                                                       @Qualifier("customerObjectMapper") ObjectMapper objectMapper) {
        return new ExportServerClientApi(exportServerFeign, objectMapper);
    }

    @Bean
    public ExportDataListener exportDataListener() {
        return new ExportDataListener();
    }

    @Bean
    public ExportDataController exportDataController(ExportServerClientApi exportServerClientApi, List<BaseHandler<?,?>> exportDataHandlerList) {
        return new ExportDataController(exportServerClientApi, exportDataHandlerList);
    }

    @Bean
    public RegisterMappingBean registerMappingBean(RequestMappingHandlerMapping requestMappingHandlerMapping, ExportClientAction exportClientAction,
                                                   ExportDataController exportDataController) {
        return new RegisterMappingBean(requestMappingHandlerMapping, exportClientAction, exportDataController);
    }
}
