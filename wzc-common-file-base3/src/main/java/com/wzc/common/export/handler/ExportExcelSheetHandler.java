package com.wzc.common.export.handler;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.wzc.common.export.enums.ExportEnums;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 通用多sheet方案，只支持2个sheet 2个数据源的情况
 *
 * <AUTHOR>
 */
public interface ExportExcelSheetHandler<T> extends BaseHandler<T, Object> {

    String SHEET_PREFIX="sheet";
    /**
     * 分页获取数据
     *
     * @param t 参数
     * @return 返回分页数据  模版  key 对应className
     */
    Map<String, List<Object>> getSheetDate(T t);

    /**
     * excel模版类
     *
     * @return 返回配置的easy-excel类型
     */
    Class<?>[] getExcelSheetTemplate();

    /**
     * 数据源 sheet数据源总数
     *
     * @param t 入参
     * @return 返回总页数
     */
    List<Long> sheetTotal(T t);

    @Override
    default ExportEnums exportType() {
        return ExportEnums.EXPORT_EXCEL_SHEET;
    }

    default Pagination<Object> getDate(T t) {
        return null;
    }

    default Long total(T t) {
        return 0L;
    }
}
