package com.wzc.common.export.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExcelInterfaceDTO {
    int index;
    int order;
    Class<?> converter;
    String format;
    private String[] value;
    private String classType;
    private Integer width;
    private String sheetName;
    private String className;
    private boolean contentMerge;
    private boolean isGroup;
    private String fieldName;
}
