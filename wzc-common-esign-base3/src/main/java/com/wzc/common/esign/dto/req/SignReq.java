package com.wzc.common.esign.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(
    description = "签章抽象类公共参数"
)
@Data
public abstract class SignReq implements Serializable {
    @NotBlank(
        message = "文件ID不能为空"
    )
    @Schema(
        name = "百度云文件ID"
    )
    private String pdfUuid;
    @NotBlank(
        message = "合同名称不能为空"
    )
    @Schema(
        name = "合同名称"
    )
    private String pdfName;
    @NotNull(
        message = "签署位置不能为空"
    )
    private PosBeanReq posBeanReq;

    public SignReq() {
    }

}
