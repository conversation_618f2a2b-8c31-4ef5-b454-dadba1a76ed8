package com.wzc.common.esign.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(
    description = "平台方签署盖章"
)
@Data
public class PlatformSignReq extends SignReq {
    @NotNull(
        message = "sealId不能为空"
    )
    @Schema(
        name = "接口调用方(平台方)的印章,请在www.tsign.cn官网中设置默认印章其sealId值为0"
    )
    private Integer sealId;

    private byte[] pdfBytes;


}
