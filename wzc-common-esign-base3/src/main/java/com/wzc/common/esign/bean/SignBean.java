package com.wzc.common.esign.bean;
import com.timevale.esign.sdk.tech.bean.PosBean;
import com.timevale.esign.sdk.tech.bean.SignPDFStreamBean;

import java.io.Serializable;


public class SignBean implements Serializable {
    public SignBean() {
    }

    public SignBean(SignPDFStreamBean signPDFStreamBean, PosBean posBean) {
        this.signPDFStreamBean = signPDFStreamBean;
        this.posBean = posBean;
    }

    /**
     * pdf信息
     */
    private SignPDFStreamBean signPDFStreamBean;

    /**
     * 盖章信息
     */
    private PosBean posBean;


    public SignPDFStreamBean getSignPDFStreamBean() {
        return signPDFStreamBean;
    }

    public void setSignPDFStreamBean(SignPDFStreamBean signPDFStreamBean) {
        this.signPDFStreamBean = signPDFStreamBean;
    }

    public PosBean getPosBean() {
        return posBean;
    }

    public void setPosBean(PosBean posBean) {
        this.posBean = posBean;
    }

}
