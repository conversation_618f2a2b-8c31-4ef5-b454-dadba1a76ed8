
package com.wzc.common.esign.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

@Schema(
    description = "企业模板印章"
)
@Data
public class OrgSwalReq implements Serializable {
    @NotBlank(
        message = "帐号不能为空"
    )
    @Schema(
        name = "帐号"
    )
    private String accountId;
    @Schema(
        name = "模板类型"
    )
    private String templateType;
    @Schema(
        name = "模板类型"
    )
    private String color;
    @Size(
        max = 8,
        message = "横向文内容最长为8"
    )
    @Schema(
        name = "生成印章中的横向文内容"
    )
    private String hText;
    @Size(
        max = 20,
        message = "下弦文内容最长为20"
    )
    @Schema(
        name = "生成印章中的下弦文内容"
    )
    private String qText;

    public OrgSwalReq() {
    }

}
