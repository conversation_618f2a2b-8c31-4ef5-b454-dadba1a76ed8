<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, user-scalable=yes, initial-scale=1.0">
    <title>预览</title>
    <script type="text/javascript" src="${baseUrl}${contextPath}/res/ureport-asserts/js/base64.min.js"></script>
    <script type="text/javascript" src="${baseUrl}${contextPath}/res/ureport-asserts/js/watermark.js"></script>
    <script>
        /**
         * 初始化水印
         */
        function initWaterMark() {
            let watermarkTxt = '';
            if (watermarkTxt !== '') {
                watermark.init({
                    watermark_txt: '',
                    watermark_x: 0,
                    watermark_y: 0,
                    watermark_rows: 0,
                    watermark_cols: 0,
                    watermark_x_space: 10,
                    watermark_y_space: 10,
                    watermark_font: '微软雅黑',
                    watermark_fontsize: '18px',
                    watermark_color: 'black',
                    watermark_alpha: 0.2,
                    watermark_width: 180,
                    watermark_height: 80,
                    watermark_angle: 10,
                });
            }
        }
    </script>
</head>

<body>
<iframe src="" width="100%" frameborder="0"></iframe>
</body>

<script type="text/javascript">
    let url = '${pdfUrl}';
    const base_url = '${baseUrl}'.endsWith('/') ? '${baseUrl}' : '${baseUrl}' + '/';
    if (!url.startsWith(base_url)) {
        url = base_url + '${contextPath}/onlinePreview/getCorsFile?urlPath=' + encodeURIComponent(Base64.encode(url));
    }
    document.getElementsByTagName('iframe')[0].src = base_url + 'pdf/web/viewer.html?file=' + encodeURIComponent(url) + "&disablepresentationmode=${pdfPresentationModeDisable}&disableopenfile=${pdfOpenFileDisable}&disableprint=${pdfPrintDisable}&disabledownload=${pdfDownloadDisable}&disablebookmark=${pdfBookmarkDisable}&disableediting=${pdfDisableEditing}";
    document.getElementsByTagName('iframe')[0].height = document.documentElement.clientHeight;
    /**
     * 页面变化调整高度
     */
    window.onresize = function () {
        var fm = document.getElementsByTagName("iframe")[0];
        fm.height = window.document.documentElement.clientHeight;
    }

    /*初始化水印*/
    window.onload = function () {
        initWaterMark();
    }
</script>
</html>

<style>
    * {
        margin: 0;
        padding: 0;
    }
    html, body {
        height: 100%;
        width: 100%;
    }
</style>
