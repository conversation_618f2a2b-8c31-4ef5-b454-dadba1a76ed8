/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.bstek.ureport.console.html;

import cn.hutool.core.util.StrUtil;
import com.bstek.ureport.build.Context;
import com.bstek.ureport.build.ReportBuilder;
import com.bstek.ureport.build.paging.Page;
import com.bstek.ureport.cache.CacheUtils;
import com.bstek.ureport.chart.ChartData;
import com.bstek.ureport.console.MobileUtils;
import com.bstek.ureport.console.RenderPageServletAction;
import com.bstek.ureport.console.cache.TempObjectCache;
import com.bstek.ureport.console.exception.ReportDesignException;
import com.bstek.ureport.definition.Paper;
import com.bstek.ureport.definition.ReportDefinition;
import com.bstek.ureport.definition.searchform.FormPosition;
import com.bstek.ureport.exception.ReportComputeException;
import com.bstek.ureport.export.*;
import com.bstek.ureport.export.html.HtmlProducer;
import com.bstek.ureport.export.html.HtmlReport;
import com.bstek.ureport.export.html.SearchFormData;
import com.bstek.ureport.model.Report;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.codehaus.jackson.map.ObjectMapper;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2017年2月15日
 */
@Slf4j
public class HtmlPreviewServletAction extends RenderPageServletAction {
	private ExportManager exportManager;
	private ReportBuilder reportBuilder;
	private ReportRender reportRender;
	private HtmlProducer htmlProducer=new HtmlProducer();
	@Override
	public void execute(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String method=retriveMethod(req);
		if(method!=null){
			invokeMethod(method, req, resp);
		}else{
			VelocityContext context = new VelocityContext();
			HtmlReport htmlReport=null;
			String errorMsg=null;
			try{
				htmlReport=loadReport(req);
			}catch(Exception ex){
				if(!(ex instanceof ReportDesignException)){
					ex.printStackTrace();					
				}
				errorMsg=buildExceptionMessage(ex);
			}
			String title=buildTitle(req);
			context.put("title", title);
			if(htmlReport==null){
				context.put("content", "<div style='color:red'><strong>报表计算出错，错误信息如下：</strong><br><div style=\"margin:10px\">"+errorMsg+"</div></div>");
				context.put("error", true);
				context.put("searchFormJs", "");
				context.put("downSearchFormHtml", "");
				context.put("upSearchFormHtml", "");
			}else{
				SearchFormData formData=htmlReport.getSearchFormData();
				if(formData!=null){
					context.put("searchFormJs", formData.getJs());
					if(formData.getFormPosition().equals(FormPosition.up)){
						context.put("upSearchFormHtml", formData.getHtml());						
						context.put("downSearchFormHtml", "");						
					}else{
						context.put("downSearchFormHtml", formData.getHtml());						
						context.put("upSearchFormHtml", "");						
					}
				}else{
					context.put("searchFormJs", "");
					context.put("downSearchFormHtml", "");
					context.put("upSearchFormHtml", "");	
				}
				//获取参数，通过参数判断是否需要执行该操作
				String openStr = req.getParameter("_open");
				log.info("执行自定义插件判断 open:{}",openStr);
				Boolean isOpen = Boolean.valueOf(openStr);
				if (isOpen) {
					//开关打开，执行自定义插件
					Document parse = Jsoup.parse(htmlReport.getContent());
					parse.getElementsByTag("a").attr("style","color: red;text-decoration: underline;");
					Elements tr = parse.getElementsByTag("tr");
					String coordinate = req.getParameter("_coordinate");
					//默认为J列
					String[] clomStr = {"J"};
					log.info("需要修改的列 coordinate:{}",coordinate);
					if (!StrUtil.isEmpty(coordinate)) {
						String[] split = coordinate.split("[,]");
						clomStr = split;
					}
					String clasPrefix = "_";
					String clasSuffix = "";
					for (int k = 0; k < tr.size(); k++) {
						//判断是否存在总计
						Elements td = tr.get(k).getElementsByTag("td");
						Element first = td.first();
						if ("总计".equals(first.text())){
							//获取class最后一个数字
							String attr = first.attr("class");
							if (StrUtil.isEmpty(attr)){
								break;
							}
							log.info("attr:{}",attr);
							String substring = attr.substring(attr.length() - 1, attr.length());
							Integer value = Integer.valueOf(substring);
							clasSuffix = (value - 1) + "";
							for (String item : clomStr) {
								String clasAll = clasPrefix+item+clasSuffix;
								Elements elements = tr.get(k - 1).getElementsByClass(clasAll);
								Element firstTwo = elements.first();
								Elements aTag = firstTwo.getElementsByTag("a");
								String href = aTag.first().attr("href");
								//href 去除参数 mineralBatch and carrierName
								String[] split = href.split("[?]");
								String[] paramStr = split[split.length - 1].split("&");
								StringBuffer updateUrl = new StringBuffer(split[0]);
								updateUrl.append("?");
								for (int i = 0; i < paramStr.length; i++) {
									if (paramStr[i].startsWith("mineralBatch") || paramStr[i].startsWith("carrierName")){
										continue;
									}
									if (i == 0){
										updateUrl.append(paramStr[i]);
									}else{
										updateUrl.append("&").append(paramStr[i]);
									}
								}
								//当前值
								Elements ele = tr.get(k).getElementsByClass(clasPrefix+item+value);
								Element tds = ele.first();
								Elements a = tds.getElementsByTag("a");
								Element first1 = a.first();
								if (null == first1) {
									continue;
								}
								first1.attr("href",updateUrl.toString());
							}
						}
					}
					context.put("content", parse.html());
				}else{
					context.put("content", htmlReport.getContent());
				}

				//判断是否开启固定表头
				String fixed = req.getParameter("_fixed");
				log.info("执行自定义插件判断 fixed:{}",fixed);
				Boolean isFixed = Boolean.valueOf(fixed);
				if (isFixed){
					Document parse = Jsoup.parse(context.get("content").toString());
					Elements tr = parse.getElementsByTag("tr");
					//创建table
					Element table = parse.createElement("table");
					Element table1 = parse.getElementsByTag("table").first();
					table.attr("id","fixedHead");
					table.attr("border","0");
					String u = req.getParameter("_u");
					String[] split = u.split("[:]");
					log.info("split[1]:{}",split[1]);
					if (split[1].startsWith("DailyReport")) {
						table.attr("style","margin: auto; border-collapse: collapse; position: sticky; height: 40pt; z-index: 3; top: 0px; background: rgb(255, 255, 255) !important;");
						table1.attr("style","margin:auto;z-index: 1;");
					}else{
						table.attr("style","margin: auto; border-collapse: collapse; position: sticky; height: 40pt; z-index: 2; top: 0px; background: rgb(255, 255, 255) !important;");
						table1.attr("style","margin:auto;z-index: 1;");
					}
					//设置 不同的table
					Integer xhIndex = -1;
					for (int i = 0; i < tr.size(); i++) {
						Element td = tr.get(i).getElementsByTag("td").first();
						//定位到序号行
						if(td == null){
							continue;
						}
						if ("序号".equals(td.text().trim())) {
							xhIndex = i;
							break;
						}
					}
					//elementList 保存原来的
					StringBuffer sb = new StringBuffer();
					for (Integer i = 0; i <= xhIndex; i++) {
						sb.append("<tr>").append(tr.get(i).html()).append("</tr>");
						tr.get(i).remove();
					}
					table.html(sb.toString());
					table1.before(table);
					table1.attr("id","wrap");
					//追加JS
					String jsWjd = "<script type=\"text/javascript\">\n" +
							"    var pre_scrollTop = 0;//滚动条事件之前文档滚动高度\n" +
							"    var pre_scrollLeft = 0;//滚动条事件之前文档滚动宽度\n" +
							"    var obj_fixedHead;\n" +
							"\n" +
							"    window.onload = function () {\n" +
							"        pre_scrollTop = (document.documentElement.scrollTop || document.body.scrollTop);\n" +
							"        pre_scrollLeft = (document.documentElement.scrollLeft || document.body.scrollTop);\n" +
							"        obj_fixedHead = document.getElementById(\"fixedHead\");\n" +
							"    };\n" +
							"    window.onscroll = function () {\n" +
							"        if (pre_scrollTop != (document.documentElement.scrollTop || document.body.scrollTop)) {\n" +
							"            //滚动了竖直滚动条\n" +
							"            pre_scrollTop = (document.documentElement.scrollTop || document.body.scrollTop);\n" +
							"            if (obj_fixedHead) {\n" +
							"                obj_fixedHead.style.top = (document.documentElement.scrollTop || document.body.scrollTop) + \"px\";\n" +
							"            }\n" +
							"        } else if (pre_scrollLeft != (document.documentElement.scrollLeft || document.body.scrollLeft)) {\n" +
							"            //滚动了水平滚动条\n" +
							"            pre_scrollLeft = (document.documentElement.scrollLeft || document.body.scrollLeft);\n" +
							"        }\n" +
							"    };\n" +
							"</script>";
					//table1.after(jsWjd);
					table1.after("<style type=\"text/css\">" +
							"body div:nth-child(1) {\n" +
							"    position: sticky !important;\n" +
							"    top: 0px;\n" +
							"    z-index: 2;\n" +
							"}" +
							"#fixedHead{" +
							"	top:35px !important;" +
							"}" +
							"@media (max-width: 906px) {\n" +
							"	body div:nth-child(1){" +
							"		position: relative !important;\n" +
							"	}" +
							"	#fixedHead{" +
							"		top:0px !important;" +
							"	}" +
							"}" +
							"</style>");
					context.put("content", parse.html());
				}
				context.put("style", htmlReport.getStyle());
				context.put("reportAlign", htmlReport.getReportAlign());				
				context.put("totalPage", htmlReport.getTotalPage()); 
				context.put("totalPageWithCol", htmlReport.getTotalPageWithCol()); 
				context.put("pageIndex", htmlReport.getPageIndex());
				context.put("chartDatas", convertJson(htmlReport.getChartDatas()));
				context.put("error", false);
				context.put("file", req.getParameter("_u"));
				context.put("intervalRefreshValue",htmlReport.getHtmlIntervalRefreshValue());
				String customParameters=buildCustomParameters(req);
				context.put("customParameters", customParameters);
				context.put("_t", "");
				log.info("context:{}",context);
				Tools tools=null;
				if(MobileUtils.isMobile(req)){
					tools=new Tools(false);
					tools.setShow(false);
				}else{
					String toolsInfo=req.getParameter("_t");
					if(StringUtils.isNotBlank(toolsInfo)){
						tools=new Tools(false);
						if(toolsInfo.equals("0")){
							tools.setShow(false);
						}else{
							String[] infos=toolsInfo.split(",");
							for(String name:infos){
								tools.doInit(name);
							}						
						}
						context.put("_t", toolsInfo);
						context.put("hasTools", true);
					}else{
						tools=new Tools(true);
					}
				}
				context.put("tools", tools);
			}
			context.put("contextPath", req.getContextPath());
			resp.setContentType("text/html");
			resp.setCharacterEncoding("utf-8");
			Template template=ve.getTemplate("ureport-html/html-preview.html","utf-8");
			PrintWriter writer=resp.getWriter();
			template.merge(context, writer);
			writer.close();
		}
	}
	
	private String buildTitle(HttpServletRequest req){
		String title=req.getParameter("_title");
		if(StringUtils.isBlank(title)){
			title=req.getParameter("_u");
			title=decode(title);
			int point=title.lastIndexOf(".ureport.xml");
			if(point>-1){
				title=title.substring(0,point);
			}
			if(title.equals("p")){
				title="设计中报表";
			}
		}else{
			title=decode(title);
		}
		return title+"-ureport";
	}
	
	private String convertJson(Collection<ChartData> data){
		if(data==null || data.size()==0){
			return "";
		}
		ObjectMapper mapper=new ObjectMapper();
		try {
			String json = mapper.writeValueAsString(data);
			return json;
		} catch (Exception e) {
			throw new ReportComputeException(e);
		}
	}
	
	public void loadData(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		HtmlReport htmlReport=loadReport(req);
		writeObjectToJson(resp, htmlReport);
	}

	public void loadPrintPages(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String file=req.getParameter("_u");
		file=decode(file);
		if(StringUtils.isBlank(file)){
			throw new ReportComputeException("Report file can not be null.");
		}
		Map<String, Object> parameters = buildParameters(req);
		ReportDefinition reportDefinition=null;
		if(file.equals(PREVIEW_KEY)){
			reportDefinition=(ReportDefinition)TempObjectCache.getObject(PREVIEW_KEY);
			if(reportDefinition==null){
				throw new ReportDesignException("Report data has expired,can not do export excel.");
			}
		}else{
			reportDefinition=reportRender.getReportDefinition(file);
		}
		Report report=reportBuilder.buildReport(reportDefinition, parameters);	
		Map<String, ChartData> chartMap=report.getContext().getChartDataMap();
		if(chartMap.size()>0){
			CacheUtils.storeChartDataMap(chartMap);				
		}
		FullPageData pageData=PageBuilder.buildFullPageData(report);
		StringBuilder sb=new StringBuilder();
		List<List<Page>> list=pageData.getPageList();
		Context context=report.getContext();
		if(list.size()>0){
			for(int i=0;i<list.size();i++){
				List<Page> columnPages=list.get(i);
				if(i==0){
					String html=htmlProducer.produce(context,columnPages,pageData.getColumnMargin(),false);
					sb.append(html);											
				}else{
					String html=htmlProducer.produce(context,columnPages,pageData.getColumnMargin(),false);
					sb.append(html);											
				}
			}
		}else{
			List<Page> pages=report.getPages();
			for(int i=0;i<pages.size();i++){
				Page page=pages.get(i);
				if(i==0){
					String html=htmlProducer.produce(context,page, false);
					sb.append(html);
				}else{
					String html=htmlProducer.produce(context,page, true);
					sb.append(html);
				}
			}
		}
		Map<String,String> map=new HashMap<String,String>();
		map.put("html", sb.toString());
		writeObjectToJson(resp, map);
	}
	
	public void loadPagePaper(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String file=req.getParameter("_u");
		file=decode(file);
		if(StringUtils.isBlank(file)){
			throw new ReportComputeException("Report file can not be null.");
		}
		ReportDefinition report=null;
		if(file.equals(PREVIEW_KEY)){
			report=(ReportDefinition)TempObjectCache.getObject(PREVIEW_KEY);	
			if(report==null){
				throw new ReportDesignException("Report data has expired.");
			}
		}else{
			report=reportRender.getReportDefinition(file);
		}
		Paper paper=report.getPaper();
		writeObjectToJson(resp, paper);
	}
	
	private HtmlReport loadReport(HttpServletRequest req) {
		Map<String, Object> parameters = buildParameters(req);
		HtmlReport htmlReport=null;
		String file=req.getParameter("_u");
		file=decode(file);
		String pageIndex=req.getParameter("_i");
		if(StringUtils.isBlank(file)){
			throw new ReportComputeException("Report file can not be null.");
		}
		if(file.equals(PREVIEW_KEY)){
			ReportDefinition reportDefinition=(ReportDefinition)TempObjectCache.getObject(PREVIEW_KEY);
			if(reportDefinition==null){
				throw new ReportDesignException("Report data has expired,can not do preview.");
			}
			Report report=reportBuilder.buildReport(reportDefinition, parameters);
			Map<String, ChartData> chartMap=report.getContext().getChartDataMap();
			if(chartMap.size()>0){
				CacheUtils.storeChartDataMap(chartMap);				
			}
			htmlReport=new HtmlReport();
			String html=null;
			if(StringUtils.isNotBlank(pageIndex) && !pageIndex.equals("0")){
				Context context=report.getContext();
				int index=Integer.valueOf(pageIndex);
				SinglePageData pageData=PageBuilder.buildSinglePageData(index, report);
				List<Page> pages=pageData.getPages();
				if(pages.size()==1){
					Page page=pages.get(0);
					html=htmlProducer.produce(context,page,false);					
				}else{
					html=htmlProducer.produce(context,pages,pageData.getColumnMargin(),false);					
				}
				htmlReport.setTotalPage(pageData.getTotalPages());
				htmlReport.setPageIndex(index);
			}else{
				html=htmlProducer.produce(report);				
			}
			if(report.getPaper().isColumnEnabled()){
				htmlReport.setColumn(report.getPaper().getColumnCount());				
			}
			htmlReport.setChartDatas(report.getContext().getChartDataMap().values());			
			htmlReport.setContent(html);
			htmlReport.setTotalPage(report.getPages().size());
			htmlReport.setStyle(reportDefinition.getStyle());
			htmlReport.setSearchFormData(reportDefinition.buildSearchFormData(report.getContext().getDatasetMap(),parameters));
			htmlReport.setReportAlign(report.getPaper().getHtmlReportAlign().name());
			htmlReport.setHtmlIntervalRefreshValue(report.getPaper().getHtmlIntervalRefreshValue());
		}else{
			if(StringUtils.isNotBlank(pageIndex) && !pageIndex.equals("0")){
				int index=Integer.valueOf(pageIndex);
				htmlReport=exportManager.exportHtml(file,req.getContextPath(),parameters,index);								
			}else{
				htmlReport=exportManager.exportHtml(file,req.getContextPath(),parameters);				
			}
		}
		return htmlReport;
	}
	
	
	private String buildCustomParameters(HttpServletRequest req){
		StringBuilder sb=new StringBuilder();
		Enumeration<?> enumeration=req.getParameterNames();
		while(enumeration.hasMoreElements()){
			Object obj=enumeration.nextElement();
			if(obj==null){
				continue;
			}
			String name=obj.toString();
			String value=req.getParameter(name);
			if(name==null || value==null || (name.startsWith("_") && !name.equals("_n"))){
				continue;
			}
			if(sb.length()>0){
				sb.append("&");
			}
			sb.append(name);
			sb.append("=");
			sb.append(value);
		}
		return sb.toString();
	}
	
	private String buildExceptionMessage(Throwable throwable){
		Throwable root=buildRootException(throwable);
		StringWriter sw=new StringWriter();
		PrintWriter pw=new PrintWriter(sw);
		root.printStackTrace(pw);
		String trace=sw.getBuffer().toString();
		trace=trace.replaceAll("\n", "<br>");
		pw.close();
		return trace;
	}
	
	public void setExportManager(ExportManager exportManager) {
		this.exportManager = exportManager;
	}
	
	public void setReportBuilder(ReportBuilder reportBuilder) {
		this.reportBuilder = reportBuilder;
	}
	public void setReportRender(ReportRender reportRender) {
		this.reportRender = reportRender;
	}

	@Override
	public String url() {
		return "/preview";
	}
}
