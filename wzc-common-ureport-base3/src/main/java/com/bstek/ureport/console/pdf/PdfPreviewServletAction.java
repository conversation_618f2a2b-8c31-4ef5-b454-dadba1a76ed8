package com.bstek.ureport.console.pdf;

import cn.hutool.core.convert.Convert;
import com.bstek.ureport.console.RenderPageServletAction;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Base64Utils;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

public class PdfPreviewServletAction extends RenderPageServletAction {


    private final Logger log = LoggerFactory.getLogger(PdfPreviewServletAction.class);

    @Override
    public void execute(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        String method = retriveMethod(req);
        if (method != null) {
            invokeMethod(method, req, resp);
        } else {
            VelocityContext context = new VelocityContext();
            context.put("pdfUrl", decodeUrl(req.getParameter("url")));
            context.put("baseUrl", Convert.toStr(getBaseUrl(req), ""));
            log.info("预览类型={}", req.getParameter("type"));
            context.put("contextPath", "ureport");
            context.put("pdfPresentationModeDisable", true);
            context.put("pdfPrintDisable", true);
            context.put("pdfDownloadDisable", true);
            context.put("pdfPrintDisable", true);
            context.put("pdfBookmarkDisable", true);
            context.put("pdfDisableEditing", true);
            context.put("pdfOpenFileDisable", true);
            resp.setContentType("text/html");
            resp.setCharacterEncoding("utf-8");
            Template template = ve.getTemplate("ureport-html/pdf.html", "utf-8");
            PrintWriter writer = resp.getWriter();
            template.merge(context, writer);
            writer.close();
        }
    }

    private String getBaseUrl(HttpServletRequest req) {
        String baseUrl;
        //1、支持通过 http header 中 X-Base-Url 来动态设置 baseUrl 以支持多个域名/项目的共享使用
        final String urlInHeader = req.getHeader("X-Base-Url");
        if (StringUtils.isNotEmpty(urlInHeader)) {
            baseUrl = urlInHeader;
        } else {
            if ("localhost".equals(req.getServerName())) {
                //3、默认动态拼接 baseUrl
                baseUrl = req.getScheme() + "://" + req.getServerName() + ":" + req.getServerPort()
                        + req.getContextPath() + "/";
            } else {
                //3、默认动态拼接 baseUrl
                baseUrl = req.getScheme() + "://" + req.getServerName()
                        + req.getContextPath() + "/";
            }
        }
        if (!baseUrl.endsWith("/")) {
            baseUrl = baseUrl.concat("/");
        }
        log.info("baseurl = {}", baseUrl);
        return baseUrl;
    }

    public void getCorsFile(HttpServletRequest req, HttpServletResponse response) {
        String urlPath = URLDecoder.decode(decodeUrl(req.getParameter("urlPath")), StandardCharsets.UTF_8);
        HttpURLConnection urlcon = null;
        InputStream inputStream = null;
        assert urlPath != null;
        if (!urlPath.toLowerCase().startsWith("http") && !urlPath.toLowerCase().startsWith("https") && !urlPath.toLowerCase().startsWith("ftp")) {
            return;
        }
        try {
            URL url = new URL(urlPath);
            urlcon = (HttpURLConnection) url.openConnection();
            urlcon.setConnectTimeout(30000);
            urlcon.setReadTimeout(30000);
            urlcon.setInstanceFollowRedirects(false);
            int responseCode = urlcon.getResponseCode();
            if (responseCode == 403 || responseCode == 500) { //403  500
                log.error("读取跨域文件异常，url：{}，错误：{}", urlPath,responseCode);
                return;
            }
            if (responseCode == HttpURLConnection.HTTP_MOVED_PERM || responseCode == HttpURLConnection.HTTP_MOVED_TEMP) { //301 302
                url = new URL(urlcon.getHeaderField("Location"));
                urlcon = (HttpURLConnection) url.openConnection();
            }
            inputStream = (url).openStream();
            IOUtils.copy(inputStream, response.getOutputStream());
        } catch (Exception exception) {
            log.error("读取跨域文件异常，url：{}", urlPath);
        } finally {
            assert urlcon != null;
            urlcon.disconnect();
            IOUtils.closeQuietly(inputStream);
        }
    }

    public static String decodeUrl(String urlStr)  {
        return new String(Base64Utils.decodeFromString(urlStr.replaceAll(" ", "+").replaceAll("\n", "")), StandardCharsets.UTF_8);
    }

    public void pdfViewer(HttpServletRequest req, HttpServletResponse resp) throws IOException {
        VelocityContext context = new VelocityContext();
        resp.setContentType("text/html");
        resp.setCharacterEncoding("utf-8");
        context.put("contextPath", req.getContextPath());
        Template template = ve.getTemplate("ureport-asserts/pdfjs/web/viewer.html", "utf-8");
        PrintWriter writer = resp.getWriter();
        template.merge(context, writer);
        writer.close();
    }


    @Override
    public String url() {
        return "/onlinePreview";
    }
}
