package com.wzc.be.log.embedding.core.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.inner.AppConfig;
import com.wzc.be.log.embedding.core.convert.EmbeddingLogConvert;
import com.wzc.be.log.embedding.core.model.CompositeEmbeddingLog;
import com.wzc.common.string.StringPool;
import com.wzc.common.user.base.BaseUser;
import com.wzc.common.user.utils.UserUtils;
import com.wzc.common.util.web.IpUtil;
import io.opentelemetry.api.trace.Span;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.UUID;

@Slf4j
public class EmbeddingLog {

    private static final EmbeddingLog instance = new EmbeddingLog();

    private EmbeddingLog() {}

    public static EmbeddingLog getInstance() {
        return instance;
    }
    /**
     * 基础对象埋点
     *
     * @param compositeEmbeddingLog
     */
    private void info(CompositeEmbeddingLog compositeEmbeddingLog) {
        packingLog(compositeEmbeddingLog, null, null);
        log.info(JSONUtil.toJsonStr(compositeEmbeddingLog));
    }

    /**
     * 打印对象埋点
     *
     * @param content
     */
    public void info(String content, Integer type, Integer requestType) {
        CompositeEmbeddingLog compositeEmbeddingLog = new CompositeEmbeddingLog();
        compositeEmbeddingLog.setContent(content);
        packingLog(compositeEmbeddingLog, type, requestType);
        log.info(JSONUtil.toJsonStr(compositeEmbeddingLog));
    }

    /**
     * 基础对象埋点
     *
     * @param map
     */
    public void infoLog(Map<String, Object> map) {
        info(EmbeddingLogConvert.compositeEmbeddingLog(map));
    }


    public void infoContent(Map<String, Object> map, Integer type, Integer requestType) {
        info(EmbeddingLogConvert.compositeMessageContent(map), type, requestType);
    }

    private void packingLog(CompositeEmbeddingLog compositeEmbeddingLog, Integer type, Integer requestType) {
        BaseUser user = UserUtils.getUser(false);
        compositeEmbeddingLog.setUserId(user.getUserId());
        compositeEmbeddingLog.setCompanyId(user.getCompanyId());
        compositeEmbeddingLog.setCreateTime(DateUtil.date().getTime());
        compositeEmbeddingLog.setRealIp(IpUtil.getIP());
        compositeEmbeddingLog.setServiceName(AppConfig.getAppName().orElse(StringPool.EMPTY));
        compositeEmbeddingLog.setUserIdentity(user.getUserIdentity());
        compositeEmbeddingLog.setCompanyIdentity(user.getCompanyIdentity());
        if (ObjUtil.isNotNull(type)) {
            compositeEmbeddingLog.setTypeEnums(type);
        }
        if (ObjUtil.isNotNull(requestType)) {
            compositeEmbeddingLog.setRequestTypeEnums(requestType);
        }
        compositeEmbeddingLog.setTid(Span.current().getSpanContext().getTraceId());
        compositeEmbeddingLog.setLogId(UUID.randomUUID().toString().replace(StringPool.DASH, StringPool.EMPTY));
    }
}
