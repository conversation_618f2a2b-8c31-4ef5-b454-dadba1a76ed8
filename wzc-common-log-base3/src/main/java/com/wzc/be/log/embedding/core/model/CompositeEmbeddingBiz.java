package com.wzc.be.log.embedding.core.model;

import com.wzc.be.log.embedding.sms.enums.BusinessSceneSmsEnums;
import com.wzc.be.log.embedding.sms.enums.BusinessTypeSmsEnums;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CompositeEmbeddingBiz {

    /**
     * 业务场景
     */
    private BusinessSceneSmsEnums businessScene;
    /**
     * 业务类型
     */
    private BusinessTypeSmsEnums businessType;
    /**
     * 业务编号
     */
    private String businessNo;
}
