package com.wzc.be.log.embedding.sms.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessSceneSmsEnums implements IEnum {

    SET_PAYMENT_PASSWORD(1,"设置支付密码获取短信验证码"),
    IMPORT_SETTLEMENT(2,"导入结算单消息推送"),
    FENCE_WARNING(3,"围栏预警"),
    MODIFY_MOBILE(4,"修改手机号发送验证码"),
    RELATED_ENTERPRISES(5,"企业关联接收消息"),
    VEHICLE_AUTH(6,"车辆认证-审核"),
    CARRIER_AUTH(7,"承运商-审核"),
    DRIVER_AUTH(8,"司机认证-审核"),
    CREW_AUTH(9,"船员认证-审核"),
    SHIPPER_AUTH(10,"货主认证-审核"),
    ZERO_TON_DELIVERY(11,"零吨签收提醒司机"),
    EXTERNAL_INTERFACE_CALL(12,"外部接口调用"),
    CARRIER_SUCCESS_GRABBING_ORDERS(13,"承运商抢单成功通知"),
    DRIVER_SUCCESS_GRABBING_ORDERS(14,"司机抢单成功通知"),
    CARRIER_GRABBING_ORDERS_NOTICE(15,"承运商抢单通知(上/下架公告)"),
    DRIVER_GRABBING_ORDERS_NOTICE(16,"司机抢单通知(上/下架公告)"),
    APPROVAL_NOTICE(17,"审批通知"),
    LOGIN_VERIFICATION_CODE(18,"登录验证码"),
    RESET_PASSWORD(19,"重置密码"),
    ;

    private final Integer code;
    private final String name;

}
