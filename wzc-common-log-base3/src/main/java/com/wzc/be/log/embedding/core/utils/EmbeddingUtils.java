package com.wzc.be.log.embedding.core.utils;

import cn.hutool.json.JSONUtil;
import com.wzc.be.log.embedding.core.model.CompositeEmbeddingLog;
import com.wzc.be.log.embedding.core.model.CompositeFilebeatMessage;
import lombok.experimental.UtilityClass;

@UtilityClass
public class EmbeddingUtils {


    public CompositeEmbeddingLog getMessage(String message){
        CompositeFilebeatMessage compositeFilebeatMessage = JSONUtil.toBean(message, CompositeFilebeatMessage.class);
        return null;
    }
}
