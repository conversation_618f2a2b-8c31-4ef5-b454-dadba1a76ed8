package com.wzc.be.log.embedding.core.model;

import lombok.Data;
@Data
public class CompositeEmbeddingLog {

    /**
     * 用户真实IP
     */
    private String realIp;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 用户身份
     */
    private Integer userIdentity;

    /**
     * 公司身份
     */
    private Integer companyIdentity;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 日志类型
     */
    private Integer typeEnums;

    /**
     * 日志子类
     */
    private Integer requestTypeEnums;

    /**
     * 日志体
     */
    private String content;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 链路ID
     */
    private String tid;

}
