package com.wzc.be.log.embedding.core.convert;

import cn.hutool.json.JSONUtil;
import com.wzc.be.log.embedding.core.model.CompositeEmbeddingLog;
import lombok.experimental.UtilityClass;

import java.util.Map;

@UtilityClass
public class EmbeddingLogConvert {

    /**
     * Map转换log
     *
     * @param map
     * @return
     */
    public CompositeEmbeddingLog compositeEmbeddingLog(Map<String, Object> map) {
        return JSONUtil.toBean(JSONUtil.toJsonStr(map), CompositeEmbeddingLog.class);
    }

    public static String compositeMessageContent(Map<String, Object> map) {
        return JSONUtil.toJsonStr(compositeEmbeddingLog(map));
    }
}
