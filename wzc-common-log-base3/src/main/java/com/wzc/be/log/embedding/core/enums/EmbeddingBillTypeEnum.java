package com.wzc.be.log.embedding.core.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2024/6/20
 */
@Getter
@AllArgsConstructor
public enum EmbeddingBillTypeEnum {

    /**
     * 报警类型 1-订单 2-运输计划 3-子运单
     */

    ORDER(1,"订单"),
    TRANSPORT(2,"运输计划"),
    SUB_WAYBILL(3,"子运单");

    private final Integer code;
    private final String name;
}
