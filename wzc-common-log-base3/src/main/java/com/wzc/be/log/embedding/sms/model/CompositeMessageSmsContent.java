package com.wzc.be.log.embedding.sms.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CompositeMessageSmsContent {

    private String messageId;

    /**
     * 业务场景
     */
    private Integer businessScene;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 发送状态
     */
    private Integer sendStatus;

    /**
     * 发送时间
     */
    private Long sendTime;

    /**
     * 接收状态
     */
    private Integer receiveStatus;

    /**
     * 接收时间
     */
    private Long receiveTime;

    /**
     * 接收人用户身份
     */
    private Integer userIdentity;

    /**
     * 接收人公司ID
     */
    private Long companyId;
}
