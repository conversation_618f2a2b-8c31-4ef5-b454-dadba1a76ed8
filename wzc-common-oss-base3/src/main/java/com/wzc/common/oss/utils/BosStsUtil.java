package com.wzc.common.oss.utils;

import com.wzc.common.oss.BaiduBosClient;
import com.wzc.common.oss.config.BosProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import static java.util.Objects.isNull;

/**
 * 百度bos sts工具类
 *
 * <AUTHOR>
 * @date 2023年06月26日
 */
@Slf4j
public class BosStsUtil {

    @Autowired
    private BaiduBosClient baiduBosClient;

    @Autowired
    private BosProperties bosProperties;

    /**
     * 获取bos资源临时访问路径(默认有效时间1800秒)
     *
     * @param fileKey bos文件key
     * @return 临时访问地址完整路径
     */
    public String generatePreSignUrl(String fileKey) {
        return generatePreSignUrl(fileKey, 1800);
    }

    /**
     * 获取bos资源临时访问路径
     *
     * @param fileKey             bos文件key
     * @param expirationInSeconds 有效期(秒)
     * @return 临时访问地址完整路径
     */
    public String generatePreSignUrl(String fileKey, Integer expirationInSeconds) {
        if (StringUtils.isBlank(fileKey)) {
            log.warn("fileKey为空");
            return null;
        }
        // 兼容二期数据
        if (fileKey.contains("http")) {
            return fileKey;
        }
        if (isNull(expirationInSeconds)) {
            log.error("expirationInSeconds参数异常:{}", expirationInSeconds);
            expirationInSeconds = 1800;
        }
        return baiduBosClient.getPresignedUrl(bosProperties.getBosPrivateBucket(), fileKey, expirationInSeconds);
    }

}
