package com.wzc.common.oss.annotation;

import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wzc.common.oss.deserialize.BosUrlGetFileKeyDeserializer;
import com.wzc.common.oss.serialize.BosPreSignedUrlSerializer;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = BosPreSignedUrlSerializer.class)
@JsonDeserialize(using = BosUrlGetFileKeyDeserializer.class)
public @interface BosPreSignedUrl {

    /**
     * url有效时间(秒)
     * 默认1800秒
     */
    int expirationInSeconds() default 1800;

    /**
     * 图片处理参数
     */
    String process() default "";

}
