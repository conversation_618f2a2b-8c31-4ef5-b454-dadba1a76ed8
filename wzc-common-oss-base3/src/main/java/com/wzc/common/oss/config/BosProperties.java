package com.wzc.common.oss.config;

import com.baidubce.Protocol;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;


/**
 * baidu bos 配置
 */
@Getter
public class BosProperties {

    /**
     * bos接入区域
     */
    @Value("${baidu.bos.endpoint}")
    private String endpoint;

    /**
     * 设置http或者https
     */
    @Value("${baidu.bos.protocol}")
    private Protocol protocol = Protocol.HTTPS;

    /**
     * sts切入区域
     */
    @Value("${baidu.bos.sts.endpoint}")
    private String stsEndpoint;

    /**
     * bos ACCESS_KEY_ID
     */
    @Value("${baidu.bos.accessKeyId}")
    private String accessKeyId;

    /**
     * bos SECRET_ACCESS_KEY
     */
    @Value("${baidu.bos.secretAccessKey}")
    private String secretAccessKey;

    /**
     * bos文件请求主域名
     */
    @Value("${baidu.bos.host}")
    private String host;

    /**
     * 私有bucket
     */
    @Value("${baidu.bos.private.bucket}")
    private String bosPrivateBucket;

    /**
     * 公有
     */
    @Value("${baidu.bos.public.bucket}")
    private String bosPublicBucket;

    /**
     * 上传临时目录(下载文件)
     */
    @Value("${baidu.bos.temp.dir:/download}")
    private String bosTempDir;

    /**
     * 上传临时目录
     */
    @Value("${baidu.bos.cleanable.dir:/tmp}")
    private String bosCleanableDir;

    /**
     * 归档日志目录
     */
    @Value("${baidu.bos.archive.log.dir:/log/archive}")
    private String bosArchiveLogDir;

    /**
     * baidu bos 时间目录日期格式
     */
    @Value("${baidu.bos.picture.dir.data-format:yyyyMMdd}")
    private String bosPictureDateDirFormat;

}
