package com.wzc.be.common.swagger.annotation;

import com.wzc.be.common.swagger.config.SwaggerAutoConfiguration;
import com.wzc.be.common.swagger.support.SwaggerProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;

import java.lang.annotation.*;

/**
 *  暂时不可用
 * <AUTHOR>
 */
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableConfigurationProperties(SwaggerProperties.class)
@Import({ SwaggerAutoConfiguration.class })
public @interface EnableSpringDoc {

}
