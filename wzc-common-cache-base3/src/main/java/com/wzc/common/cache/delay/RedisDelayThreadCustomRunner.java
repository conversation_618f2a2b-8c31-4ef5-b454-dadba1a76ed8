package com.wzc.common.cache.delay;

import cn.hutool.core.util.StrUtil;
import com.wzc.common.cache.utils.RedisDelayQueue;
import com.wzc.common.cache.utils.RedisKeyGenerator;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingDeque;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * 针对 非bean创建，手动动态key的逻辑
 */
@Slf4j
public class RedisDelayThreadCustomRunner {

    public final AtomicBoolean REQUEST_PROCESSOR_START = new AtomicBoolean(true);

    private final String delayKey;
    private final Consumer<String> consumer;

    public RedisDelayThreadCustomRunner(String delayKey,Consumer<String> consumer){
        this.delayKey = delayKey;
        this.consumer = consumer;
    }

    /**
     * 提交异步返回值类型的线程任务
     */
    public Thread mainThread;

    public void run() {
        REQUEST_PROCESSOR_START.set(true);
        mainThread = new Thread(() -> {
            RBlockingDeque<String> blockingDeque = RedisDelayQueue.getDelayQueue(RedisKeyGenerator.gen(delayKey));
            while (REQUEST_PROCESSOR_START.get()) {
                try {
                    consumer.accept(blockingDeque.take());
                } catch (Exception e) {
                    log.error(StrUtil.format("队列延迟异常处理回调异常，请检查 key={}", delayKey), e);
                }
            }
        });
        mainThread.start();
    }

    public void stop() {
        this.REQUEST_PROCESSOR_START.set(false);
        this.mainThread.interrupt();
    }
}
