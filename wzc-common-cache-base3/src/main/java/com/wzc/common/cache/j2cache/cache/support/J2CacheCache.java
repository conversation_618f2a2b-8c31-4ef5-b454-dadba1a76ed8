package com.wzc.common.cache.j2cache.cache.support;

import cn.hutool.core.util.StrUtil;
import lombok.NonNull;
import net.oschina.j2cache.CacheChannel;
import net.oschina.j2cache.CacheObject;
import net.oschina.j2cache.NullObject;
import org.springframework.cache.CacheManager;
import org.springframework.cache.support.AbstractValueAdaptingCache;
import org.springframework.cache.support.NullValue;
import org.springframework.lang.Nullable;

import java.util.concurrent.Callable;

/**
 * {@link CacheManager} implementation for J2Cache.
 * <AUTHOR>
 *
 */
public class J2CacheCache extends AbstractValueAdaptingCache {

	private final CacheChannel cacheChannel;

	private final String j2CacheName;

	public J2CacheCache(String cacheName, CacheChannel cacheChannel) {
		this(cacheName,cacheChannel, true);
	}

	public J2CacheCache(String cacheName, CacheChannel cacheChannel, boolean allowNullValues) {
		super(allowNullValues);
		j2CacheName = cacheName;
		this.cacheChannel = cacheChannel;
	}

	@Override
	public String getName() {
		return j2CacheName;
	}

	@Override
	public Object getNativeCache() {
		return this.cacheChannel;
	}

	@Override
	public <T> T get(@NonNull Object key, @Nullable Callable<T> valueLoader) {
		T value;
		try {
			value = valueLoader.call();
		} catch (Exception ex) {
			throw new ValueRetrievalException(key, valueLoader, ex);
		}
		put(key, value);
		return value;
	}

	@Override
	public void put(@NonNull Object key, Object value) {
		cacheChannel.set(j2CacheName, String.valueOf(key), value, super.isAllowNullValues());
	}

	@Override
	public ValueWrapper putIfAbsent(@NonNull Object key, Object value) {
		if (!cacheChannel.exists(j2CacheName, StrUtil.toString(key))) {
			cacheChannel.set(j2CacheName, String.valueOf(key), value);
		}
		return get(key);
	}

	@Override
	public void evict(@NonNull Object key) {
		cacheChannel.evict(j2CacheName, String.valueOf(key));
	}

	@Override
	public void clear() {
		cacheChannel.clear(j2CacheName);
	}

	@Override
	protected Object lookup(@NonNull Object key) {
		CacheObject cacheObject = cacheChannel.get(j2CacheName, String.valueOf(key));
		if(cacheObject.rawValue() != null && cacheObject.rawValue().getClass().equals(NullObject.class) && super.isAllowNullValues()) {
			return NullValue.INSTANCE;
		}
		return cacheObject.getValue();
	}

}
