package com.wzc.common.cache;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.wzc.common.cache.lock.RedisLockAspect;
import com.wzc.common.cache.lock.RedisLockClient;
import com.wzc.common.cache.lock.RedisLockClientImpl;
import com.wzc.common.cache.proper.CustomRedisProperties;
import com.wzc.common.cache.serializer.RedisSerializerConfigAble;
import com.wzc.common.cache.utils.RedisKeyIncr;
import lombok.AllArgsConstructor;
import org.redisson.api.RedissonClient;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(CustomRedisProperties.class)
@AllArgsConstructor
@PropertySource(value = "classpath:/redis.properties")
public class RedisAutoConfig{


    @Bean("customRedisLogAspect")
    public RedisLockAspect redisLockAspect(RedisLockClient redisLockClient) {
        return new RedisLockAspect(redisLockClient);
    }


    @Bean
    public RedisLockClient redisLockClient(RedissonClient redissonClient, StringRedisTemplate redisTemplate) {
        return new RedisLockClientImpl(redissonClient,redisTemplate);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory,RedisSerializer<Object> redisSerializer) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setKeySerializer(RedisSerializer.string());
        redisTemplate.setHashKeySerializer(RedisSerializer.string());
        redisTemplate.setValueSerializer(redisSerializer);
        redisTemplate.setHashValueSerializer(RedisSerializer.json());
        redisTemplate.setConnectionFactory(factory);
        return redisTemplate;
    }

    @Bean
    public RedisKeyIncr redisKeyIncr(StringRedisTemplate redisTemplate) {
        return new RedisKeyIncr(redisTemplate);
    }

    @Bean
    public RedisSerializer<?> redisSerializer(CustomRedisProperties properties, ObjectMapper objectMapper) {
        return new RedisSerializerConfigAble().defaultRedisSerializer(properties, objectMapper);
    }
}
