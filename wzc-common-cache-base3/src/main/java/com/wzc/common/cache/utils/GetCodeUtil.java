package com.wzc.common.cache.utils;

import com.wzc.common.util.SpringUtil;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 编号生成器
 * <AUTHOR>
 * @date 2020/7/24 - 11:43
 */
@UtilityClass
@Slf4j
public class GetCodeUtil {

    private static RedisKeyIncr redisKeyIncr;

    private static RedisKeyIncr getRedisKeyIncr() {
        if (redisKeyIncr == null) {
            redisKeyIncr = SpringUtil.getBean(RedisKeyIncr.class);
        }
        return redisKeyIncr;
    }

    /**
     * 根据日期相关格式化生成code，长度不够自动补0，隔天不清理
     *
     * @param prefix        code前缀
     * @param dateStrFormat 日期格式化
     * @param num           长度 拼接在日期后面的长度
     * @return
     */
    public String nextCode(String prefix, String dateStrFormat, Integer num) {
        return getRedisKeyIncr().nextCode(prefix, dateStrFormat, num);
    }

    /**
     * 以日期为准，每天从0开始
     *
     * @param key 业务名称
     * @param str 拼接字符串
     * @param num 长度(整体返回的长度，限制返回长度，如果长度过长，会自动清理时间戳长度)
     * @return
     */
    public String getDateCodeExpire(String key, String str, int num) {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmssS");
        String timeStr = df.format(new Date());
        StringBuilder sb = new StringBuilder();
        StringBuilder zeor = new StringBuilder();
        String orderCodeSuffix = getRedisKeyIncr().getDateKeyExpire(key);
        append(zeor, num, str.length());
        sb.append(str).append(timeStr).append(zeor).append(orderCodeSuffix);
        return sb.toString();
    }

    private void append(StringBuilder stringBuilder, int num, int keyLength) {
        int k = num - keyLength - 1;
        if (k < 0) {
            log.warn("生成code配置错误，指定长度需要大于生产序列长度+前缀长度，本次生成长度不限制");
            k = 0;
        }
        for (int i = 0; i < k; i++) {
            stringBuilder.append("0");
        }
    }

    /**
     * 自动生成code 自动拼装str 前缀
     *
     * @param key 业务名称
     * @param str 标识
     * @param num 位数
     * @return
     */
    public String getCode(String key, String str, int num) {
        //假设从redis中取出用户的序号
        String code = getRedisKeyIncr().getCodeKey(key);
        StringBuilder stringBuilder = new StringBuilder(str);
        append(stringBuilder, num, str.length());
        stringBuilder.append(code);
        return stringBuilder.toString();
    }
}
