package com.wzc.common.cache.lock;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import com.wzc.common.cache.enums.RedisResultCode;
import com.wzc.common.cache.utils.RedisKeyGenerator;
import com.wzc.common.exception.ServiceException;
import com.wzc.common.util.spel.ExpressionEvaluator;
import jodd.util.StringPool;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.core.annotation.Order;
import org.springframework.expression.EvaluationContext;
import org.springframework.util.Assert;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

import static com.wzc.common.cache.constant.RedisConstant.CACHE_LOCK_REPLAY_PREFIX;

/**
 * redis 分布式锁
 */
@Order(-1)
@Aspect
@RequiredArgsConstructor
public class RedisLockAspect implements ApplicationContextAware {

	/**
	 * 表达式处理
	 */
	private static final ExpressionEvaluator EVALUATOR = new ExpressionEvaluator();
	/**
	 * redis 限流服务
	 */
	private final RedisLockClient redisLockClient;
	private ApplicationContext applicationContext;

	/**
	 * AOP 环切 注解 @RedisLock
	 */
	@Around("@annotation(redisLock)")
	public Object aroundRedisLock(ProceedingJoinPoint point, RedisLock redisLock) {
		String lockName = redisLock.value();
		Assert.hasText(lockName, "@RedisLock value must have length; it must not be null or empty");
		lockName = getKey(lockName);
		// el 表达式
		String lockParam = redisLock.param();
		// 表达式不为空
		String lockKey;
		if (StrUtil.isNotBlank(lockParam)) {
			String evalAsText = evalLockParam(point, lockParam);
			//修改对比值，避免参数过长导致redis问题
			lockKey = lockName + CharPool.COLON + DigestUtil.sha1Hex(evalAsText);
			//如果是redis - list锁，就走getMultiLock
			if(redisLock.type().equals(LockType.LIST_ROCK)) {
				lockKey = lockName + CharPool.COLON + evalAsText;
			}
		} else {
			lockKey = lockName;
		}
		LockType lockType = redisLock.type();
		long waitTime = redisLock.waitTime();
		long leaseTime = redisLock.leaseTime();
		TimeUnit timeUnit = redisLock.timeUnit();
		return redisLockClient.lock(lockKey, lockType, waitTime, leaseTime, timeUnit, point::proceed, redisLock.msg());
	}

	private String getKey(String lockName){
		String key = String.join(StringPool.COLON, CACHE_LOCK_REPLAY_PREFIX, lockName);
		return RedisKeyGenerator.gen(key);
	}

	/**
	 * 计算参数表达式
	 *
	 * @param point     ProceedingJoinPoint
	 * @param lockParam lockParam
	 * @return 结果
	 */
	private String evalLockParam(ProceedingJoinPoint point, String lockParam) {
		MethodSignature ms = (MethodSignature) point.getSignature();
		Method method = ms.getMethod();
		Object[] args = point.getArgs();
		Object target = point.getTarget();
		Class<?> targetClass = target.getClass();
		EvaluationContext context = EVALUATOR.createContext(method, args, target, targetClass, applicationContext);
		AnnotatedElementKey elementKey = new AnnotatedElementKey(method, targetClass);
		return EVALUATOR.evalAsText(lockParam, elementKey, context);
	}

	@Override
	public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
}
