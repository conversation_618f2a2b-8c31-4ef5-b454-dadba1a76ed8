package com.wzc.common.cache.ratelimiter;

import com.baidu.mapcloud.cloudnative.common.model.BizException;
import com.wzc.common.cache.enums.RedisResultCode;
import com.wzc.common.exception.CustomBizErrorWrapper;
import lombok.Getter;

import java.util.concurrent.TimeUnit;

/**
 * 限流异常
 *
 * <AUTHOR>
 */
@Getter
public class RateLimiterException extends BizException {
	private final String key;
	private final long max;
	private final long ttl;
	private final TimeUnit timeUnit;

	public RateLimiterException(String key, long max, long ttl, TimeUnit timeUnit) {
		super(new CustomBizErrorWrapper(RedisResultCode.RATE_LIMIT_ERROR, key, max, timeUnit.toSeconds(ttl)));
		this.key = key;
		this.max = max;
		this.ttl = ttl;
		this.timeUnit = timeUnit;
	}
}
