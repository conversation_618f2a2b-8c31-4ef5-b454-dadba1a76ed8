package com.wzc.common.cache.listener;

import com.wzc.common.cache.utils.RedisKeyGenerator;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 监听器bean池
 *
 * <AUTHOR>
 */
public class RedisKeyExpireProcessorPool {
    private static final Map<String, RedisKeyExpireProcessor> POOL = new HashMap<>(8);

    public static RedisKeyExpireProcessor put(String key, RedisKeyExpireProcessor bean) {
        return POOL.put(key, bean);
    }

    public static Boolean hasKey(String key){
        return POOL.containsKey(key);
    }

    public static List<RedisKeyExpireProcessor> match(String key) {
        return POOL.keySet()
                .stream()
                .filter(k -> key.startsWith(RedisKeyGenerator.gen(k)))
                .map(POOL::get)
                .collect(Collectors.toList());
    }
}
