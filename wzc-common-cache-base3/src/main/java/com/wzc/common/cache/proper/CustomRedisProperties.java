package com.wzc.common.cache.proper;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ConfigurationProperties("wzc.common.redis")
public class CustomRedisProperties {

    /**
     * redis-key顶级前缀
     */
    private String keyPrefix;
    private Boolean listener;
    /**
     * 序列化方式
     */
    private SerializerType serializerType = SerializerType.STRING;

    public enum SerializerType {
        /**
         * json 序列化
         */
        JSON,
        /**
         * jdk 序列化
         */
        JDK,
        /**
         * string
         */
        STRING
    }

}
