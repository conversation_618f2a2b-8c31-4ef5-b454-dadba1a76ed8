package com.wzc.common.cache.lock;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.type.LogicalType;
import com.google.common.collect.Lists;
import com.wzc.common.cache.constant.RedisConstant;
import com.wzc.common.cache.enums.RedisResultCode;
import com.wzc.common.cache.func.CheckedSupplier;
import com.wzc.common.cache.utils.RedisKeyGenerator;
import com.wzc.common.exception.ServiceException;
import jodd.util.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wzc.common.cache.enums.RedisResultCode.FAIL_TRY_LOCK_MESSAGE;

/**
 * 锁客户端
 */
@Slf4j
@RequiredArgsConstructor
public class RedisLockClientImpl implements RedisLockClient {
    private final RedissonClient redissonClient;
    private final StringRedisTemplate redisTemplate;

    @Override
    public boolean tryLock(String lockName, LockType lockType, long waitTime, long leaseTime, TimeUnit timeUnit) throws InterruptedException {
        if(lockType == LockType.CUSTOM) {
            //自定义redis锁
            return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(genKey(lockName), "lock", leaseTime, timeUnit));
        }if(lockType == LockType.LIST_ROCK) {
            List<String> keys = getLockKeys(lockName);
            List<Boolean> list = Lists.newArrayList();
            keys.forEach(key -> list.add(Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, "lock", leaseTime, timeUnit))));
            return list.stream().allMatch(b -> b);
        } else {
            //框架redisson锁
            RLock lock = getLock(lockName, lockType);
            return lock.tryLock(waitTime, leaseTime, timeUnit);
        }
    }

    private List<String> getLockKeys(String lockName){
        String keys = StrUtil.subAfter(lockName, CharPool.COLON, true);
        return StrUtil.split(keys, CharPool.COMMA).stream().map(this::genKey).collect(Collectors.toList());
    }

    @Override
    public void unLock(String lockName, LockType lockType) {
        if(lockType == LockType.CUSTOM) {
            //自定义redis锁
            redisTemplate.delete(genKey(lockName));
        } if(lockType == LockType.LIST_ROCK) {
            redisTemplate.delete(getLockKeys(lockName));
        }else {
            //框架redisson锁
            RLock lock = getLock(lockName, lockType);
            // 仅仅在已经锁定和当前线程持有锁时解锁
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private RLock getLock(String lockName, LockType lockType) {
        RLock lock;
        if (LockType.REENTRANT == lockType) {
            lock = redissonClient.getLock(lockName);
            return lock;
        } else {
            lock = redissonClient.getFairLock(lockName);
        }
        return lock;
    }

    @SneakyThrows
    @Override
    public <T> T lock(String lockName, LockType lockType, long waitTime, long leaseTime, TimeUnit timeUnit, CheckedSupplier<T> supplier, String msg) {
        boolean result = this.tryLock(lockName, lockType, waitTime, leaseTime, timeUnit);
        if (result) {
            //如果取锁成功
            try {
                return supplier.get();
            } finally {
                //不论结果如何释放锁
                this.unLock(lockName, lockType);
            }
        } else {
            //如果取锁失败，统一抛出异常
            throw new ServiceException(FAIL_TRY_LOCK_MESSAGE, msg);
        }
    }

    /**
     * 生成缓存的key
     *
     * @param key key
     * @return String
     */
    private String genKey(String key) {
        key = String.join(StringPool.COLON, RedisConstant.CACHE_LOCK_CUSTOM_PREFIX, key);
        return RedisKeyGenerator.gen(key);
    }
}
