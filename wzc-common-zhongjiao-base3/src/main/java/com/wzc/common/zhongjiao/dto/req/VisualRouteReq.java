package com.wzc.common.zhongjiao.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/4
 * @description : 运输服务查询请求参数
 */
@Data
@Schema(description = "运输规划服务查询请求参数")
public class VisualRouteReq {

    @Schema(name = "车牌号", required = true)
    private String vclN;

    @Schema(name = "车牌颜色(1 蓝色、2 黄色、3 黄绿色)", required = true)
    private String vco;

    @Schema(name = "开始时间  yyyy-MM-dd HH:mm:ss，近6 个月自然月", required = true)
    private String qryBtm;

    @Schema(name = "结束时间  yyyy-MM-dd HH:mm:ss，与开始时间相差 72 小时之内", required = true)
    private String qryEtm;

    @Schema(name = "始发地坐标(WGS84 坐标系) 116.31795,30.4252，始发地坐标和目的地坐标为一组，" +
            "始发地行政区划编码和目的地行政区划编码为一组，都输入的时候以始发地坐标、目的地坐标为准", required = true)
    private String startLonlat;

    @Schema(name = "目的地坐标(WGS84 坐标系) 116.31795,30.4252，始发地坐标和目的地坐标为一组，" +
            "始发地行政区划编码和目的地行政区划编码为一组，都输入的时候以始发地坐标、目的地坐标为准", required = true)
    private String endLonlat;

    @Schema(name = "始发地行政区划编码 431102，始发地坐标和目的地坐标为一组，" +
            "始发地行政区划编码和目的地行政区划编码为一组，都输入的时候以始发地坐标、目的地坐标为准", required = true)
    private String startAreaCode;

    @Schema(name = "目的地行政区划编码 450324，始发地坐标和目的地坐标为一组，" +
            "始发地行政区划编码和目的地行政区划编码为一组，都输入的时候以始发地坐标、目的地坐标为准", required = true)
    private String endAreaCode;
}
