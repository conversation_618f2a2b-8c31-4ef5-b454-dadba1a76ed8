package com.wzc.common.zhongjiao.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 *
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/3
 * @description : 停车列表响应结果
 */
@Data
@Schema(description = "停车列表响应结果")
public class ParkArray {

    @Schema(name = "停车地址")
    private String parkAdr;

    @Schema(name = "停车开始时间")
    private String parkBte;

    @Schema(name = "停车结束时间")
    private String parkEte;

    @Schema(name = "纬度")
    private String parkLat;

    @Schema(name = "经度")
    private String parkLon;

    @Schema(name = "停车时长")
    private String parkMins;
}
