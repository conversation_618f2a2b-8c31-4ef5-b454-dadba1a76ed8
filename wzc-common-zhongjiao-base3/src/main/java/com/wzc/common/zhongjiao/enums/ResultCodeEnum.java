package com.wzc.common.zhongjiao.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/3
 * @description : 中交接口状态码
 */
@Getter
@AllArgsConstructor
public enum ResultCodeEnum {

    /**
     * 中交接口状态码
     */
    S1001(1001,"服务执行成功"),
    S1002(1002,"参数不正确"),
    S1003(1003,"服务调用数量已达上限"),
    S1004(1004,"服务调用次数已达上限"),
    S1005(1005,"该账号未授权指定所属行政区划范围"),
    S1006(1006,"无结果"),
    S1010(1010,"用户名或密码不正确"),
    S1011(1011,"IP 不在白名单列表"),
    S1012(1012,"账号已禁用"),
    S1013(1013,"账号已过有效期"),
    S1014(1014,"无服务权限"),
    S1015(1015,"用户认证系统已升级，请使用令牌访问"),
    S1016(1016,"令牌失效"),
    S1017(1017,"账号欠费"),
    S1018(1018,"授权的服务已禁用"),
    S1019(1019,"授权的服务已过期"),
    S1020(1020,"该车调用次数已达上限"),
    S1021(1021,"client_id 不正确"),
    S1031(1031,"签名验证失败"),
    S1050(1050,"没有购买此类套餐"),
    S1051(1051,"套餐服务能力已用尽"),
    S1052(1052,"用户不存在"),
    S1053(1053,"服务无对应有效套餐"),
    S9001(9001,"系统异常"),
    S1060(1060,"新能源车辆已订阅，暂时无结果");

    /**
     * 状态code
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;


    /**
     * 按结果编码获取提示
     * @param code
     * @return
     */
    public static String getName(Integer code) {
        for (ResultCodeEnum c : ResultCodeEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return "";
    }
}
