package com.wzc.common.zhongjiao.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/3
 * @description : 中交结果数据对象
 */
@Data
@Schema(description = "中交结果数据对象")
public class ResultRes<T> {

    /**
     * 返回结果
     */
    @Schema(name = "返回结果")
    private T result;

    /**
     * 状态码，1001为正常
     */
    @Schema(name = "状态码")
    private Integer status;

    /**
     * 消息提示
     */
    @Schema(name = "消息提示")
    private String message;

    /**
     * 接口调用成功
     */
    @Schema(name = "调用状态")
    private Boolean isSuccess;

    /**
     * 订阅、删除订阅接口状态码(0-成功，1-失败)
     */
    private Integer state;

    /**
     * 调用失败返回
     * @param message
     * @return
     */
    public static ResultRes fail(String message) {
        ResultRes resultRes = new ResultRes<>();
        resultRes.setIsSuccess(Boolean.FALSE);
        resultRes.setMessage(message);
        return resultRes;
    }
}
