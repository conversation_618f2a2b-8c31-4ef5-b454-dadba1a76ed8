package com.wzc.common.zhongjiao.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/3
 * @description : 中交属性配置类
 */
@Data
public class ZhongJiaoProperties {

    /**
     * 允许中交接口登录的环境
     */
    @Value("${zhongjiao.loginEnv:prod}")
    private String env;

    /**
     * 中交接口地址
     */
    @Value("${zhongjiao.url:https://openapi.sinoiov.cn}")
    private String url;

    /**
     * 旧接口地址，保留定制接口
     */
    @Value("${zhongjiao.oldUrl:https://zhiyunopenapi.95155.com}")
    private String oldUrl;

    /**
     * 登录账号
     */
    @Value("${zhongjiao.username:}")
    private String username;

    /**
     * 登录密码
     */
    @Value("${zhongjiao.password:}")
    private String password;

    /**
     * 私钥
     */
    @Value("${zhongjiao.srt:}")
    private String srt;

    /**
     * 客户端id
     */
    @Value("${zhongjiao.cid:}")
    private String cid;

    /**
     * token保存时间
     */
    @Value("${zhongjiao.days:30}")
    private Long days;


    /**
     * 登陆接口地址
     */
    @Value("${zhongjiao.uri.login:/save/apis/login}")
    private String loginUri;

    /**
     * 入网验证接口地址
     */
    @Value("${zhongjiao.uri.checkTruckExistUri:/save/apis/checkTruckExistV2}")
    private String checkTruckExistUri;

    /**
     * 入网确认接口地址
     */
    @Value("${zhongjiao.uri.checkVehicleExistUri:/save/apis/checkVehicleExistV2}")
    private String checkVehicleExistUri;

    /**
     * 车辆订阅接口地址
     */
    @Value("${zhongjiao.uri.vnoRegUri:/save/apis/subByVno}")
    private String vnoRegUri;

    /**
     * 删除车辆订阅接口地址
     */
    @Value("${zhongjiao.uri.vnoDelUri:/save/apis/unsubByVno}")
    private String vnoDelUri;

    /**
     * 运输规划接口地址
     */
    @Value("${zhongjiao.uri.visualRouteUri:/save/apis/visualRoute}")
    private String visualRouteUri;

    /**
     * 运输行程接口地址
     */
    @Value("${zhongjiao.uri.routerPathUri:/save/apis/routerPath}")
    private String routerPathUri;

    /**
     * 行驶证核验接口地址
     */
    @Value("${zhongjiao.uri.vQueryLicenseUri:/save/apis/vQueryLicenseV2}")
    private String vQueryLicenseUri;

    /**
     * 车辆排放标准接口地址
     */
    @Value("${zhongjiao.uri.vEmissionByVnoUri:/save/apis/vEmissionByVnoV3}")
    private String vEmissionByVnoUri;
}
