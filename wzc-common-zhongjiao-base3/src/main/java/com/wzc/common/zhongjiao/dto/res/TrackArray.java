package com.wzc.common.zhongjiao.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/4
 * @description : 轨迹列表响应结果
 */
@Data
@Schema(description = "轨迹列表响应结果")
public class TrackArray {

    @Schema(name = "上报里程 单位：千米")
    private String mlg;

    @Schema(name = "正北方向夹角 " +
            "正北，大于 0 且小于 90：东\n" +
            "北，等于 90：正东，大于 90\n" +
            "且小于 180：东南，等于\n" +
            "180：正南，大于 180 且小于\n" +
            "270：西南，\n" +
            " * 等于 270：正西，大\n" +
            "于 270 且小于等于 359：西\n" +
            "北，其他：未知")
    private String agl;

    @Schema(name = "GPS 时间")
    private String gtm;

    @Schema(name = "海拔 单位：米")
    private String hgt;

    @Schema(name = "纬度 格式：1/600000.0（WGS84 坐标系）")
    private String lat;

    @Schema(name = "经度 格式：1/600000.0（WGS84 坐标系）")
    private String lon;

    @Schema(name = "GPS 速度 单位：千米/小时")
    private String spd;
}
