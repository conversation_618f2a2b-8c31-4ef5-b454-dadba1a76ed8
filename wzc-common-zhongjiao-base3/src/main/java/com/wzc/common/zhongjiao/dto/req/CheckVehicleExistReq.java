package com.wzc.common.zhongjiao.dto.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/4
 * @description : description
 */
@Data
@Schema(description = "查询入网确认请求参数")
public class CheckVehicleExistReq {

    @Schema(name = "车牌号(1 蓝色，2 黄色，3 黄   参考值：京 xxx_2，车牌号车架号二选一,都存在 ", required = true)
    private String vclN;

    @Schema(name = "车架号 ，车牌号车架号二选一,都存在", required = true)
    private String vin;
}
