package com.wzc.common.zhongjiao.dto.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.1.0
 * @date 2023/8/4
 * @description : 查询车辆排放标准返回参数
 */
@Data
@Schema(description = "行驶证信息核验响应结果")
public class QueryLicenseRes {

    @Schema(name = "联系人")
    private String vclWnrNm;

    @Schema(name = "联系人手机")
    private String vclWnrPhn;

    @Schema(name = "省编码")
    private String province;

    @Schema(name = "市编码")
    private String city;

    @Schema(name = "县")
    private String country;

    @Schema(name = "县编码")
    private String ctyCode;

    @Schema(name = "车辆类型")
    private String vclTpNm;

    @Schema(name = "车辆品牌")
    private String vbrndCdNm;

    @Schema(name = "车辆型号")
    private String prdCdNm;

    @Schema(name = "车主/业户")
    private String cmpNm;

    @Schema(name = "车辆识别代码/车架号")
    private String vin;

    @Schema(name = "总质量/准牵引总质量（单位 Kg）")
    private String vclTon;

    @Schema(name = "核定载重量（单位 Kg）")
    private String ldTn;

    @Schema(name = "外廓尺寸-长(mm)")
    private String vclLng;

    @Schema(name = "外廓尺寸-宽(mm)")
    private String vclWdt;

    @Schema(name = "外廓尺寸-高(mm)")
    private String vclHgt;

    @Schema(name = "内廓尺寸-长(mm)")
    private String boxLng;

    @Schema(name = "内廓尺寸-宽(mm)")
    private String boxWdt;

    @Schema(name = "内廓尺寸-高(mm)")
    private String boxHgt;

    @Schema(name = "行驶证发证日期  20191007")
    private String drvLicDt;

    @Schema(name = "行驶证有效期 UTC  1551251651949")
    private String drvLicVdy;

    @Schema(name = "车轴数  4")
    private String vehicleAxis;
}
