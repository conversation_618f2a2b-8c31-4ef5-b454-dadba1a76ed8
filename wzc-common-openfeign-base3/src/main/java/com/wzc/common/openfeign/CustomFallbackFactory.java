package com.wzc.common.openfeign;

import com.baidu.mapcloud.cloudnative.openfeign.fallback.BaseFallbackFactory;
import feign.Target;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.proxy.Enhancer;

/**
 * 默认 Fallback，避免写过多fallback类
 */
@AllArgsConstructor
@Slf4j
public class CustomFallbackFactory<T> extends BaseFallbackFactory<T> {

	private final Target<T> target;

	@Override
	public T newFallbackClient(Throwable throwable) {
		final Class<T> targetType = target.type();
		final String targetName = target.name();
		Enhancer enhancer = new Enhancer();
		enhancer.setSuperclass(targetType);
		enhancer.setUseCache(true);
		enhancer.setCallback(new BaseFeignFallback<>(targetType, targetName, throwable));
		return (T) enhancer.create();
	}
}
