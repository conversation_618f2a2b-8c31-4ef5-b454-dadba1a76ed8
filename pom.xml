<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>spring-boot-starter-parent</artifactId>
        <groupId>org.springframework.boot</groupId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>

    <groupId>com.wzc</groupId>
    <artifactId>wzc-common-parent-base3</artifactId>
    <packaging>pom</packaging>
    <version>1.2.0-SNAPSHOT</version>
    <name>wzc-common-parent</name>
    <description>公共包</description>

    <modules>
        <module>wzc-common-utils-base3</module>
        <module>wzc-common-swagger-base3</module>
        <module>wzc-common-mybatis-base3</module>
        <module>wzc-common-base3</module>
        <module>wzc-common-cache-base3</module>
        <module>wzc-common-openfeign-base3</module>
        <module>wzc-common-log-base3</module>
        <module>wzc-common-oss-base3</module>
        <module>wzc-common-user-base3</module>
        <module>wzc-common-web-base3</module>
        <module>wzc-common-api-base3</module>
        <module>wzc-common-tsdb-base3</module>
        <module>wzc-common-seata-base3</module>
        <module>wzc-common-validation-base3</module>
        <module>wzc-common-canal-base3</module>
        <module>wzc-common-file-base3</module>
        <module>wzc-common-kafka-base3</module>
        <module>wzc-common-report-base3</module>
        <module>wzc-common-search-base3</module>
        <module>wzc-common-es-base3</module>
        <module>wzc-common-zhongjiao-base3</module>
        <module>wzc-common-yingyan-base3</module>
        <module>wzc-common-ureport-base3</module>
        <module>wzc-common-crypto-base3</module>
        <module>wzc-common-map-base3</module>
        <module>wzc-common-event-base3</module>
        <module>wzc-common-esign-base3</module>
        <module>wzc-common-ocr-base3</module>
        <module>wzc-common-retry-base3</module>
        <module>wzc-common-easy-trans</module>
        <module>wzc-common-datascope</module>
        <module>wzc-common-adapter</module>
    </modules>

    <properties>
        <maven.compiler.target>11</maven.compiler.target>
        <java.version>11</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mail.version>1.4.7</mail.version>
        <common.version>1.2.0-SNAPSHOT</common.version>
        <poi.version>4.0.1</poi.version>
        <hutool.version>5.8.32</hutool.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-event-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-utils-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-mybatis-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-log-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-cache-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-openfeign-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-swagger-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-user-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-oss-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-web-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-easy-trans</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-api-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-validation-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-canal-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-file-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-report-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-kafka-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-search-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-es-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-seata-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-ureport-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-yingyan-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-zhongjiao-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-crypto-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-map-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-esign-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-ocr-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-datascope</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc</groupId>
                <artifactId>wzc-common-retry-base3</artifactId>
                <version>${common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>2.14.2</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.mapcloud.event-sender</groupId>
                <artifactId>event-sender-base</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.baidubce</groupId>
                <artifactId>bce-java-sdk</artifactId>
                <version>0.10.260</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.hbase</groupId>
                        <artifactId>hbase-client</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>bcprov-jdk15on</artifactId>
                        <groupId>org.bouncycastle</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
