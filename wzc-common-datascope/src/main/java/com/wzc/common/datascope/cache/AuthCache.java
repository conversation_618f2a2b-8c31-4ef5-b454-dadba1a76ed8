package com.wzc.common.datascope.cache;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.google.common.collect.Lists;
import com.wzc.common.cache.redis.CustomRedis;
import com.wzc.common.user.rpc.BaseAuthClientApi;
import com.wzc.common.user.rpc.dto.AuthDataScopeDTO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;

@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Data
public class AuthCache {

    private final static String AUTH_CACHE_PREFIX = "auth:cache:";
    private final BaseAuthClientApi baseAuthClientApi;

    public AuthDataScopeDTO getAuthDataScope() {
        AuthDataScopeDTO dataScopeDTO = getApiDataScope();
        if(ObjectUtil.isNotNull(dataScopeDTO)) {
            if(ObjectUtil.isNotEmpty(dataScopeDTO.getDataCenter()) && dataScopeDTO.getDataCenter().contains("-1")){
                dataScopeDTO.setDataCenter(Lists.newArrayList());
            }
            if(ObjectUtil.isNotEmpty(dataScopeDTO.getOrderType()) && dataScopeDTO.getOrderType().contains("-1")){
                dataScopeDTO.setOrderType(Lists.newArrayList());
            }
            if(ObjectUtil.isNotEmpty(dataScopeDTO.getCusMaterialSpecies()) && dataScopeDTO.getCusMaterialSpecies().contains("-1")){
                dataScopeDTO.setCusMaterialSpecies(Lists.newArrayList());
            }
            if(ObjectUtil.isNotEmpty(dataScopeDTO.getUserList()) && dataScopeDTO.getUserList().contains("-1")){
                dataScopeDTO.setUserList(Lists.newArrayList());
            }
            return dataScopeDTO;
        }
        return null;
    }

    public AuthDataScopeDTO getApiDataScope() {
        if(UserContext.userId().isPresent()) {
            String cacheKey = AUTH_CACHE_PREFIX + UserContext.userId().get();
            Object object = CustomRedis.get(cacheKey);
            AuthDataScopeDTO dataScopeDTO = null;
            if(ObjectUtil.isNotNull(object)) {
                dataScopeDTO = JSONUtil.toBean(Convert.toStr(object,""),AuthDataScopeDTO.class);
            }
            if (ObjectUtil.isNotNull(dataScopeDTO)) {
                return dataScopeDTO;
            }
            dataScopeDTO = baseAuthClientApi.getAllAuthDataScope();
            if(ObjectUtil.isNotNull(dataScopeDTO)) {
                CustomRedis.set(cacheKey, JSONUtil.toJsonStr(dataScopeDTO), 60L);
                return dataScopeDTO;
            }
        }
        return null;
    }
}
