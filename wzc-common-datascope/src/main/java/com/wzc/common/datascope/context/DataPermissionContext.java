package com.wzc.common.datascope.context;


import com.alibaba.ttl.TransmittableThreadLocal;
import com.wzc.common.datascope.model.DataScopeDTO;

public class DataPermissionContext {
    private static final ThreadLocal<DataScopeDTO> DATA_PERMISSION_THREAD_LOCAL = new TransmittableThreadLocal<>();

    public static void set(DataScopeDTO dataPermission) {
        DATA_PERMISSION_THREAD_LOCAL.set(dataPermission);
    }

    public static DataScopeDTO get() {
        return DATA_PERMISSION_THREAD_LOCAL.get();
    }

    public static void clear() {
        DATA_PERMISSION_THREAD_LOCAL.remove();
    }
}