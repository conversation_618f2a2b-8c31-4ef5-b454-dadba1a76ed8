package com.wzc.common.util.spring;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.wzc.common.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

@Slf4j
public class PropertyUtil {
    private static Environment getEnv() {
        return SpringUtil.getBean(Environment.class);
    }

    public static String getProperty(String key) {
        return getEnv().getProperty(key);
    }

    public static String getProperty(String key, boolean isThrow) {
        try {
            Environment environment = getEnv();
            return environment.getProperty(key);
        } catch (Exception e) {
            if (isThrow) {
                log.error("获取变量异常=={}", ExceptionUtil.stacktraceToString(e));
                throw e;
            }
        }
        return null;
    }
}
