package com.wzc.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationEvent;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * spring 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SpringUtil implements ApplicationContextAware {

	private static ApplicationContext context;

	@Override
	public void setApplicationContext(@Nullable ApplicationContext context) throws BeansException {
		SpringUtil.context = context;
	}

	/**
	 * 获取bean
	 *
	 * @param clazz class类
	 * @param <T>   泛型
	 * @return T
	 */
	public static <T> T getBean(Class<T> clazz) {
		if (clazz == null) {
			return null;
		}
		return context.getBean(clazz);
	}

	/**
	 * 获取bean
	 *
	 * @param beanId beanId
	 * @param <T>    泛型
	 * @return T
	 */
	public static <T> T getBean(String beanId) {
		if (beanId == null) {
			return null;
		}
		return (T) context.getBean(beanId);
	}

	/**
	 * 获取bean
	 *
	 * @param beanName bean名称
	 * @param clazz    class类
	 * @param <T>      泛型
	 * @return T
	 */
	public static <T> T getBean(String beanName, Class<T> clazz) {
		if (null == beanName || "".equals(beanName.trim())) {
			return null;
		}
		if (clazz == null) {
			return null;
		}
		return (T) context.getBean(beanName, clazz);
	}

	/**
	 * 获取 ApplicationContext
	 *
	 * @return ApplicationContext
	 */
	public static ApplicationContext getContext() {
		if (context == null) {
			return null;
		}
		return context;
	}

	/**
	 * 获取bean
	 * @param var1
	 * @return
	 * @param <T>
	 */
	public static <T> Map<String, T> getBeansOfType(@Nullable Class<T> var1){
		if(context == null){
			return new HashMap<>();
		}
		return context.getBeansOfType(var1);
	}

	/**
	 * 获取bean——list
	 * @param tClass
	 * @return
	 * @param <T>
	 */
	public static <T> List<T> getBeansOfTypeList(@Nullable Class<T> tClass){
		if(context == null){
			return new ArrayList<>(0);
		}
		Map<String,T> map = context.getBeansOfType(tClass);
		return new ArrayList<>(map.values());
	}

	/**
	 * 发布事件
	 *
	 * @param event 事件
	 */
	public static void publishEvent(ApplicationEvent event) {
		if (context == null) {
			return;
		}
		try {
			context.publishEvent(event);
		} catch (Exception ex) {
			log.error(ex.getMessage());
		}
	}

}
