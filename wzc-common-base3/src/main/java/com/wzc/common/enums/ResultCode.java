package com.wzc.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务代码枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResultCode implements BizError {

	/**
	 * 请求未授权
	 */
	UN_AUTHORIZED(10641, "请求未授权"),

	/**
	 * 参数校验失败
	 */
	PARAM_VALID_ERROR(10642, " "),

	/**
	 * 业务异常
	 */
	FAILURE(10643, "业务异常 "),

	/**
	 * 服务器异常
	 */
	INTERNAL_SERVER_ERROR(10644, "服务器异常"),

	/**
	 * 校验rpc接口返回值相关
	 */
	API_EMPTY_DATA(10645, "API调用结果数据为空"),

	/**
	 * 校验rpc接口返回值相关
	 */
	API_EMPTY_DATA_BAIDU(10646, "调用第三方API调用结果数据为空"),

	/**
	 * URL验证失败。
	 */
	URL_ERROR(10647, "URL验证失败！！！"),

	PDF_TO_IMAGE(10648,"pdf生成图片失败！");
	/**
	 * code编码
	 */
	final Integer code;
	/**
	 * 中文信息描述
	 */
	final String msg;
}
