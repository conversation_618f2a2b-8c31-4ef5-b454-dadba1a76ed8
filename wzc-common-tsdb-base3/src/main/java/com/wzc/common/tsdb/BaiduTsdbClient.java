package com.wzc.common.tsdb;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baidubce.BceClientConfiguration;
import com.baidubce.services.tsdb.TsdbClient;
import com.baidubce.services.tsdb.model.*;
import com.google.common.collect.Maps;
import com.wzc.common.tsdb.properties.BaiduTsdbProperties;
import com.wzc.common.tsdb.constants.TsdbCommonConstants;
import com.wzc.common.tsdb.dto.TimeSerialDataDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.*;

/**
 * baidu tsdb 访问客户端
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Slf4j
public class BaiduTsdbClient {

    private BaiduTsdbProperties properties;

    private TsdbClient readTsdbClient;
    private TsdbClient writeTsdbClient;

    public BaiduTsdbClient(BaiduTsdbProperties properties) {
        BceClientConfiguration readConfig = new BceClientConfiguration()
                .withEndpoint(properties.getReadEndpoint());
        this.readTsdbClient = new TsdbClient(readConfig);
        BceClientConfiguration writeConfig = new BceClientConfiguration()
                .withEndpoint(properties.getWriteEndpoint());
        this.writeTsdbClient = new TsdbClient(writeConfig);
        this.properties = properties;
    }

    /**
     * 判断是否存在轨迹
     * @param vehicleNo
     * @param startTimestampSecond
     * @param endTimestampSecond
     * @return
     */
    public boolean checkGps(String vehicleNo, Long startTimestampSecond, Long endTimestampSecond){
        Map<String, List<String>> tags = new HashMap<>();
        tags.put(TsdbCommonConstants.TAGKEY, Collections.singletonList(vehicleNo));
        Filters filters = new Filters();
        filters.withAbsoluteStart(startTimestampSecond);
        filters.withAbsoluteEnd(endTimestampSecond);
        filters.withTags(tags);
        Query query = new Query()
                .withMetric(TsdbCommonConstants.MERTIC)
                .withField(TsdbCommonConstants.FIELD)
                .withFilters(filters)
                .withLimit(1);
        QueryDatapointsResponse dataPointsResponse = readTsdbClient.queryDatapoints(Collections.singletonList(query));
        log.info("验证是否存在轨迹：{}",JSONUtil.toJsonStr(dataPointsResponse.getResults()));
        return dataPointsResponse.getResults().stream()
                .anyMatch(result -> result.getRawCount() > 0);
    }

    /**
     * 获取第一个轨迹
     * @param vehicleNo
     * @param startTimestampSecond
     * @param endTimestampSecond
     * @return
     */
    @SneakyThrows
    public TimeSerialDataDTO getGpsOne(String vehicleNo, Long startTimestampSecond, Long endTimestampSecond) {
        Map<String, List<String>> tags = Maps.newHashMap();
        tags.put(TsdbCommonConstants.TAGKEY, Collections.singletonList(vehicleNo));
        Filters filters = new Filters();
        filters.withAbsoluteStart(startTimestampSecond);
        filters.withAbsoluteEnd(endTimestampSecond);
        filters.withTags(tags);
        Query query = new Query()
                .withMetric(TsdbCommonConstants.MERTIC)
                .withField(TsdbCommonConstants.FIELD)
                .withFilters(filters)
                .withLimit(1);
        QueryDatapointsResponse dataPointsResponse = readTsdbClient.queryDatapoints(Collections.singletonList(query));
        for (Result subResult : dataPointsResponse.getResults()) {
            for (Group subGroup : subResult.getGroups()) {
                for (Group.TimeAndValue timeAndValue : subGroup.getTimeAndValueList()) {
                    return JSONUtil.toBean(timeAndValue.getStringValue(), TimeSerialDataDTO.class);
                }
            }
        }
        return null;
    }

    /**
     * 根据车牌号查询tsdb轨迹数据
     *
     * @param vehicleNo 车牌号
     * @param startTimestampSecond 开始时间戳(秒)
     * @param endTimestampSecond 结束时间戳(秒)
     */
    @SneakyThrows
    public List<TimeSerialDataDTO> getDataByVehicleNo(String vehicleNo, Long startTimestampSecond, Long endTimestampSecond) {
        Map<String, List<String>> tags = new HashMap<>();
        tags.put(TsdbCommonConstants.TAGKEY, Collections.singletonList(vehicleNo));
        Filters filters = new Filters();
        filters.withAbsoluteStart(startTimestampSecond);
        filters.withAbsoluteEnd(endTimestampSecond);
        filters.withTags(tags);
        // 分页次数
        int pageCount = 0;
        // 分页偏移量
        int offset = 0;
        List<TimeSerialDataDTO> totalTimeSerialList = new ArrayList<>();
        List<TimeSerialDataDTO> tempTimeSerialList;
        do {
            Query query = new Query()
                    .withMetric(TsdbCommonConstants.MERTIC)
                    .withField(TsdbCommonConstants.FIELD)
                    .withFilters(filters)
                    .withOffset(offset)
                    .withLimit(properties.getQueryDataPageSize());
            QueryDatapointsResponse dataPointsResponse = readTsdbClient.queryDatapoints(Collections.singletonList(query));
            tempTimeSerialList = new ArrayList<>();
            // 组装数据
            for (Result subResult : dataPointsResponse.getResults()) {
                for (Group subGroup : subResult.getGroups()) {
                    for (Group.TimeAndValue timeAndValue : subGroup.getTimeAndValueList()) {
                        TimeSerialDataDTO timeSerialData = JSONUtil.toBean(timeAndValue.getStringValue(), TimeSerialDataDTO.class);
                        tempTimeSerialList.add(timeSerialData);
                    }
                }
            }
            totalTimeSerialList.addAll(tempTimeSerialList);
            // 更新偏移量
            offset += properties.getQueryDataPageSize();
            pageCount++;
            if (pageCount > TsdbCommonConstants.MAX_PAGE_COUNT) {
                log.warn("tsdb查询超过最大分页数 vehicleNo:{} startTimestampSecond:{} endTimestampSecond:{}", vehicleNo, startTimestampSecond, endTimestampSecond);
                break;
            }
        } while (CollectionUtil.isNotEmpty(tempTimeSerialList));
        return totalTimeSerialList;
    }

    /**
     * 根据sql脚本查询
     */
    public GetRowsWithSqlResponse getBySql(String sql) {
        return readTsdbClient.getRowsWithSql(sql);
    }

    /**
     * 写入数据到tsdb
     */
    public void writeData(List<TimeSerialDataDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            log.warn("writeData无数据");
            return;
        }
        List<Datapoint> dataPoints = new ArrayList<>();
        for (TimeSerialDataDTO serialData : list) {
            Datapoint dataPoint = new Datapoint();
            dataPoint.withMetric(TsdbCommonConstants.MERTIC);
            dataPoint.withField(TsdbCommonConstants.FIELD);
            dataPoint.addTag(TsdbCommonConstants.TAGKEY, serialData.getCarNo());
            dataPoint.addStringValue(Long.parseLong(serialData.getLocTime()), JSONUtil.toJsonStr(serialData));
            dataPoints.add(dataPoint);
        }
        WriteDatapointsRequest dataPointsRequest = new WriteDatapointsRequest();
        dataPointsRequest.setDatapoints(dataPoints);
        writeTsdbClient.writeDatapoints(dataPointsRequest);
    }
}
