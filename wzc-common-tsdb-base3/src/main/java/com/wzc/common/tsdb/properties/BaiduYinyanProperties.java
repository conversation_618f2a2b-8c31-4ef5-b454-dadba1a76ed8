package com.wzc.common.tsdb.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

/**
 * 百度鹰眼配置
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@Data
public class BaiduYinyanProperties {

    /**
     * 鹰眼WebApi域名
     */
    @Value("${baidu.yingyan.host:http://yingyan.baidu.com}")
    private String yingyanHost;

    /**
     * 鹰眼WebApi轨迹查询路径
     */
    @Value("${baidu.yingyan.track.patch:/api/v3/track/gettrack}")
    private String yingyanTrackPath;

    /**
     * 鹰眼WebApi接口serviceId
     */
    @Value("${baidu.yingyan.service-id:}")
    private String yingyanServiceId;

    /**
     * 鹰眼WebApi接口AccessKey
     */
    @Value("${baidu.yingyan.access-key:}")
    private String yingyanAccessKey;

}
