package com.wzc.be.common.kafka;


import com.wzc.be.common.kafka.config.*;
import com.wzc.be.common.kafka.config.proper.KafkaConsumerProperties;
import com.wzc.be.common.kafka.config.proper.KafkaProducerProperties;
import com.wzc.be.common.kafka.util.KafkaTools;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.KafkaAdminClient;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.producer.Producer;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;

import java.util.Map;
import java.util.Properties;

@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties({KafkaConsumerProperties.class, KafkaProducerProperties.class})
@Import({KafkaConsumerConfig.class, KafkaProducerConfig.class, KafkaConsumerConfig2.class, KafkaProducerConfig2.class})
@PropertySource(value = "classpath:/kafka.properties")
public class AutoKafkaConfig {

    @Bean
    public KafkaCloser kafkaCloser(Map<String, Producer<?, ?>> procedures,Map<String, Consumer<?, ?>> consumers){
        return new KafkaCloser(procedures,consumers);
    }

    /**
     * Kafka管理Client用于Topic的创建等操作
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.kafka.client.admin.enable", havingValue = "true")
    public KafkaAdminClient kafkaAdminClient(KafkaProducerProperties kafkaProducerProperties){
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaProducerProperties.getServers());
        return (KafkaAdminClient) KafkaAdminClient.create(props);
    }

    @Bean("kafkaTools")
    @ConditionalOnProperty(name = "spring.data.kafka.client.admin.enable", havingValue = "true")
    public KafkaTools kafkaTools(KafkaAdminClient kafkaAdminClient, KafkaConsumerProperties kafkaConsumerProperties){
        return new KafkaTools(kafkaAdminClient, kafkaConsumerProperties);
    }
}
