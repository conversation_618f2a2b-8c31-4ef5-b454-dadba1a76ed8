package com.wzc.be.common.kafka.config;

import lombok.NonNull;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.producer.Producer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;

import java.util.Map;
import java.util.Objects;

public class <PERSON><PERSON>kaCloser implements ApplicationListener<ContextClosedEvent> {

    private final Map<String, Producer<?, ?>> procedures;

    private final Map<String, Consumer<?, ?>> consumers;

    public KafkaCloser(Map<String, Producer<?, ?>> procedures, Map<String, Consumer<?, ?>> consumers) {
        this.procedures = procedures;
        this.consumers = consumers;
    }

    @Override
    public void onApplicationEvent(@NonNull ContextClosedEvent contextClosedEvent) {
        if (!Objects.isNull(procedures)) {
            procedures.values().forEach(Producer::close);
        }
        if (!Objects.isNull(consumers)) {
            consumers.values().forEach(Consumer::close);
        }
    }
}
