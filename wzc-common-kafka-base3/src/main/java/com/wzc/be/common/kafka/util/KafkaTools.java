package com.wzc.be.common.kafka.util;

import com.wzc.be.common.kafka.config.proper.KafkaConsumerProperties;
import com.wzc.common.string.StringPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ExecutionException;
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class KafkaTools {

    private final AdminClient kafkaAdminClient;

    private final KafkaConsumerProperties kafkaConsumerProperties;

    //获取主题对应消费组
    public Set<String> getGroupByTopic(String topic) throws ExecutionException, InterruptedException {

        //获取消费组
        ListConsumerGroupsResult listConsumerGroupsResult = kafkaAdminClient.listConsumerGroups();
        Collection<ConsumerGroupListing> consumerGroups = listConsumerGroupsResult.all().get();

        Set<String> groupSet = new HashSet<>();

        //匹配对应的消费组
        for (ConsumerGroupListing consumerGroup : consumerGroups) {
            //获取有客户端绑定关系的消费组
            DescribeConsumerGroupsResult describeConsumerGroupsResult = kafkaAdminClient.describeConsumerGroups(Collections.singletonList(consumerGroup.groupId()));
            Map<String, ConsumerGroupDescription> consumerGroupDescriptions = describeConsumerGroupsResult.all().get();

            for (Map.Entry<String, ConsumerGroupDescription> entry : consumerGroupDescriptions.entrySet()) {
                ConsumerGroupDescription consumerGroupDescription = entry.getValue();
                if (consumerGroupDescription.members().stream()
                        .anyMatch(memberDescription -> memberDescription.assignment().topicPartitions().stream()
                                .anyMatch(topicPartition -> topicPartition.topic().equals(topic)))) {
                    groupSet.add(entry.getKey());
                }
            }
        }
        return groupSet;

    }

    /**
     * 按顺序获取消费组
     * 如果报错，就取随机uuid。
     * @param topic
     * @return
     */
    public String getNextGroup(String topic) {
        try {
            //获取消费组
            Set<String> groupSet = getGroupByTopic(topic);
            //获得下一个消费组
            int nextNum = 1;
            for (String group : groupSet) {
                if (group.equals(kafkaConsumerProperties.getGroupPrefix() + nextNum)){
                    nextNum ++;
                }
            }
            return kafkaConsumerProperties.getGroupPrefix() + nextNum;
        }catch (Exception e){
            log.error("获取group异常:{}",e.getMessage());
            return kafkaConsumerProperties.getGroupPrefix() + UUID.randomUUID().toString().replaceAll(StringPool.DASH,StringPool.EMPTY);
        }
    }
}
