package com.wzc.be.common.kafka.config;

import com.wzc.be.common.kafka.config.proper.KafkaConsumerProperties;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;

import java.util.HashMap;
import java.util.Map;

/**
 * kafka消费者配置信息类
 *
 * <AUTHOR>
 */
@EnableConfigurationProperties({KafkaConsumerProperties.class})
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(name = "spring.data.kafka.servers2.enable", havingValue = "true")
public class KafkaConsumerConfig2 {

    @Bean("kafkaListenerContainerFactory2")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> kafkaListenerContainerFactory(KafkaConsumerProperties kafkaConsumerProperties) {
        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(consumerFactory(kafkaConsumerProperties));
        factory.setConcurrency(kafkaConsumerProperties.getConcurrency());
        factory.getContainerProperties().setPollTimeout(5000);
        factory.getContainerProperties().setAckMode(kafkaConsumerProperties.getAckMode());
        return factory;
    }

    /**
     * 消费者批量工厂
     */
    @Bean("batchFactory2")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, Object>> batchFactory(KafkaConsumerProperties kafkaConsumerProperties) {
        ConcurrentKafkaListenerContainerFactory<String, Object> factory = new ConcurrentKafkaListenerContainerFactory<>();
        Map<String, Object> map = consumerConfigs(kafkaConsumerProperties);
        //设置为批量消费，每个批次数量在Kafka配置参数中设置ConsumerConfig.MAX_POLL_RECORDS_CONFIG
        map.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, kafkaConsumerProperties.getMaxPollRecords());
        map.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, kafkaConsumerProperties.getPollInterval());
        factory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(map));
        factory.setConcurrency(kafkaConsumerProperties.getConcurrency());
        factory.setBatchListener(true);
        factory.getContainerProperties().setPollTimeout(5000);
        factory.getContainerProperties().setAckMode(kafkaConsumerProperties.getAckMode());
        return factory;
    }

    public ConsumerFactory<String, String> consumerFactory(KafkaConsumerProperties kafkaConsumerProperties) {
        return new DefaultKafkaConsumerFactory<>(consumerConfigs(kafkaConsumerProperties));
    }

    // consumer配置信息初始化
    public Map<String, Object> consumerConfigs(KafkaConsumerProperties kafkaConsumerProperties) {
        Map<String, Object> propsMap = new HashMap<>(16);
        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConsumerProperties.getServers2());
        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, kafkaConsumerProperties.isEnableAutoCommit());
        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, kafkaConsumerProperties.getAutoCommitInterval());
        propsMap.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, kafkaConsumerProperties.getSessionTimeout());
        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, kafkaConsumerProperties.getGroupId());
        propsMap.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, kafkaConsumerProperties.getAutoOffsetReset());
        return propsMap;
    }
}