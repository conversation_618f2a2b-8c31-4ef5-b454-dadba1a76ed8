package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 银行卡识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsRoadTransportCertResult {

    /**
     *
     */
    @Alias("Angle")
    private BigDecimal angle;
    /**
     *
     */
    @Alias("StructuralList")
    private List<HundredWattsBaseStructuralList> structuralList;

    /**
     * 请求id
     */
    @Alias("RequestId")
    private String requestId;
}
