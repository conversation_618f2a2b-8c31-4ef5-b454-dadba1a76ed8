package com.wzc.common.ocr.dto.base;

import com.wzc.common.ocr.dto.base.result.OcrBaseResult;
import lombok.Data;

/**
 * OCR识别记录
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class OcrCallRecordDTO {

    /**
     * 图片地址
     */
    private String url;

    /**
     * 图片base64
     */
    private String base64;

    /**
     * 图片类型
     */
    private String type;

    /**
     * ocr供应商
     */
    private String supplier;

    /**
     * 项目划分
     */
    private Integer projectDivision;

    /**
     * 识别是否成功
     */
    private Boolean success;

    /**
     * 三方返回message
     */
    private Exception exception;

    /**
     * 识别结果
     */
    private OcrBaseResult result;


    /**
     * 扩展字段
     */
    private String extend;


}
