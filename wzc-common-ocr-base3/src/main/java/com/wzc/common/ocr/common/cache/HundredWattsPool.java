package com.wzc.common.ocr.common.cache;

import java.util.HashMap;
import java.util.Map;

/**
 * 设置佰瓦和原有百度ocr返回值的映射
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
public class HundredWattsPool {

    public static final Map<String, String> VEHICLE_LICENSE_RESPONSE_POOL;
    public static final Map<String, String> DRIVERS_LICENSE_RESPONSE_POOL;
    public static final Map<Integer, String> ID_CARD_RESPONSE_POOL;
    public static final Map<Integer, String> ID_CARD_IMAGE_STATAS_RESPONSE_POOL;
    public static final Map<String, String> BUSINESS_RESPONSE_POOL;

    //身份证识别正确编码
    public static final Integer ID_CARD_NORMAL = 0;
    //营业执照识别正确编码
    public static final String BUSINESS_NORMAL = "0";
    //行驶证识别正确值
    public static final String VEHICLE_LICENSE_NORMAL = "normal";
    //驾驶证识别正确值
    public static final String DRIVERS_LICENSE_NORMAL = "normal";

    //身份证识别清晰度,当超过50时，对应IsClear返回1，低于50，则返回0
    public static final Integer ID_CARD_IS_CLEAR_FRACTION = 50;
    //边框/四角完整度,当超过50时，对应IsComplete返回1，低于50，则返回0
    public static final Integer ID_CARD_IS_COMPLETE_FRACTION = 50;


    // 私有构造函数，用于隐藏默认的公共构造函数
    private HundredWattsPool() {
        // 在私有构造函数中可以执行一些初始化操作
    }

    static {
        VEHICLE_LICENSE_RESPONSE_POOL = new HashMap<>();
        //复印件
        VEHICLE_LICENSE_RESPONSE_POOL.put("WARN_DRIVER_LICENSE_COPY_CARD", "copy");
        //翻拍
        VEHICLE_LICENSE_RESPONSE_POOL.put("WARN_DRIVER_LICENSE_SCREENED_CARD", "screen");

        DRIVERS_LICENSE_RESPONSE_POOL = new HashMap<>();
        //复印件
        DRIVERS_LICENSE_RESPONSE_POOL.put("WARN_DRIVER_LICENSE_COPY_CARD", "copy");
        //翻拍
        DRIVERS_LICENSE_RESPONSE_POOL.put("WARN_DRIVER_LICENSE_SCREENED_CARD", "screen");

        ID_CARD_RESPONSE_POOL = new HashMap<>();
        // 正常
        ID_CARD_RESPONSE_POOL.put(ID_CARD_NORMAL, "normal");
        //复印件
        ID_CARD_RESPONSE_POOL.put(-9102, "copy");
        //临时身份证
        ID_CARD_RESPONSE_POOL.put(-9104, "temporary");
        //翻拍
        ID_CARD_RESPONSE_POOL.put(-9103, "screen");
        //其他未知情况
        ID_CARD_RESPONSE_POOL.put(-9100, "unknown");
        ID_CARD_RESPONSE_POOL.put(-9101, "unknown");
        ID_CARD_RESPONSE_POOL.put(-9105, "unknown");
        ID_CARD_RESPONSE_POOL.put(-9106, "unknown");
        ID_CARD_RESPONSE_POOL.put(-9107, "unknown");

        BUSINESS_RESPONSE_POOL = new HashMap<>();
        //复印件
        BUSINESS_RESPONSE_POOL.put(BUSINESS_NORMAL, "copy");

        BUSINESS_RESPONSE_POOL.put("WARN_COPY_CARD", "copy");
        //翻拍
        BUSINESS_RESPONSE_POOL.put("WARN_RESHOOT_CARD", "screen");


        ID_CARD_IMAGE_STATAS_RESPONSE_POOL = new HashMap<>();
        ID_CARD_IMAGE_STATAS_RESPONSE_POOL.put(ID_CARD_NORMAL, "normal");
        ID_CARD_IMAGE_STATAS_RESPONSE_POOL.put(-9107, "over_exposure");
    }

}
