package com.wzc.common.ocr.dto.supplier.baidu;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * 泰普科基础返回
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class BaiduBaseResponse<T> {

    /**
     * 请求状态
     */
    @Alias("error_code")
    private Integer errorCode;

    /**
     * 返回消息
     */
    @Alias("error_msg")
    private String errorMsg;

    /**
     * 数据
     */
    @Alias("result")
    private T result;

    /**
     * 数据
     */
    @Alias("words_result")
    private T wordsResult;
    /**
     * 数据
     */
    @Alias("log_id")
    private String logId;
}
