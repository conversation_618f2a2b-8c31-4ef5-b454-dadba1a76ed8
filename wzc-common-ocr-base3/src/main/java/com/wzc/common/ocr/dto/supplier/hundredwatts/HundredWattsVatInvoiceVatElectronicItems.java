package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

/**
 * 银行卡识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsVatInvoiceVatElectronicItems {

    /**
     *
     */
    @Alias("DeparturePlace")
    private String departurePlace;
    /**
     *
     */
    @Alias("TransportItemsName")
    private String transportItemsName;
    /**
     *
     */
    @Alias("BuildingName")
    private String buildingName;
    /**
     *
     */
    @Alias("VehicleType")
    private String vehicleType;
    /**
     *
     */
    @Alias("Quantity")
    private String quantity;
    /**
     *
     */
    @Alias("Specification")
    private String specification;
    /**
     *
     */
    @Alias("Tax")
    private String tax;
    /**
     *
     */
    @Alias("ArrivalPlace")
    private String arrivalPlace;
    /**
     *
     */
    @Alias("unit")
    private String Unit;
    /**
     *
     */
    @Alias("VehicleBrand")
    private String vehicleBrand;
    /**
     *
     */
    @Alias("Name")
    private String name;
    /**
     *
     */
    @Alias("PlaceOfBuildingService")
    private String placeOfBuildingService;
    /**
     *
     */
    @Alias("Price")
    private String price;
    /**
     *
     */
    @Alias("Total")
    private String total;
    /**
     *
     */
    @Alias("TaxRate")
    private String taxRate;
    /**
     *
     */
    @Alias("AreaUnit")
    private String areaUnit;
    /**
     *
     */
    @Alias("EstateNumber")
    private String estateNumber;
}
