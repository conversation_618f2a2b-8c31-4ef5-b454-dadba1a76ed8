package com.wzc.common.ocr.dto.supplier.timecapsule;

import com.wzc.common.ocr.dto.base.result.OcrBaseResult;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 泰普科行驶证识别结果
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TimecapsuleVehicleLicenseResult extends OcrBaseResult {

    /**
     * "号牌号码
     */
    private String vehicleNo;

    /**
     * 号牌颜色
     */
    private String licensePlateColor;

    /**
     * 车辆识别代号
     */
    private String vehicleIdentificationNumber;

    /**
     * 发动机号码
     */
    private String engineNumber;

    /**
     * 所有人
     */
    private String owner;

    /**
     * 使用性质
     */
    private String usage;

    /**
     * 车辆类型
     */
    private String vehicleType;

    /**
     * 品牌型号
     */
    private String brandModel;

    /**
     * 住址
     */
    private String address;

    /**
     * 发证单位
     */
    private String issuingAuthority;

    /**
     * 发证日期(yyyy-MM-dd)
     */
    private String issuingDate;

    /**
     * 注册日期(yyyy-MM-dd)
     */
    private String registrationDate;

    /**
     * 检验记录
     */
    private String inspectionRecord;

    /**
     * 核定载质量
     */
    private String approvedLoad;

    /**
     * 整备质量
     */
    private String curbWeight;

    /**
     * 外廓尺寸
     */
    private String exteriorDimensions;

    /**
     * 车辆长度(毫米)
     */
    private String carLength;

    /**
     * 车辆宽度(毫米)
     */
    private String carWidth;

    /**
     * 车辆高度(毫米)
     */
    private String carHeight;

    /**
     * 核定载人数
     */
    private String approvedPassengers;

    /**
     * 总质量
     */
    private String grossWeight;

    /**
     * 燃油类型
     */
    private String fuelType;

    /**
     * 准牵引总质量
     */
    private String towingCapacity;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 证芯编号
     */
    private String chipNumber;

    /**
     * 档案编号
     */
    private String fileNumber;

    /**
     * 行驶证类型
     */
    private String riskType;

}
