package com.wzc.common.ocr.common.base;/**
 * Create By hjh on 2023/12/4
 */

import com.wzc.common.ocr.dto.base.result.*;
import com.wzc.common.ocr.dto.base.request.OcrBase64Req;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 *@ClassName OcrClientHandler
 *@Description ocr客户端策略基类
 *<AUTHOR>
 *@Date 2023/12/4 10:53
 *@Version 1.0
 **/
public abstract class OcrClientHandler {

    /**
     * 榜单图片识别
     */
    public abstract PoundBillResult poundBill(OcrBase64Req ocrBase64Req);
    /**
     * 身份证图片识别
     */
    public abstract IdCardResult idCard(OcrBase64Req ocrBase64Req);

    /**
     * 银行卡识别
     */
    public abstract BankCardResult bankCard(OcrBase64Req ocrBase64Req);

    /**
     * 增值税发票识别
     */
    public abstract VatInvoiceResult vatInvoice(OcrBase64Req ocrBase64Req);

    /**
     * 道路运输许可证识别
     */
    public abstract RoadTransportCertResult roadTransportLicense(OcrBase64Req ocrBase64Req);

    /**
     * 行驶证识别
     */
    public abstract VehicleLicenseResult vehicleLicense(OcrBase64Req ocrBase64Req);

    /**
     * 驾驶证识别
     */
    public abstract DriverLicenseResult driverLicense(OcrBase64Req ocrBase64Req);

    /**
     *  营业执照识别
     */
    public abstract BusinessLicenseResult businessLicense(OcrBase64Req ocrBase64Req);

    /**
     *  车牌识别
     */
    public abstract LicensePlateResult licensePlate(OcrBase64Req ocrBase64Req);

    /**
     *  道路运输从业人员资格证
     */
    public abstract QualificationCertificateForRoadTransportPersonnelResult qualificationCertificateForRoadTransportPersonnel(OcrBase64Req ocrBase64Req);

    /**
     *  道路运输经营许可证
     */
    public abstract RoadTransportOperationLicenseResult roadTransportOperationLicense(OcrBase64Req ocrBase64Req);

    /**
     *  水路运输经营许可证
     */
    public abstract WaterTransportOperationLicenseResult waterTransportOperationLicense(OcrBase64Req ocrBase64Req);

    /**
     *  船舶运输营运证
     */
    public abstract ShipTransportOperationCertificateResult shipTransportOperationCertificateResult(OcrBase64Req ocrBase64Req);

    /**
     *  船舶自动识别系统AIS标识码证书
     */
    public abstract AISCertificateResult aisCertificate(OcrBase64Req ocrBase64Req);

    /**
     *  船员适任证
     */
    public abstract CrewCompetencyCertificateResult crewCompetencyCertificate(OcrBase64Req ocrBase64Req);

    /**
     * 获取方法名
     */
    public abstract String getType();



}
