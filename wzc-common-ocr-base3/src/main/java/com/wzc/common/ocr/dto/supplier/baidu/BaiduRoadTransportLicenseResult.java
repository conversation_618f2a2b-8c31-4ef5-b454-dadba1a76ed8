package com.wzc.common.ocr.dto.supplier.baidu;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * ocr身份证识别结果
 *
 * <AUTHOR>
 * @date 2023年06月26日
 */
@Data
public class BaiduRoadTransportLicenseResult extends BaiduBaseResult implements Serializable {

    /**
     * 唯一的log id，用于问题定位
     */
    @Alias("log_id")
    private Long logId;

    /**
     * 传入PDF文件的总页数，当 pdf_file 参数有效时返回该字段
     */
    @Alias("pdf_file_size")
    private String pdfFileSize;

    /**
     * 识别结果数
     */
    @Alias("words_result_num")
    private Integer wordsResultNum;

    /**
     * 识别结果
     */
    @Alias("words_result")
    private Map<String, List<BaiduWord>> wordsResult;
}
