package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

/**
 * 佰瓦营业执照识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsBusinessLicenseResult {

    /**
     *
     */
    @Alias("RegNum")
    private String regNum;
    /**
     *
     */
    @Alias("Name")
    private String name;
    /**
     *
     */
    @<PERSON><PERSON>("Capital")
    private String capital;
    /**
     *
     */
    @<PERSON><PERSON>("Person")
    private String person;
    /**
     *
     */
    @Alias("Address")
    private String address;
    /**
     *
     */
    @<PERSON>as("Business")
    private String business;
    /**
     *
     */
    @Alias("Type")
    private String type;
    /**
     *
     */
    @Alias("Period")
    private String period;
    /**
     *
     */
    @Alias("ComposingForm")
    private String composingForm;
    /**
     *
     */
    @Alias("SetDate")
    private String setDate;
    /**
     *
     */
    @Alias("IsDuplication")
    private String isDuplication;
    /**
     *
     */
    @Alias("RegistrationDate")
    private String registrationDate;
    /**
     *
     */
    @Alias("Angle")
    private String angle;

    /**
     *
     */
    @<PERSON>as("RecognizeWarnMsg")
    private List<String> recognizeWarnMsg;

    /**
     *
     */
    @Alias("RequestId")
    private String requestId;
}



