package com.wzc.common.ocr.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;

/**
 * 佰瓦对接配置
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Getter
public class HundredWattsProperties {


    /**
     * 佰瓦 userName
     */
    @Value("${ocr.hundredwatts.userName}")
    private String userName;

    /**
     * 佰瓦 password
     */
    @Value("${ocr.hundredwatts.password}")
    private String password;

    /**
     * 佰瓦 url
     */
    @Value("${ocr.hundredwatts.tokenUrl}")
    private String tokenUrl;

    /**
     * 佰瓦 url
     */
    @Value("${ocr.hundredwatts.url}")
    private String url;



}
