package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 银行卡识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsVatInvoiceMixedInvoiceItems {

    /**
     *
     */
    @Alias("Angle")
    private BigDecimal angle;
    /**
     *
     */
    @Alias("SingleInvoiceInfos")
    private HundredWattsVatInvoiceSingleInvoiceInfos singleInvoiceInfos;

    /**
     *
     */
    @Alias("Type")
    private Integer type;

    /**
     *
     */
    @Alias("SubType")
    private String subType;
}
