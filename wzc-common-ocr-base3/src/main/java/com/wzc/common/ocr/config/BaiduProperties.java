package com.wzc.common.ocr.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;

/**
 * 百度对接配置
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Getter
public class BaiduProperties {


    /**
     * aip oauth访问地址
     */
    @Value("${baidu.ocr.oauth-url:https://aip.baidubce.com/oauth/2.0/token}")
    private String oauthUrl;


    /**
     * accessKeyId
     */
    @Value("${baidu.ocr.access-key}")
    private String accessKey;

    /**
     * secretKey
     */
    @Value("${baidu.ocr.secret-key}")
    private String secretKey;



    /**
     * ocr图片体积限制(kb)
     */
    @Value("${baidu.ocr.size.limit:3072}")
    private Integer bosUploadSizeLimit;


    /**
     * 百度道路运输证ocr地址
     */
    @Value("${ocr.baidu.road.transport.certificate.url:https://aip.baidubce.com/rest/2.0/ocr/v1/road_transport_certificate}")
    private String roadTransportCertificateUrl;
    /**
     * 百度身份证ocr地址
     */
    @Value("${ocr.baidu.idcard.url:https://aip.baidubce.com/rest/2.0/ocr/v1/idcard}")
    private String idCardUrl;
    /**
     * 百度行驶证ocr地址
     */
    @Value("${ocr.baidu.vehicle.license.url:https://aip.baidubce.com/rest/2.0/ocr/v1/vehicle_license}")
    private String vehicleLicenseUrl;
    /**
     * 百度驾驶证ocr地址
     */
    @Value("${ocr.baidu.driving.license.url:https://aip.baidubce.com/rest/2.0/ocr/v1/driving_license}")
    private String drivingLicenseUrl;
    /**
     * 百度驾驶证ocr地址
     */
    @Value("${ocr.baidu.business.license.url:https://aip.baidubce.com/rest/2.0/ocr/v1/business_license}")
    private String businesslicenseUrl;
    /**
     * 百度增值税发票
     */
    @Value("${ocr.baidu.business.license.url:https://aip.baidubce.com/rest/2.0/ocr/v1/vat_invoice}")
    private String vatInvoiceUrl;
    /**
     * 百度增值税发票
     */
    @Value("${ocr.baidu.bank.card.url:https://aip.baidubce.com/rest/2.0/ocr/v1/bankcard}")
    private String bankCardUrl;

}
