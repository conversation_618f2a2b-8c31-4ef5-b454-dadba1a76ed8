package com.wzc.common.ocr.client;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.BizErrorWrapper;
import com.baidu.mapcloud.cloudnative.common.model.BizException;
import com.wzc.common.ocr.common.base.OcrClientHandler;
import com.wzc.common.ocr.common.constant.OcrConstants;
import com.wzc.common.ocr.common.enums.ExceptionEnum;
import com.wzc.common.ocr.common.enums.OcrTypeEnum;
import com.wzc.common.ocr.common.enums.SupplierTypeEnum;
import com.wzc.common.ocr.common.enums.TimecapsuleCodeEnum;
import com.wzc.common.ocr.config.OcrProperties;
import com.wzc.common.ocr.convert.OcrConvert;
import com.wzc.common.ocr.db.OcrCallRecordService;
import com.wzc.common.ocr.dto.base.OcrCallRecordDTO;
import com.wzc.common.ocr.dto.base.request.OcrBase64Req;
import com.wzc.common.ocr.dto.base.request.OcrImageUrlReq;
import com.wzc.common.ocr.dto.base.request.OcrInputStreamReq;
import com.wzc.common.ocr.dto.base.result.*;
import com.wzc.common.ocr.pdf.model.Invoice;
import com.wzc.common.ocr.pdf.utils.PdfInvoiceExtractor;
import com.wzc.common.string.StringPool;
import com.wzc.common.util.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

import static java.util.Objects.isNull;

/**
 * OCR客户端
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@Slf4j
@AllArgsConstructor
public class OcrClient {

    private final OcrProperties ocrProperties;

    private final Map<String, OcrClientHandler> ocrClientHandlerMap;

    private final RandomSupplierClient randomSupplierClient;

    /**
     * 磅单识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public PoundBillResult poundBill(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.POUNDBILL);
        return poundBillBase64(ocrBase64Req);
    }

    /**
     * 磅单识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public PoundBillResult poundBill(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.POUNDBILL);
        return poundBillBase64(ocrBase64Req);
    }

    /**
     * 磅单识别
     */
    private PoundBillResult poundBillBase64(OcrBase64Req ocrBase64Req) {
        OcrClientHandler ocrClientHandler = this.initOcrClientHandler(ocrProperties.getPoundBillRouteStrategy());
        try {
            PoundBillResult basePoundBillResult = ocrClientHandler.poundBill(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, ocrClientHandler, basePoundBillResult);
            return basePoundBillResult;
        } catch (Exception ex) {
            log.error("磅单识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, ocrClientHandler, ex);
            throw ex;
        }
    }

    private OcrClientHandler initOcrClientHandler(String strategy){
        OcrClientHandler ocrClientHandler = null;
        //1.随机供应商策略
        if (SupplierTypeEnum.RANDOM_SUPPLIER.getCode().equals(strategy)){
            ocrClientHandler = randomSupplierClient.initClient();
        //2.固定供应商策略
        }else {
            ocrClientHandler = ocrClientHandlerMap.get(strategy);
            if (ObjectUtil.isEmpty(ocrClientHandler)){
                ocrClientHandler = ocrClientHandlerMap.get(SupplierTypeEnum.RANDOM_SUPPLIER.getCode());
                log.info("ocrClientHandler为空,随机的ocrClientHandler:{}", JSONUtil.toJsonStr(ocrClientHandler));
            }
        }
        return ocrClientHandler;
    }

    /**
     * 身份证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public IdCardResult idCard(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.IDCARD);
        return idCardBase64(ocrBase64Req);
    }

    /**
     * 身份证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public IdCardResult idCard(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.IDCARD);
        return idCardBase64(ocrBase64Req);
    }

    /**
     * 身份证识别
     */
    private IdCardResult idCardBase64(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getIdCardRouteStrategy());
        try {
            IdCardResult idCardResult = objectOcrClientHandler.idCard(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, idCardResult);
            return idCardResult;
        } catch (Exception ex) {
            log.error("身份证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }

    /**
     * 银行卡识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public BankCardResult bankCard(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.BANK_CARD);
        return bankCard64(ocrBase64Req);
    }

    /**
     * 银行卡识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public BankCardResult bankCard(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.BANK_CARD);
        return bankCard64(ocrBase64Req);
    }

    /**
     * 银行卡识别
     */
    private BankCardResult bankCard64(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getBankCardRouteStrategy());
        try {
            BankCardResult bankCardResult = objectOcrClientHandler.bankCard(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, bankCardResult);
            return bankCardResult;
        } catch (Exception ex) {
            log.error("银行卡识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }

    /**
     * 增值税发票识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public VatInvoiceResult vatInvoices(OcrInputStreamReq ocrInputStreamReq) throws IOException {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.VAT_INVOICES);
        return vatInvoices(ocrBase64Req);
    }

    /**
     * 增值税发票识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public VatInvoiceResult vatInvoices(OcrImageUrlReq ocrImageUrlReq) throws IOException {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.VAT_INVOICES);
        return vatInvoices(ocrBase64Req);
    }

    /**
     * 增值税发票识别
     */
    private VatInvoiceResult vatInvoices(OcrBase64Req ocrBase64Req) throws IOException {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getVatInvoiceRouteStrategy());
        VatInvoiceResult vatInvoiceResult = new VatInvoiceResult();
        try {
            if (ocrBase64Req.getImageUrl().contains(".pdf")){
                Invoice invoice = PdfInvoiceExtractor.extract(ocrBase64Req.getImageUrl());
                vatInvoiceResult = OcrConvert.invoicetoVatInvoiceResult(invoice);
            }else {
                vatInvoiceResult = objectOcrClientHandler.vatInvoice(ocrBase64Req);
                saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, vatInvoiceResult);
            }
        }catch (Exception ex) {
            log.error("增值税发票识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
        return vatInvoiceResult;
    }


    /**
     * 道路许可证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public RoadTransportCertResult roadTransportLicense(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.ROAD_TRANSPORT_LICENSE);
        return roadTransportLicense(ocrBase64Req);
    }

    /**
     * 道路许可证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public RoadTransportCertResult roadTransportLicense(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.ROAD_TRANSPORT_LICENSE);
        return roadTransportLicense(ocrBase64Req);
    }

    /**
     * 道路许可证识别
     */
    private RoadTransportCertResult roadTransportLicense(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getRoadTransportCertRouteStrategy());
        try {
            RoadTransportCertResult roadTransportCertResult = objectOcrClientHandler.roadTransportLicense(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, roadTransportCertResult);
            return roadTransportCertResult;
        } catch (Exception ex) {
            log.error("道路许可证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }


    /**
     * 行驶证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public VehicleLicenseResult vehicleLicense(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.VEHICLE_LICENSE);
        return vehicleLicense(ocrBase64Req);
    }

    /**
     * 行驶证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public VehicleLicenseResult vehicleLicense(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.VEHICLE_LICENSE);
        return vehicleLicense(ocrBase64Req);
    }

    /**
     * 行驶证识别
     */
    private VehicleLicenseResult vehicleLicense(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getVehicleLicenseRouteStrategy());
        try {
            VehicleLicenseResult vehicleLicenseResult = objectOcrClientHandler.vehicleLicense(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, vehicleLicenseResult);
            return vehicleLicenseResult;
        } catch (Exception ex) {
            log.error("行驶证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }

    /**
     * 驾驶证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public DriverLicenseResult driverLicense(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.DRIVER_LICENSE);
        return driverLicense(ocrBase64Req);
    }

    /**
     * 驾驶证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public DriverLicenseResult driverLicense(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.DRIVER_LICENSE);
        return driverLicense(ocrBase64Req);
    }

    /**
     * 驾驶证识别
     */
    private DriverLicenseResult driverLicense(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getDriverLicenseRouteStrategy());
        try {
            DriverLicenseResult driverLicenseResult = objectOcrClientHandler.driverLicense(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, driverLicenseResult);
            return driverLicenseResult;
        } catch (Exception ex) {
            log.error("驾驶证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }


    /**
     * 营业执照识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public BusinessLicenseResult businessLicense(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.DRIVER_LICENSE);
        return businessLicense(ocrBase64Req);
    }

    /**
     * 营业执照识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public BusinessLicenseResult businessLicense(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.BUSINESS_LICENSE);
        return businessLicense(ocrBase64Req);
    }

    /**
     * 营业执照识别
     */
    private BusinessLicenseResult businessLicense(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getBusinessLicenseRouteStrategy());
        try {
            BusinessLicenseResult businessLicenseResult = objectOcrClientHandler.businessLicense(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, businessLicenseResult);
            return businessLicenseResult;
        } catch (Exception ex) {
            log.error("营业执照识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }

    /**
     * 车牌识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public LicensePlateResult licensePlate(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.LICENSE_PLATE);
        return licensePlate(ocrBase64Req);
    }

    /**
     * 车牌识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public LicensePlateResult licensePlate(OcrImageUrlReq ocrImageUrlReq) {
        OcrBase64Req ocrBase64Req;
        if (StrUtil.isBlank(ocrImageUrlReq.getImageBase64())) {
            byte[] bytes = downloadImageToByte(ocrImageUrlReq.getImageUrl());
            ocrBase64Req = this.initOcrBase64Req(ocrImageUrlReq.getImageUrl(), Base64.encode(bytes), ocrImageUrlReq);
        } else {
            // 如果存在base64使用传入的base64
            ocrBase64Req = this.initOcrBase64Req(ocrImageUrlReq.getImageUrl(), ocrImageUrlReq.getImageBase64(), ocrImageUrlReq);
        }
        ocrBase64Req.setOcrType(OcrTypeEnum.LICENSE_PLATE);
        return licensePlate(ocrBase64Req);
    }

    /**
     * 车牌识别
     */
    private LicensePlateResult licensePlate(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getLicensePlateRouteStrategy());
        try {
            LicensePlateResult licensePlateResult = objectOcrClientHandler.licensePlate(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, licensePlateResult);
            return licensePlateResult;
        } catch (Exception ex) {
            log.error("车牌识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }

    /**
     * 道路运输从业人员资格证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public QualificationCertificateForRoadTransportPersonnelResult qualificationCertificateForRoadTransportPersonnel(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL);
        return qualificationCertificateForRoadTransportPersonnel(ocrBase64Req);
    }

    /**
     * 道路运输从业人员资格证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public QualificationCertificateForRoadTransportPersonnelResult qualificationCertificateForRoadTransportPersonnel(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL);
        return qualificationCertificateForRoadTransportPersonnel(ocrBase64Req);
    }

    /**
     * 道路运输从业人员资格证识别
     */
    private QualificationCertificateForRoadTransportPersonnelResult qualificationCertificateForRoadTransportPersonnel(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getRoadTransportOperatorCertRouteStrategy());
        try {
            QualificationCertificateForRoadTransportPersonnelResult qualificationCertificateForRoadTransportPersonnelResult = objectOcrClientHandler.qualificationCertificateForRoadTransportPersonnel(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, qualificationCertificateForRoadTransportPersonnelResult);
            return qualificationCertificateForRoadTransportPersonnelResult;
        } catch (Exception ex) {
            log.error("道路运输从业人员资格证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }

    /**
     * 道路运输经营许可证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public RoadTransportOperationLicenseResult roadTransportOperationLicense(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.ROAD_TRANSPORT_OPERATION_LICENSE);
        return roadTransportOperationLicense(ocrBase64Req);
    }

    /**
     * 道路运输经营许可证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public RoadTransportOperationLicenseResult roadTransportOperationLicense(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.ROAD_TRANSPORT_OPERATION_LICENSE);
        return roadTransportOperationLicense(ocrBase64Req);
    }

    /**
     * 道路运输经营许可证识别
     */
    private RoadTransportOperationLicenseResult roadTransportOperationLicense(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getRoadTransportOperationLicenseRouteStrategy());
        try {
            RoadTransportOperationLicenseResult roadTransportOperationLicenseResult = objectOcrClientHandler.roadTransportOperationLicense(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, roadTransportOperationLicenseResult);
            return roadTransportOperationLicenseResult;
        } catch (Exception ex) {
            log.error("道路运输经营许可证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }


    /**
     * 水路运输经营许可证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public WaterTransportOperationLicenseResult waterTransportOperationLicense(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.WATER_TRANSPORT_OPERATION_LICENSE);
        return waterTransportOperationLicense(ocrBase64Req);
    }

    /**
     * 水路运输经营许可证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public WaterTransportOperationLicenseResult waterTransportOperationLicense(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.WATER_TRANSPORT_OPERATION_LICENSE);
        return waterTransportOperationLicense(ocrBase64Req);
    }

    /**
     * 水路运输经营许可证识别
     */
    private WaterTransportOperationLicenseResult waterTransportOperationLicense(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getRoadTransportOperationLicenseRouteStrategy());
        try {
            WaterTransportOperationLicenseResult waterTransportOperationLicenseResult = objectOcrClientHandler.waterTransportOperationLicense(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, waterTransportOperationLicenseResult);
            return waterTransportOperationLicenseResult;
        } catch (Exception ex) {
            log.error("水路运输经营许可证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }


    /**
     * 船舶运输营运证识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public ShipTransportOperationCertificateResult shipTransportOperationCertificate(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.SHIP_TRANSPORT_OPERATION_CERTIFICATE);
        return shipTransportOperationCertificate(ocrBase64Req);
    }

    /**
     * 船舶运输营运证识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public ShipTransportOperationCertificateResult shipTransportOperationCertificate(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.SHIP_TRANSPORT_OPERATION_CERTIFICATE);
        return shipTransportOperationCertificate(ocrBase64Req);
    }

    /**
     * 船舶运输营运证识别
     */
    private ShipTransportOperationCertificateResult shipTransportOperationCertificate(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getRoadTransportOperationLicenseRouteStrategy());
        try {
            ShipTransportOperationCertificateResult shipTransportOperationCertificateResult = objectOcrClientHandler.shipTransportOperationCertificateResult(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, shipTransportOperationCertificateResult);
            return shipTransportOperationCertificateResult;
        } catch (Exception ex) {
            log.error("船舶运输营运证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }


    /**
     * 船舶自动识别系统AIS标识码证书识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public AISCertificateResult aisCertificate(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.AIS_CERTIFICATE);
        return aisCertificate(ocrBase64Req);
    }

    /**
     * 船舶自动识别系统AIS标识码证书识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public AISCertificateResult aisCertificate(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.AIS_CERTIFICATE);
        return aisCertificate(ocrBase64Req);
    }

    /**
     * 船舶自动识别系统AIS标识码证书识别
     */
    private AISCertificateResult aisCertificate(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getRoadTransportOperationLicenseRouteStrategy());
        try {
            AISCertificateResult aisCertificateResult = objectOcrClientHandler.aisCertificate(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, aisCertificateResult);
            return aisCertificateResult;
        } catch (Exception ex) {
            log.error("船舶自动识别系统AIS标识码证书识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }
    /**
     * 船舶自动识别系统AIS标识码证书识别 ocrInputStreamReq
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public CrewCompetencyCertificateResult crewCompetencyCertificate(OcrInputStreamReq ocrInputStreamReq) {
        InputStream inputStream = ocrInputStreamReq.getInputStream();
        if (isNull(inputStream)) {
            throw new RuntimeException(ExceptionEnum.OCR_INPUT_STREAM_IS_NULL_ERROR.getMsg());
        }
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req("", Base64.encode(inputStream), ocrInputStreamReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.AIS_CERTIFICATE);
        return crewCompetencyCertificate(ocrBase64Req);
    }

    /**
     * 船舶自动识别系统AIS标识码证书识别 ocrImageUrlReq
     *
     * @throws RuntimeException 执行中根据情况可能会抛出异常,如url无效或者ocr厂商异常
     */
    public CrewCompetencyCertificateResult crewCompetencyCertificate(OcrImageUrlReq ocrImageUrlReq) {
        String url = ocrImageUrlReq.getImageUrl();
        byte[] bytes = downloadImageToByte(url);
        OcrBase64Req ocrBase64Req = this.initOcrBase64Req(url, Base64.encode(bytes), ocrImageUrlReq);
        ocrBase64Req.setOcrType(OcrTypeEnum.CREW_COMPETENCY_CERTIFICATE);
        return crewCompetencyCertificate(ocrBase64Req);
    }

    /**
     * 船舶自动识别系统AIS标识码证书识别
     */
    private CrewCompetencyCertificateResult crewCompetencyCertificate(OcrBase64Req ocrBase64Req) {
        OcrClientHandler objectOcrClientHandler = this.initOcrClientHandler(ocrProperties.getCrewCompetencyCertificateStrategy());
        try {
            CrewCompetencyCertificateResult crewCompetencyCertificateResult = objectOcrClientHandler.crewCompetencyCertificate(ocrBase64Req);
            saveSuccessCallRecord(ocrBase64Req, objectOcrClientHandler, crewCompetencyCertificateResult);
            return crewCompetencyCertificateResult;
        } catch (Exception ex) {
            log.error("船员适任证识别异常:" + ExceptionUtil.stacktraceToString(ex));
            saveErrorCallRecord(ocrBase64Req, objectOcrClientHandler, ex);
            throw ex;
        }
    }


    /**
     * 初始化base64请求参数
     */
    private OcrBase64Req initOcrBase64Req(String url,String base64, OcrImageUrlReq ocrImageUrlReq){
        OcrBase64Req ocrBase64Req = new OcrBase64Req();
        ocrBase64Req.setImageUrl(url);
        ocrBase64Req.setImageBase64(base64);
        String cardSide = ocrImageUrlReq.getCardSide();
        if (StringUtils.hasText(cardSide)){
            ocrBase64Req.setCardSide(cardSide);
        }
        Integer projectDivision = ocrImageUrlReq.getProjectDivision();
        if (ObjectUtil.isNotEmpty(projectDivision)){
            ocrBase64Req.setProjectDivision(projectDivision);
        }
        ocrBase64Req.setExtend(ocrImageUrlReq.getExtend());
        return ocrBase64Req;
    }

    /**
     * 初始化base64请求参数
     */
    private OcrBase64Req initOcrBase64Req(String url,String base64, OcrInputStreamReq ocrInputStreamReq){
        OcrBase64Req ocrBase64Req = new OcrBase64Req();
        ocrBase64Req.setImageUrl(url);
        ocrBase64Req.setImageBase64(base64);
        String cardSide = ocrInputStreamReq.getCardSide();
        if (StringUtils.hasText(cardSide)){
            ocrBase64Req.setCardSide(cardSide);
        }
        Integer projectDivision = ocrInputStreamReq.getProjectDivision();
        if (ObjectUtil.isNotEmpty(projectDivision)){
            ocrBase64Req.setProjectDivision(projectDivision);
        }
        return ocrBase64Req;
    }





    /**
     * OCR调用记录保存(成功)
     */
    private void saveSuccessCallRecord(OcrBase64Req req, OcrClientHandler handler, OcrBaseResult result) {
        saveCallRecord(req, handler, Boolean.TRUE, null, result);
    }

    /**
     * OCR调用记录保存(失败)
     */
    private void saveErrorCallRecord(OcrBase64Req req, OcrClientHandler handler, Exception exception) {
        saveCallRecord(req, handler, Boolean.FALSE, exception, null);
    }

    /**
     * 记录调用记录
     */
    private void saveCallRecord(OcrBase64Req req, OcrClientHandler handler, Boolean success, Exception e, OcrBaseResult result) {
        if (!Boolean.TRUE.equals(ocrProperties.getEnableCallRecord())) {
            log.info("未开启OCR调用记录保存");
            return;
        }
        OcrCallRecordService callRecordService = SpringUtil.getBean(OcrCallRecordService.class);
        OcrCallRecordDTO callRecordDTO = new OcrCallRecordDTO();
        callRecordDTO.setUrl(req.getImageUrl());
        callRecordDTO.setExtend(req.getExtend());
        callRecordDTO.setType(req.getOcrType().getCode());
        callRecordDTO.setSupplier(handler.getType());
        callRecordDTO.setException(e);
        callRecordDTO.setResult(result);
        callRecordDTO.setSuccess(success);
        callRecordService.record(callRecordDTO);
    }

    /**
     * 图片下载
     */
    private byte[] downloadImageToByte(String url) {
        // 如果开关开启 & 域名中包含baidu bos域名 & 未包含process条件
        // 进行图片缩放处理
        if (Boolean.TRUE.equals(ocrProperties.getOcrBaiduBosProcessEnable())
                && url.contains(OcrConstants.BAIDU_BCE_BOS_HOST)
                && !url.contains(OcrConstants.BAIDU_BOS_PROCESS_LABEL)) {
            if (!url.contains(StringPool.QUESTION_MARK)) {
                url = url + StringPool.QUESTION_MARK;
            } else {
                url = url + StringPool.AMPERSAND;
            }
            url = url + OcrConstants.BAIDU_BOS_PROCESS_LABEL + StringPool.EQUALS
                    + String.format(OcrConstants.OCR_BAIDU_BOS_RESIZE_PROCESS, OcrConstants.OCR_RESIZE_MAX_WIDTH, OcrConstants.OCR_RESIZE_MAX_HEIGHT);
        }
        log.info("downloadImage:{}", url);
        byte[] bytes;
        try {
            bytes = HttpUtil.downloadBytes(url);
        }catch (Exception e){
            throw new BizException(BizErrorWrapper.msg(ExceptionEnum.OCR_IMAGE_NOT_EXIST.getCode(), ExceptionEnum.OCR_IMAGE_NOT_EXIST.getMsg()));
        }
        if (bytes.length == 0) {
            throw new RuntimeException(ExceptionEnum.OCR_DOWNLOAD_IMAGE_ERROR.getMsg());
        }
        return bytes;
    }

}
