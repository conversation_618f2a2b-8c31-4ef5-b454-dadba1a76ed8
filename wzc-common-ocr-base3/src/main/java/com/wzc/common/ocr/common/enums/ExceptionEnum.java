package com.wzc.common.ocr.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import lombok.Getter;

@Getter
public enum ExceptionEnum implements BizError {
    TIMECAPSULE_HTTP_REQUEST_ERROR(17001, "接口HTTP请求失败"),
    TIMECAPSULE_RESULT_STATUS_ERROR(17002, "接口返回状态失败 status:%s msg:%s"),
    OCR_DOWNLOAD_IMAGE_ERROR(17003, "OCR识别下载图片失败"),
    OCR_INPUT_STREAM_IS_NULL_ERROR(17004, "OCR识别inputstream为空"),
    HUNDREDWATTS_HTTP_REQUEST_ERROR(17005, "接口HTTP请求失败"),
    HUNDREDWATTS_HTTP_CODE_ERROR(17006, "接口HTTP请求失败 code:"),
    INTERFACE_HTTP_RESPONSE_CONVERT_ERROR(17007, "接口响应数据转换异常"),
    TIMECAPSULE_HTTP_NOT_EXSIST(17008, "接口不存在"),

    BAIDU_HTTP_REQUEST_ERROR(17009, "接口HTTP请求失败"),
    BAIDU_HTTP_REQUEST_ACCESS_TOKEN_ERROR(17010, "access_token接口HTTP请求失败"),
    BAIDU_HTTP_NOT_EXSIST(17011, "接口不存在"),
    HUNDREDWATTS_VAT_INVOICE_TEMPLATE_ERROR(17012, "不是正确的发票图片"),
    HUNDREDWATTS_AIS_CERTIFICATE_ERROR(17100, "疑似非船舶自动识别系统AIS标识码证书"),
    HUNDREDWATTS_CERTIFICATE_OF_COMPETENCY_ERROR(17106, "疑似非船员适任证AIS标识码证书"),
    HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_ERROR(17101, "疑似非船舶运输营运证"),
    HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_ERROR(17102, "疑似非水路运输经营许可证"),
    HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_ERROR(17103, "疑似非道路运输经营许可证"),
    HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_ERROR(17104, "疑似非道路运输人员从业资格证"),
    HUNDREDWATTS_ROAD_TRANSPORT_LICENSE_ERROR(17105, "疑似非道路运输许可证"),
    OCR_IMAGE_NOT_EXIST(17041, "OCR图片不存在"),
    IMAGE_COMPRESS_ERROR(17042, "压缩base64图片异常"),

    OCR_COMMON_ERROR(2000, "上传证件疑似非%s证件，请核实"),
    OCR_COMMON_POUND_BILL_ERROR(2001, "正常识别，请单独上传单张磅单")
    ;

    /**
     * code编码
     */
    private final Integer code;
    /**
     * 中文信息描述
     */
    private final String msg;

    ExceptionEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
