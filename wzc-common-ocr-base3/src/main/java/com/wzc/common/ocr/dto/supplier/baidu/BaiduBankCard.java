package com.wzc.common.ocr.dto.supplier.baidu;

import cn.hutool.core.annotation.Alias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * OCR文字dto
 *
 * <AUTHOR>
 * @date 2023年06月25日
 */
@Data
public class BaiduBankCard implements Serializable {

    /**
     * 银行卡卡号
     */
    @Alias("bank_card_Number")
    private String bankCardNumber;

    /**
     * 有效期
     */
    @Alias("valid_date")
    private String validDate;

    /**
     * 银行卡类型，0：不能识别; 1：借记卡; 2：贷记卡（原信用卡大部分为贷记卡）; 3：准贷记卡; 4：预付费卡
     */
    @Alias("bank_card_type")
    private String bankCardType;

    /**
     * 银行名，不能识别时为空
     */
    @Alias("bank_name")
    private String bankName;

    /**
     * 持卡人姓名，不能识别时为空
     */
    @Alias("holder_name")
    private String holderName;
}
