package com.wzc.common.ocr.dto.supplier.baidu;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;

/**
 * baidu access token 请求返回值
 *
 * <AUTHOR>
 * @date 2023年06月19日
 */
@Data
public class BaiduAccessTokenResponse implements Serializable {

    @Alias("refresh_token")
    private String refreshToken;

    /**
     * Access Token的有效期(秒为单位，有效期30天)
     */
    @Alias("expires_in")
    private Integer expiresIn;

    @Alias("session_key")
    private String sessionkey;


    @Alias("session_secret")
    private String sessionSecret;

    /**
     * token
     */
    @Alias("access_token")
    private String accessToken;

    @Alias("scope")
    private String scope;

    /**
     * 错误码
     */
    @Alias("error")
    private String error;

    /**
     * 错误描述信息
     */
    @Alias("error_description")
    private String errorDescription;

}
