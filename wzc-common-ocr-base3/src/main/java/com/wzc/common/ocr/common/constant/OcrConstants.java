package com.wzc.common.ocr.common.constant;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReUtil;

public class OcrConstants {

    public static void main(String[] args) {
        String str = "1000万元";
        long number = NumberUtil.parseInt(str); // 提取数字部分并转换为整数
        boolean containsNumber = ReUtil.contains("\\d", str);

        String s = NumberChineseFormatter.format(Double.parseDouble(String.valueOf(number)), true);
        str = s +  str.replaceAll("\\d", "");

    }

    private OcrConstants() {
        // 防止该类被实例化
        throw new AssertionError();
    }

    /*  泰普科参数开始   */

    /**
     * 泰普科appId参数名
     */
    public static final String TIMECAPSULE_APP_ID = "appid";

    /**
     * 泰普科image参数名
     */
    public static final String TIMECAPSULE_IMAGE = "image";

    /**
     * 泰普科requestTime参数名
     */
    public static final String TIMECAPSULE_TOKEN = "token";

    /**
     * 泰普科requestTime参数名
     */
    public static final String TIMECAPSULE_REQUEST_TIME = "requestTime";

    /**
     * 泰普科success status
     */
    public static final Integer TIMECAPSULE_SUCCESS_STATUS = 0;

    /**
     * 泰普科
     */
    public static final String TIMECAPSULE = "timecapsule";


    /*  泰普科参数结束   */

    /*  百度参数开始   */

    public static final String BAIDU_ACCESS_TOKEN = "access_token";
    public static final String BAIDU_GRANT_TYPE = "grant_type";
    public static final String BAIDU_CLIENT_ID = "client_id";
    public static final String BAIDU_CLIENT_SECRET = "client_secret";

    /**
     * 百度success status
     */
    public static final Integer BAIDU_SUCCESS_STATUS = 0;

    /**
     * 百度token的redis的key
     */
    public static final String BAIDU_OCR_ACCESS_TOKEN_CACHE_KEY = "BAIDU:OCR:ACCESS-TOKEN";


    public static final String BAIDU_OCR_NAME = "姓名";
    public static final String BAIDU_OCR_CARD_NO = "公民身份号码";
    public static final String BAIDU_OCR_BIRTH_DATE = "出生";
    public static final String BAIDU_OCR_GENDER = "性别";
    public static final String BAIDU_OCR_ADDRESS = "住址";
    public static final String BAIDU_OCR_EXPIRATION_DATE = "失效日期";
    public static final String BAIDU_OCR_ISSUING_AUTHORITY = "签发机关";
    public static final String BAIDU_OCR_ISSUE_DATE = "签发日期";
    public static final String BAIDU_OCR_NATION = "民族";

    public static final String BAIDU_OCR_CAR_NO_AND_COLOR = "车辆号牌";

    public static final String BAIDU_OCR_CAR_TYPE = "车辆类型";

    public static final String BAIDU_OCR_BUSINESS_SCOPE = "经营范围";

    public static final String BAIDU_OCR_CAR = "车";

    public static final String BAIDU_OCR_CAR_LOAD = "吨座位";

    public static final String BAIDU_OCR_CAR_LENGTH = "车辆毫米_长";

    public static final String BAIDU_OCR_CAR_WIDTH = "车辆毫米_宽";

    public static final String BAIDU_OCR_CAR_HEIGHT = "车辆毫米_高";

    public static final String BAIDU_OCR_CAR_NAME = "业户名称";

    public static final String BAIDU_OCR_TIME_DATA = "发证日期";


    public static final String BAIDU_OCR_ROAD_BUSINESS_LICENSE = "经营许可证";

    public static final String BAIDU_OCR_ROAD_TRANSPORT_PERMIT_NUMBER = "道路运输证号";


    /**
     * 号牌号码
     */
    public static final String BAIDU_OCR_VEHICLE_NO = "号牌号码";

    /**
     * 车辆识别代号
     */
    public static final String BAIDU_OCR_VEHICLE_IDENTIFICATION_NUMBER = "车辆识别代号";

    /**
     * 发动机号码
     */
    public static final String BAIDU_OCR_ENGINE_NUMBER = "发动机号码";

    /**
     * 所有人
     */
    public static final String BAIDU_OCR_OWNER = "所有人";
    /**
     * 使用性质
     */
    public static final String BAIDU_OCR_USAGE = "使用性质";

    /**
     * 车辆类型
     */
    public static final String BAIDU_OCR_VEHICLE_TYPE = "车辆类型";

    /**
     * 品牌型号
     */
    public static final String BAIDU_OCR_BRAND_MODEL = "品牌型号";

    /**
     * 发证单位
     */
    public static final String BAIDU_OCR_VEHICLE_LICENSE_ISSUING_AUTHORITY = "发证单位";

    /**
     * 发证日期
     */
    public static final String BAIDU_OCR_ISSUING_DATE = "发证日期";

    /**
     * 注册日期
     */
    public static final String BAIDU_OCR_REGISTRATION_DATE = "注册日期";

    /**
     * 检验记录
     */
    public static final String BAIDU_OCR_INSPECTION_RECORD = "检验记录";

    /**
     * 核定载质量
     */
    public static final String BAIDU_OCR_APPROVED_LOAD = "核定载质量";

    /**
     * 整备质量
     */
    public static final String BAIDU_OCR_CURB_WEIGHT = "整备质量";

    /**
     * 外廓尺寸
     */
    public static final String BAIDU_OCR_EXTERIOR_DIMENSIONS = "外廓尺寸";

    /**
     * 核定载人数
     */
    public static final String BAIDU_OCR_APPROVED_PASSENGERS = "核定载人数";

    /**
     * 总质量
     */
    public static final String BAIDU_OCR_GROSS_WEIGHT = "总质量";

    /**
     * 燃油类型
     */
    public static final String BAIDU_OCR_FUEL_TYPE = "燃油类型";

    /**
     * 准牵引总质量
     */
    public static final String BAIDU_OCR_TOWING_CAPACITY = "准牵引总质量";

    /**
     * 备注
     */
    public static final String BAIDU_OCR_REMARKS = "备注";

    /**
     * 证芯编号
     */
    public static final String BAIDU_OCR_CHIP_NUMBER = "证芯编号";
    /**
     * 档案编号
     */
    public static final String BAIDU_OCR_FILE_NUMBER = "档案编号";

    public static final String BAIDU_OCR_ID_NUMBER = "证号";
    public static final String BAIDU_OCR_FIRST_LICENSE_DATE = "初次领证日期";
    public static final String BAIDU_OCR_NATIONALITY = "国籍";
    public static final String DRIVING_LICENSE_TYPE = "准驾车型";
    public static final String BAIDU_OCR_VALIDITY_PERIOD = "有效期限";
    public static final String BAIDU_OCR_VALIDITY_PERIOD2 = "有效期";
    public static final String BAIDU_OCR_UNTIL_DATE = "至";
    public static final String BAIDU_OCR_VALID_START_DATE = "有效起始日期";
    public static final String BAIDU_OCR_STATUS = "状态";
    public static final String BAIDU_OCR_CREATION_TIME = "生成时间";
    public static final String BAIDU_OCR_CURRENT_TIME = "当前时间";
    public static final String BAIDU_OCR_BARCODE_NUMBER = "条形码下编号";
    public static final String BAIDU_OCR_ACCUMULATED_SCORE = "累积记分";
    public static final String BAIDU_OCR_RECORD = "记录";


    /**
     * 组成形式
     */
    public static final String BAIDU_OCR_COMPOSITION_FORM = "组成形式";

    /**
     * 法人
     */
    public static final String BAIDU_OCR_LEGAL_PERSON = "法人";

    /**
     * 证件编号
     */
    public static final String BAIDU_OCR_CERTIFICATE_NUMBER = "证件编号";

    /**
     * 注册资本
     */
    public static final String BAIDU_OCR_REGISTERED_CAPITAL = "注册资本";

    /**
     * 单位名称
     */
    public static final String BAIDU_OCR_COMPANY_NAME = "单位名称";


    /**
     * 社会信用代码
     */
    public static final String BAIDU_OCR_SOCIAL_CREDIT_CODE = "社会信用代码";

    /**
     * 实收资本
     */
    public static final String BAIDU_OCR_PAID_CAPITAL = "实收资本";

    /**
     * 有效期起始日期
     */
    public static final String BAIDU_OCR_VALIDITY_START_DATE = "有效期起始日期";

    /**
     * 核准日期
     */
    public static final String BAIDU_OCR_APPROVAL_DATE = "核准日期";

    /**
     * 成立日期
     */
    public static final String BAIDU_OCR_ESTABLISHMENT_DATE = "成立日期";

    /**
     * 税务登记号
     */
    public static final String BAIDU_OCR_TAX_REGISTRATION_NUMBER = "税务登记号";

    /**
     * 登记机关
     */
    public static final String BAIDU_OCR_REGISTRATION_AUTHORITY = "登记机关";

    /**
     * 类型
     */
    public static final String BAIDU_OCR_TYPE = "类型";

    /**
     * 姓名
     */
    public static final String TRANSPORT_OPERATOR_NAME = "姓名";

    /**
     * 性别
     */
    public static final String TRANSPORT_OPERATOR_GENDER = "性别";

    /**
     * 出生日期
     */
    public static final String TRANSPORT_OPERATOR_BIRTH_DATE = "出生日期";

    /**
     * 国籍
     */
    public static final String TRANSPORT_OPERATOR_NATIONALITY = "国籍";

    /**
     * 住址
     */
    public static final String TRANSPORT_OPERATOR_ADDRESS = "住址";

    /**
     * 证号
     */
    public static final String TRANSPORT_OPERATOR_ID_NUMBER = "证号";

    /**
     * 准驾车型
     */
    public static final String TRANSPORT_OPERATOR_DRIVING_LICENSE_TYPE = "准驾车型";

    /**
     * 从业资格类别
     */
    public static final String TRANSPORT_OPERATOR_QUALIFICATION_CATEGORY = "从业资格类别";

    /**
     * 有效起始日期
     */
    public static final String TRANSPORT_OPERATOR_VALID_START_DATE = "有效起始日期";

    /**
     * 有效期限
     */
    public static final String TRANSPORT_OPERATOR_VALIDITY_PERIOD = "有效期限";

    /**
     * 核发机关
     */
    public static final String TRANSPORT_OPERATOR_ISSUING_AUTHORITY = "核发机关";

    /**
     * 继续教育信息
     */
    public static final String TRANSPORT_OPERATOR_CONTINUING_EDUCATION_INFO = "继续教育信息";

    /**
     * 诚信考核信息
     */
    public static final String TRANSPORT_OPERATOR_INTEGRITY_ASSESSMENT_INFO = "诚信考核信息";

    /*  百度参数结束   */


    /*  佰瓦参数开始   */




    /*  佰瓦参数结束   */

    /**
     * 佰瓦file参数名
     */
    public static final String HUNDREDWATTS_FILE = "file";

    /**
     * 佰瓦base64参数名
     */
    public static final String HUNDREDWATTS_BASE64 = "base64";
    /**
     * 佰瓦imageUrl参数名
     */
    public static final String HUNDREDWATTS_IMAGE_URL = "imageUrl";
    /**
     * 佰瓦type参数名
     */
    public static final String HUNDREDWATTS_TYPE = "type";
    /**
     * 佰瓦cardSide参数名
     */
    public static final String HUNDREDWATTS_CARD_SIDE = "cardSide";
    /**
     * 佰瓦config参数名
     */
    public static final String HUNDREDWATTS_CONFIG = "config";

    /**
     * 佰瓦授权请求头Authorization
     */
    public static final String HUNDREDWATTS_HEADER_AUTHORIZATION = "Authorization";
    /**
     * 佰瓦授权请求头Authorization
     */
    public static final String HUNDREDWATTS_HEADER_CONTENT_TYPE = "Content-Type";

    /**
     * 佰瓦授权Token的redis前缀
     */
    public static final String HUNDREDWATTS_HEADER_REDIS_TOKEN_PREFIX = "HUNDREDWATTS.TOKEN";

    /**
     * 佰瓦success code
     */
    public static final Integer HUNDREDWATTS_SUCCESS_CODE = 200;

    /**
     * 佰瓦车船号
     */
    public static final String HUNDREDWATTS_POUND_BILL_VEHICLE_NO = "车船号";
    /**
     * 佰瓦皮重
     */
    public static final String HUNDREDWATTS_POUND_BILL_TARE_WEIGHT = "皮重";
    /**
     * 佰瓦净重
     */
    public static final String HUNDREDWATTS_POUND_BILL_NET_WEIGHT = "净重";
    /**
     * 佰瓦净重
     */
    public static final String HUNDREDWATTS_POUND_BILL_GROSS_WEIGHT = "毛重";
    /**
     * 佰瓦日期
     */
    public static final String HUNDREDWATTS_POUND_BILL_DATE = "日期";
    /**
     * 佰瓦时间
     */
    public static final String HUNDREDWATTS_POUND_BILL_TIME = "时间";

    /**
     * 银行名
     */
    public static final String HUNDREDWATTS_BANK_CARD_NAME = "机构";
    /**
     * 卡号
     */
    public static final String HUNDREDWATTS_BANK_CARD_NUMBER = "机号";
    /**
     * 有效期
     */
    public static final String HUNDREDWATTS_BANK_CARD_VALID_DATE = "VALID THRU";


    /**
     * 发票代码
     */
    public static final String HUNDREDWATTS_VAT_INVOICE_CODE = "号码";
    /**
     * 发票号码
     */
    public static final String HUNDREDWATTS_VAT_INVOICE_NO = "号码";
    /**
     * 发票日期
     */
    public static final String HUNDREDWATTS_VAT_INVOICE_DATE = "开票日期";
    /**
     * 道路运输证号
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_PERMIT_NUMBER = "交运管字号";

    /**
     * 业户名称
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_CAR_NAME = "业户名称";

    /**
     * 车牌号
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_CAR_NO = "车牌号码";

    /**
     * 车辆类型
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_CAR_TYPE_NAME = "车辆类型";
    /**
     * 车辆载重
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_CAR_LOAD = "吨(座)位";
    /**
     * 核发机关
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_ISSUING_AUTHORITY = "核发机关";
    /**
     * 技术等级评定级别
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_TECHNICAL_RATING_LEVEL = "技术等级评定级别";

    /**
     * 经营许可证
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_BUSINESS_LICENSE = "经营许可证号";

    /**
     * 经营范围
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_BUSINESS_SCOPE = "经营范围";

    /**
     * 车辆尺寸
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_CAR_DIMENSIONS = "车辆尺寸";
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_UNTIL_DATE = "有效期至";
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_VERIFICATION_UNTIL_DATE = "审验有效期至";

    /**
     * 发证日期
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_TIME_DATA = "发证日期";

    /**
     * 地址
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_ADDRESS= "地址";
    /**
     * 核发机关
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_ISSUE_AUTHORITY= "核发机关";
    /**
     * 技术等级评定级别
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_TECHNICAL_RATE_LEVEL = "技术等级评定级别";
    /**
     * 技术等级评定日期
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_TECHNICAL_RATE_DATE = "技术等级评定日期";

    /**
     * 统一社会信用代码
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_SOCIAL_CREDIT_CODE = "统一社会信用代码";
    /**
     * 名称
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_COMPANY_NAME = "名称";
    /**
     * 注册资本
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_REGISTERED_CAPITAL = "注册资本";
    /**
     * 类型
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_TYPE = "类型";
    /**
     * 成立日期
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_ESTABLISHMENT_DATE = "成立日期";
    /**
     * 法定代表人
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_LEGAL_PERSON = "法定代表人";
    /**
     * 住所
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_ADDRESS = "住所";
    /**
     * 经营范围
     */
    public static final String HUNDREDWATTS_BAIDU_OCR_BUSINESS_SCOPE = "经营范围";


    /**
     * 佰瓦-车牌号
     */
    public static final String HUNDREDWATTS_LICENSE_PLATE_NUMBER = "号码";

    /**
     * 佰瓦-有效起始日期
     */
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_START_DATE = "有效起始日期";

    /**
     * 佰瓦-有效期限
     */
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_PERIOD1 = "有效期限";
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_PERIOD2 = "发证机关有效期至";
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_PERIOD3 = "有效期至";

    /**
     * 佰瓦-证号
     */
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NUMBER1 = "证号";
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NUMBER2 = "从业资格证件号";
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NUMBER3 = "从业资格证号";

    /**
     * 佰瓦-姓名
     */
    public static final String HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NAME = "姓名";
    /**
     * 佰瓦-经营许可证号
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_NUMBER = "经营许可证号";
    /**
     * 佰瓦-业户名称
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_OWNER_NAME = "业户名称";
    /**
     * 佰瓦-地址
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_ADDRESS = "地址";
    /**
     * 佰瓦-经营范围
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_BUSINESS_SCOPE = "经营范围";
    /**
     * 佰瓦-发证单位
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_ISSUE_UNIT = "核发机关";
    /**
     * 佰瓦-标题
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_TITLE = "标题";
    /**
     * 佰瓦-性别
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_SEX = "性别";
    /**
     * 佰瓦-出生日期
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_BIRTH_DATE = "出生日期";
    /**
     * 佰瓦-国籍
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_NATIONAL = "国籍";
    /**
     * 佰瓦-住址
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_HOME_ADDRESS = "住址";
    /**
     * 佰瓦-从业资格类别
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_QUALIFICATIONS_TYPE = "从业资格类别";
    /**
     * 佰瓦-核发机关
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_ISSUE_AUTHORITY = "核发机关";
    /**
     * 佰瓦-继续教育信息
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_CONTINUING_EDUCATION_INFORMATION = "继续教育信息";
    /**
     * 佰瓦-诚信考核信息
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_INTEGRITY_ASSESSMENT_INFORMATION = "诚信考核信息";
    /**
     * 佰瓦-机构
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_INSTITUTION = "机构";
    /**
     * 佰瓦-证件有效期
     */
    public static final String HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_VALIDITY_DATE = "证件有效期";

    /**
     * 佰瓦-编  号
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_AIS_IDENTIFICATION_NUMBER = "编  号";

    /**
     * 佰瓦-企业名称
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_BUSINESS_NAME = "企业名称";

    /**
     * 佰瓦-法定代表人
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_LEGAL_PERSON = "法定代表人";

    /**
     * 佰瓦-地    址
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_ADDRESS = "地    址";

    /**
     * 佰瓦-经济类型
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_ECONOMY_TYPE = "经济类型";

    /**
     * 佰瓦-货物运输
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_GOODS_TRANSPORTATION = "货物运输";

    /**
     * 佰瓦-经营期限
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_OPERATION_DURATION = "经营期限";

    /**
     * 佰瓦-批准机关及文号
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_APPROVING_AUTHORITY_DOCUMENT_NUMBER = "批准机关及文号";

    /**
     * 佰瓦-起止日期
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_APPROVING_AUTHORITY_START_AND_END_DATE = "起止日期";
    /**
     * 佰瓦-旅客运输
     */
    public static final String HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_PASSENGER_TRANSPORTATION = "旅客运输";
    /**
     * 佰瓦-编  号
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_NUMBER = "编  号";
    /**
     * 佰瓦-船名/曾用名
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_NAME = "船名/曾用名";
    /**
     * 佰瓦-船籍港
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_FLAG_SHIP_PORT = "船籍港";
    /**
     * 佰瓦-船舶登记号
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_REGISTRATION_NUMBER = "船舶登记号";
    /**
     * 佰瓦-船检登记号
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_INSPECTION_REGISTRATION_NUMBER = "船检登记号";
    /**
     * 佰瓦-船舶所有人
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_OWNER = "船舶所有人";
    /**
     * 佰瓦-船舶经营人
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_OPERATORS = "船舶经营人";
    /**
     * 佰瓦-经营人许可证号码
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_OPERATOR_LICENSE_NUMBER = "经营人许可证号码";
    /**
     * 佰瓦-使用期限
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_VALID_UNTIL = "使用期限";
    /**
     * 佰瓦-发证机关
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_ISSUE_AUTHORITY = "发证机关";
    /**
     * 佰瓦-发证日期
     */
    public static final String HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_ISSUE_DATE = "发证日期";
    /**
     * 佰瓦-标题
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_TITLE = "标题";
    /**
     * 佰瓦-编号
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_NUMBER = "编号";
    /**
     * 佰瓦-船舶名称
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_SHIP_NAME = "船舶名称";
    /**
     * 佰瓦-AIS标识码
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_AIS_IDENTIFICATION_CODE = "AIS标识码";
    /**
     * 佰瓦-单位
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_UNIT = "单位";
    /**
     * 佰瓦-船舶识别号(或初次登记号码)
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_SHIP_IDENTIFICATION_NUMBER = "船舶识别号(或初次登记号码)";
    /**
     * 佰瓦-操作员代码
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_OPERATOR_CODE = "操作员代码";
    /**
     * 佰瓦-AIS设备产品型号
     */
    public static final String HUNDREDWATTS_AIS_EQUIPMENT_PRODUCT_MODEL = "AIS设备产品型号";
    /**
     * 佰瓦-核发日期
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_ISSUANCE_DATE = "核发日期";
    /**
     * 佰瓦-AIS设备产品型号
     */
    public static final String HUNDREDWATTS_AIS_CERTIFICATE_AIS_EQUIPMENT_PRODUCT_MODEL = "AIS设备产品型号";
    /**
     * 佰瓦-增值税发票识别-增值税发票类型
     */
    public static final Integer HUNDREDWATTS_VAT_INVOICE_VAT_INVOICE_TYPE = 3;
    /**
     * 佰瓦-增值税发票识别-全电发票类型
     */
    public static final Integer HUNDREDWATTS_VAT_INVOICE_ALL_ELECTRIC_TYPE = 16;

    /**
     * 佰瓦-船员适任证识别-证书编号
     */
    public static final String HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_CERTIFICATE_NUMBER = "证书编号";

    /**
     * 佰瓦-船员适任证识别-职务资格
     */
    public static final String HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_JOB_QUALIFICATIONS = "职务资格";

    /**
     * 佰瓦-船员适任证识别-签发日期
     */
    public static final String HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_ISSUE_DATE = "签发日期";

    /**
     * 佰瓦-船员适任证识别-截止日期
     */
    public static final String HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_DEADLINE_DATE = "截止日期";

    /**
     * 百度bce bos地址
     */
    public static final String BAIDU_BCE_BOS_HOST = "bcebos.com";

    /**
     * 百度bos process参数名
     */
    public static final String BAIDU_BOS_PROCESS_LABEL = "x-bce-process";

    /**
     * OCR 图片缩放尺寸
     */
    public static final Integer OCR_RESIZE_MAX_HEIGHT = 1600;

    /**
     * OCR 图片缩放尺寸
     */
    public static final Integer OCR_RESIZE_MAX_WIDTH = 1600;

    /**
     * OCR 百度bos process缩放条件
     */
    public static final String OCR_BAIDU_BOS_RESIZE_PROCESS = "image/resize,m_lfit,w_%s,h_%s/format,f_jpg";

    /**
     * 整数 或 整数 + 随机字符 + 整数
     */
    public static final String OCR_NUMBER_FILTER_DIGITAL_REGEX = "\\d+(\\D\\d+)?";

    /**
     * 提取 公斤 kg 斤 等需要转换的单位
     */
    public static final String OCR_NUMBER_FILTER_KG_REGEX = "(公斤|kg|斤|千克)";

}
