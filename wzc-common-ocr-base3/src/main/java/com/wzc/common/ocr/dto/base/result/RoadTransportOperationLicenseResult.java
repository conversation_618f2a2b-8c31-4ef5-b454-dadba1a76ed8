package com.wzc.common.ocr.dto.base.result;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 道路运输许可证结果
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RoadTransportOperationLicenseResult extends OcrBaseResult {

    /**
     * 经营许可证号
     */
    private String licenseNumber;
    /**
     * 业户名称
     */
    private String ownerName;
    /**
     * 地址
     */
    private String address;
    /**
     * 经营范围
     */
    private String businessScope;
    /**
     * 有效期起
     */
    private String validityStartDate;
    /**
     * 有效期止
     */
    private String expirationDate;
    /**
     * 发证日期
     */
    private String issueDate;
    /**
     * 发证单位
     */
    private String issueUnit;

    /**
     * 请求id
     */
    private String requestId;

    //新增字段

    /**
     * 标题
     */
    private String title;
    /**
     * 性别
     */
    private String sex;
    /**
     * 出生日期
     */
    private String birthDate;
    /**
     * 国籍
     */
    private String national;
    /**
     * 住址
     */
    private String homeAddress;
    /**
     * 从业资格类别
     */
    private String qualificationsType;
    /**
     * 核发机关
     */
    private String issueAuthority;
    /**
     * 继续教育信息
     */
    private String continuingEducationInformation;
    /**
     * 诚信考核信息
     */
    private String integrityAssessmentInformation;
    /**
     * 机构
     */
    private String institution;

}
