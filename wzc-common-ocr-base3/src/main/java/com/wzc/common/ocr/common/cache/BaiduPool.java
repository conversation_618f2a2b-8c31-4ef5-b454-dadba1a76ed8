package com.wzc.common.ocr.common.cache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设置佰瓦和原有百度ocr返回值的映射
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
public class BaiduPool {

    public static final Map<Integer, String> ID_CARD_RESPONSE_POOL = new HashMap<>();

    public static final List<Integer> ID_CARD_LIST = new ArrayList();


    // 私有构造函数，用于隐藏默认的公共构造函数
    private BaiduPool() {
        // 在私有构造函数中可以执行一些初始化操作
    }

    static {
        ID_CARD_LIST.add(-1);
        ID_CARD_LIST.add(0);
        ID_CARD_LIST.add(2);
        ID_CARD_LIST.add(3);
        ID_CARD_LIST.add(4);
        ID_CARD_RESPONSE_POOL.put(-1, "身份证正面所有字段全为空");
        ID_CARD_RESPONSE_POOL.put(0, "身份证证号不合法，此情况下不返回身份证证号");
        ID_CARD_RESPONSE_POOL.put(2, "身份证证号和性别、出生信息都不一致");
        ID_CARD_RESPONSE_POOL.put(3, "身份证证号和出生信息不一致");
        ID_CARD_RESPONSE_POOL.put(4, "身份证证号和性别信息不一致");
    }

}
