package com.wzc.common.ocr.dto.base.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.io.Serializable;

/**
 * OCR通用识别请求参数
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@Data
public class OcrInputStreamReq extends OcrReq implements Serializable {

    /**
     * 图片输入流
     */
    @NotNull(message = "图片不能为空")
    private InputStream inputStream;


}
