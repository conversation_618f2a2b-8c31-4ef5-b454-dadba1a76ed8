package com.wzc.common.ocr.convert;/**
 * Create By hjh on 2023/10/26
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.NumberChineseFormatter;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wzc.common.ocr.common.cache.HundredWattsPool;
import com.wzc.common.ocr.common.constant.OcrConstants;
import com.wzc.common.ocr.common.enums.IdCardClearEnum;
import com.wzc.common.ocr.common.enums.IdCardCompleteEnum;
import com.wzc.common.ocr.common.enums.VatinvoiceTypeEnum;
import com.wzc.common.ocr.dto.base.request.OcrBaiduReq;
import com.wzc.common.ocr.dto.base.request.OcrBase64Req;
import com.wzc.common.ocr.dto.base.result.*;
import com.wzc.common.ocr.dto.supplier.baidu.*;
import com.wzc.common.ocr.dto.supplier.hundredwatts.*;
import com.wzc.common.ocr.dto.supplier.timecapsule.*;
import com.wzc.common.ocr.pdf.model.Invoice;
import com.wzc.common.ocr.util.DateUtil;
import com.wzc.common.ocr.util.OcrFilterUtil;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.Objects.isNull;

/**
 *@ClassName AiOcrConvert
 *@Description 通用ocr转换类
 *<AUTHOR>
 *@Date 2023/10/26 20:17
 *@Version 1.0
 **/
public class OcrConvert {
    public static IdCardResult hundredWattsIdCardResultToIdCardResult(HundredWattsIdCardResult hundredWattsIdCardResult) {
        if (hundredWattsIdCardResult == null) {
            return null;
        }

        IdCardResult idCardResult = new IdCardResult();
        idCardResult.setRequestId(hundredWattsIdCardResult.getRequestId());
        idCardResult.setName(hundredWattsIdCardResult.getName());
        idCardResult.setNation(hundredWattsIdCardResult.getNation());
        idCardResult.setAddress(hundredWattsIdCardResult.getAddress());
        idCardResult.setGender(hundredWattsIdCardResult.getSex());
        if (StrUtil.isNotBlank(hundredWattsIdCardResult.getBirth())) {
            String birthDate = DateUtil.formatDate(hundredWattsIdCardResult.getBirth(), DateUtil.DATE_FORMAT_PATTERN1, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setBirthDate(birthDate);
        }
        idCardResult.setIdCardNo(hundredWattsIdCardResult.getIdNum());
        String validDate = hundredWattsIdCardResult.getValidDate();
        if (StrUtil.isNotBlank(validDate)) {
            String[] split = validDate.split("-");
            if (split.length > 1) {
                idCardResult.setIssueDate(split[0]);
                String issueDate = DateUtil.formatDate(split[0], DateUtil.DATE_FORMAT_PATTERN3, DateUtil.DATE_FORMAT_PATTERN2);
                if (StrUtil.isNotBlank(issueDate)) {
                    idCardResult.setIssueDateFormat(issueDate);
                }
                if (!DateUtil.OCR_ID_CARD_EXPIRATION_DATE_LONG_TERM.equals(split[1])) {
                    idCardResult.setExpirationDate(split[1]);
                    String expirationDate = DateUtil.formatDate(split[1], DateUtil.DATE_FORMAT_PATTERN3, DateUtil.DATE_FORMAT_PATTERN2);
                    idCardResult.setExpirationDateFormat(expirationDate);
                } else {
                    idCardResult.setExpirationDate(split[1]);
                }
            }
        }
        idCardResult.setIssuingAuthority(hundredWattsIdCardResult.getAuthority());
        String advancedInfo = hundredWattsIdCardResult.getAdvancedInfo();
        HundredWattsIdCardAdvancedInfo advancedInfoBean = JSONUtil.toBean(advancedInfo, HundredWattsIdCardAdvancedInfo.class);
        if (ObjectUtil.isNotEmpty(advancedInfoBean)) {
            List<Integer> warnInfos = advancedInfoBean.getWarnInfos();
            if (CollUtil.isNotEmpty(warnInfos)) {
                //佰瓦返回监控指标集合，原有业务只返回一个，所以取第一个
                Integer warnInfo = warnInfos.get(0);
                String riskType = HundredWattsPool.ID_CARD_RESPONSE_POOL.get(warnInfo);
                if (ObjectUtil.isNotEmpty(riskType)) {
                    idCardResult.setRiskType(riskType);
                } else {
                    //无告警信息，则是正常身份证
                    idCardResult.setRiskType(HundredWattsPool.ID_CARD_RESPONSE_POOL.get(HundredWattsPool.ID_CARD_NORMAL));
                }
                String imageStatus = HundredWattsPool.ID_CARD_IMAGE_STATAS_RESPONSE_POOL.get(warnInfo);
                if (ObjectUtil.isNotEmpty(imageStatus)) {
                    idCardResult.setImageStatus(imageStatus);
                } else {
                    //无告警信息，则是正常身份证
                    idCardResult.setImageStatus(HundredWattsPool.ID_CARD_IMAGE_STATAS_RESPONSE_POOL.get(HundredWattsPool.ID_CARD_NORMAL));
                }

            } else {
                idCardResult.setRiskType(HundredWattsPool.ID_CARD_RESPONSE_POOL.get(HundredWattsPool.ID_CARD_NORMAL));
                idCardResult.setImageStatus(HundredWattsPool.ID_CARD_IMAGE_STATAS_RESPONSE_POOL.get(HundredWattsPool.ID_CARD_NORMAL));
            }
            IdCardQuantity cardQuantity = new IdCardQuantity();
            Integer quality = advancedInfoBean.getQuality();
            if (ObjectUtil.isNotEmpty(quality)) {
                cardQuantity.setIsClearPropobility(String.valueOf(quality));
            }
            if (quality > HundredWattsPool.ID_CARD_IS_CLEAR_FRACTION) {
                cardQuantity.setIsClear(IdCardClearEnum.YES.getCode());
            } else {
                cardQuantity.setIsClear(IdCardClearEnum.NO.getCode());
            }
            Integer borderCodeValue = advancedInfoBean.getBorderCodeValue();
            if (ObjectUtil.isNotEmpty(borderCodeValue)) {
                cardQuantity.setIsCompletePropobility(String.valueOf(borderCodeValue));
            }
            if (quality > HundredWattsPool.ID_CARD_IS_COMPLETE_FRACTION) {
                cardQuantity.setIsComplete(IdCardCompleteEnum.YES.getCode());
            } else {
                cardQuantity.setIsComplete(IdCardCompleteEnum.NO.getCode());
            }
            idCardResult.setCardQuality(cardQuantity);
        }

        return idCardResult;
    }

    public static PoundBillResult hundredWattsPoundBillResultToPoundBillResult(HundredWattsPoundBillResult hundredWattsPoundBillResult) {
        if (hundredWattsPoundBillResult == null) {
            return null;
        }

        HundredWattsPoundBillSingleResult single = hundredWattsPoundBillResult.getSingle();
        if (ObjectUtil.isEmpty(single)) {
            return null;
        }

        PoundBillResult poundBillResult = new PoundBillResult();
        poundBillResult.setRequestId(hundredWattsPoundBillResult.getRequestId());
        poundBillResult.setVehicleNo(getPoundBillResultValue(single.getVehicleNo()));
        String tareWeight = getPoundBillResultValue(single.getTareWeight());
        poundBillResult.setTareWeight(tareWeight);
        if (StrUtil.isNotBlank(tareWeight)){
            tareWeight = OcrFilterUtil.filterTonWeightNumber(tareWeight);
            tareWeight = devicePoundBillWeight(tareWeight);
            poundBillResult.setTareWeightFormat(tareWeight);
        }
        String netWeight = getPoundBillResultValue(single.getNetWeight());
        poundBillResult.setNetWeight(netWeight);
        if (StrUtil.isNotBlank(netWeight)){
            netWeight = OcrFilterUtil.filterTonWeightNumber(netWeight);
            netWeight = devicePoundBillWeight(netWeight);
            poundBillResult.setNetWeightFormat(netWeight);
        }
        String grossWeight = getPoundBillResultValue(single.getGrossWeight());
        poundBillResult.setGrossWeight(grossWeight);
        if (StrUtil.isNotBlank(grossWeight)){
            grossWeight = OcrFilterUtil.filterTonWeightNumber(grossWeight);
            grossWeight = devicePoundBillWeight(grossWeight);
            poundBillResult.setGrossWeightFormat(grossWeight);
        }

        String dateStr = getPoundBillResultValue(single.getDate());
        if (StringUtil.isNotBlank(dateStr)) {
            dateStr = dateStr.replaceAll("\\\\n", "");
            //字符串包含年，说明是dateStr是xx年xx月xx日格式的，需要转换格式为xx-xx-xx
            if (dateStr.contains("年")) {
                String date = DateUtil.formatDate(dateStr, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                if (StrUtil.isNotBlank(date)) {
                    poundBillResult.setDate(date);
                }
            } else {
                poundBillResult.setDate(dateStr);
            }
        }
        String timeStr = getPoundBillResultValue(single.getTime());
        //手写体出现将时间识别为年月日的问题进行排除
        if (StringUtil.isNotBlank(timeStr) && !timeStr.contains("月")) {
            poundBillResult.setTime(timeStr);
        }


        return poundBillResult;
    }

    private static String devicePoundBillWeight(String weight){
        BigDecimal bigDecimal = new BigDecimal(weight);
        if (bigDecimal.compareTo(new BigDecimal(10000)) > 0){
            return String.valueOf(bigDecimal.divide(new BigDecimal(1000)).setScale(20, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString());
        }
        return weight;
    }

    private static String getPoundBillResultValue(HundredWattsPoundBillSingleBaseResult hundredWattsPoundBillSingleBaseResult) {
        if (ObjectUtil.isEmpty(hundredWattsPoundBillSingleBaseResult)) {
            return "";
        }
        return hundredWattsPoundBillSingleBaseResult.getValue();
    }

    public static PoundBillResult timecapsulePoundBillResultToPoundBillResult(TimecapsulePoundBillResult timecapsulePoundBillResult) {
        if (timecapsulePoundBillResult == null) {
            return null;
        }
        PoundBillResult poundBillResult = new PoundBillResult();
        poundBillResult.setDate(timecapsulePoundBillResult.getDate());
        poundBillResult.setTime(timecapsulePoundBillResult.getTime());
        poundBillResult.setVehicleNo(timecapsulePoundBillResult.getCarlicense());
        poundBillResult.setTareWeight(timecapsulePoundBillResult.getTareweight());
        poundBillResult.setGrossWeight(timecapsulePoundBillResult.getGrossweight());
        poundBillResult.setNetWeight(timecapsulePoundBillResult.getNetweight());

        return poundBillResult;
    }


    public static IdCardResult timecapsuleIdCardResultToIdCardResult(TimecapsuleIdCardResult timecapsuleIdCardResult) {
        if (timecapsuleIdCardResult == null) {
            return null;
        }
        IdCardResult idCardResult = new IdCardResult();
        idCardResult.setName(timecapsuleIdCardResult.getName());
        idCardResult.setGender(timecapsuleIdCardResult.getGender());
        idCardResult.setNation(timecapsuleIdCardResult.getNation());
        idCardResult.setIdCardNo(timecapsuleIdCardResult.getIdCardNo());
        idCardResult.setAddress(timecapsuleIdCardResult.getAddress());
        idCardResult.setIssuingAuthority(timecapsuleIdCardResult.getIssuingAuthority());
        String birthDate = timecapsuleIdCardResult.getBirthDate();
        if (StrUtil.isNotBlank(birthDate)) {
            birthDate = DateUtil.formatDate(birthDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setBirthDate(birthDate);
        }
        String expirationDate = timecapsuleIdCardResult.getExpirationDate();
        if (StrUtil.isNotBlank(expirationDate)) {
            idCardResult.setExpirationDate(expirationDate);
            expirationDate = DateUtil.formatDate(expirationDate, DateUtil.DATE_FORMAT_PATTERN3, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setExpirationDateFormat(expirationDate);
        }

        String issueDate = timecapsuleIdCardResult.getIssueDate();
        if (StrUtil.isNotBlank(issueDate)) {
            idCardResult.setIssueDate(issueDate);
            issueDate = DateUtil.formatDate(issueDate, DateUtil.DATE_FORMAT_PATTERN3, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setIssueDateFormat(issueDate);
        }
        //泰普科没有这两个字段，赋值默认值
        idCardResult.setRiskType(HundredWattsPool.ID_CARD_RESPONSE_POOL.get(HundredWattsPool.ID_CARD_NORMAL));
        idCardResult.setImageStatus(HundredWattsPool.ID_CARD_IMAGE_STATAS_RESPONSE_POOL.get(HundredWattsPool.ID_CARD_NORMAL));
        return idCardResult;
    }


    public static BankCardResult hundredWattsBankCardResultToBankCardResult(HundredWattsBankCardResult hundredWattsBankCardResult) {
        if (hundredWattsBankCardResult == null) {
            return null;
        }
        BankCardResult bankCardResult = new BankCardResult();
        bankCardResult.setRequestId(hundredWattsBankCardResult.getRequestId());
        bankCardResult.setBankCardNumber(hundredWattsBankCardResult.getCardNo());

        String validDate = hundredWattsBankCardResult.getValidDate();
        if (StringUtil.isNotBlank(validDate)) {
            validDate = DateUtil.formatDate(validDate, DateUtil.DATE_FORMAT_PATTERN5, DateUtil.DATE_FORMAT_PATTERN6);
            bankCardResult.setValidDate(validDate);
        }
        bankCardResult.setBankCardType(hundredWattsBankCardResult.getCardType());
        String bankInfo = hundredWattsBankCardResult.getBankInfo();
        //截取(之前的银行名称
        String bankInfoFormat = bankInfo.substring(0, bankInfo.indexOf("("));
        bankCardResult.setBankName(bankInfoFormat);

        return bankCardResult;
    }


    public static VatInvoiceResult hundredWattsVatInvoiceResultToVatInvoiceResult(HundredWattsVatInvoiceResult hundredWattsVatInvoiceResult) {
        if (hundredWattsVatInvoiceResult == null) {
            return null;
        }
        VatInvoiceResult vatInvoiceResult = new VatInvoiceResult();

        vatInvoiceResult.setRequestId(hundredWattsVatInvoiceResult.getRequestId());

        List<HundredWattsVatInvoiceMixedInvoiceItems> mixedInvoiceItems = hundredWattsVatInvoiceResult.getMixedInvoiceItems();
        if (CollUtil.isEmpty(mixedInvoiceItems)) {
            return null;
        }

        String total = "0";
        for (HundredWattsVatInvoiceMixedInvoiceItems mixedInvoiceItem : mixedInvoiceItems) {
            HundredWattsVatInvoiceSingleInvoiceInfos singleInvoiceInfos = mixedInvoiceItem.getSingleInvoiceInfos();
            if (ObjectUtil.isEmpty(singleInvoiceInfos)) {
                continue;
            }
            HundredWattsVatInvoiceVatInvoiceInfo vatElectronicSpecialInvoiceFull = singleInvoiceInfos.getVatElectronicSpecialInvoiceFull();
            if (ObjectUtil.isEmpty(vatElectronicSpecialInvoiceFull) || StrUtil.isBlank(vatElectronicSpecialInvoiceFull.getTotal())) {
                continue;
            }
            if (new BigDecimal(vatElectronicSpecialInvoiceFull.getTotal()).compareTo(new BigDecimal(total)) > 0) {
                total = vatElectronicSpecialInvoiceFull.getTotal();
            }
        }

        String subType = mixedInvoiceItems.get(0).getSubType();
        if(StrUtil.isNotBlank(subType)){
            if ("VatCommonInvoice".equals(subType)){
                vatInvoiceResult.setType(VatinvoiceTypeEnum.PAPER_GENERAL_VAT_INVOICE.getCode());
            } else if ("VatSpecialInvoice".equals(subType)) {
                vatInvoiceResult.setType(VatinvoiceTypeEnum.PAPER_VAT_INVOICE.getCode());
            } else if ("VatElectronicSpecialInvoiceFull".equals(subType)) {
                vatInvoiceResult.setType(VatinvoiceTypeEnum.ELECTRONIC_INVOICES_SPECIAL_VAT_INVOICES.getCode());
            } else if ("VatElectronicInvoiceFull".equals(subType)) {
                vatInvoiceResult.setType(VatinvoiceTypeEnum.ELECTRONIC_INVOICE_FREE_LIFE.getCode());
            }
        }

        HundredWattsVatInvoiceSingleInvoiceInfos singleInvoiceInfos = mixedInvoiceItems.get(0).getSingleInvoiceInfos();
        if (ObjectUtil.isEmpty(singleInvoiceInfos)) {
            return null;
        }

        HundredWattsVatInvoiceVatInvoiceInfo vatInvoiceInfo = null;

        HundredWattsVatInvoiceVatInvoiceInfo vatSpecialInvoice = singleInvoiceInfos.getVatSpecialInvoice();
        if (ObjectUtil.isNotEmpty(vatSpecialInvoice)) {
            vatInvoiceInfo = vatSpecialInvoice;
        }

        HundredWattsVatInvoiceVatInvoiceInfo vatCommonInvoice = singleInvoiceInfos.getVatCommonInvoice();

        if (ObjectUtil.isNotEmpty(vatCommonInvoice)) {
            vatInvoiceInfo = vatCommonInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatElectronicCommonInvoice = singleInvoiceInfos.getVatElectronicCommonInvoice();

        if (ObjectUtil.isNotEmpty(vatElectronicCommonInvoice)) {
            vatInvoiceInfo = vatElectronicCommonInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatElectronicSpecialInvoice = singleInvoiceInfos.getVatElectronicSpecialInvoice();

        if (ObjectUtil.isNotEmpty(vatElectronicSpecialInvoice)) {
            vatInvoiceInfo = vatElectronicSpecialInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatElectronicInvoiceBlockchain = singleInvoiceInfos.getVatElectronicInvoiceBlockchain();

        if (ObjectUtil.isNotEmpty(vatElectronicInvoiceBlockchain)) {
            vatInvoiceInfo = vatElectronicInvoiceBlockchain;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatElectronicInvoiceToll = singleInvoiceInfos.getVatElectronicInvoiceToll();

        if (ObjectUtil.isNotEmpty(vatElectronicInvoiceToll)) {
            vatInvoiceInfo = vatElectronicInvoiceToll;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatSalesList = singleInvoiceInfos.getVatSalesList();

        if (ObjectUtil.isNotEmpty(vatSalesList)) {
            vatInvoiceInfo = vatSalesList;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatElectronicSpecialInvoiceFull = singleInvoiceInfos.getVatElectronicSpecialInvoiceFull();

        if (ObjectUtil.isNotEmpty(vatElectronicSpecialInvoiceFull)) {
            vatInvoiceInfo = vatElectronicSpecialInvoiceFull;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatElectronicInvoiceFull = singleInvoiceInfos.getVatElectronicInvoiceFull();

        if (ObjectUtil.isNotEmpty(vatElectronicInvoiceFull)) {
            vatInvoiceInfo = vatElectronicInvoiceFull;
        }
        HundredWattsVatInvoiceVatInvoiceInfo electronicFlightTicketFull = singleInvoiceInfos.getElectronicFlightTicketFull();

        if (ObjectUtil.isNotEmpty(electronicFlightTicketFull)) {
            vatInvoiceInfo = electronicFlightTicketFull;
        }
        HundredWattsVatInvoiceVatInvoiceInfo electronicTrainTicketFull = singleInvoiceInfos.getElectronicTrainTicketFull();

        if (ObjectUtil.isNotEmpty(electronicTrainTicketFull)) {
            vatInvoiceInfo = electronicTrainTicketFull;
        }
        HundredWattsVatInvoiceVatInvoiceInfo motorVehicleSaleInvoice = singleInvoiceInfos.getMotorVehicleSaleInvoice();

        if (ObjectUtil.isNotEmpty(motorVehicleSaleInvoice)) {
            vatInvoiceInfo = motorVehicleSaleInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo vatInvoiceRoll = singleInvoiceInfos.getVatInvoiceRoll();

        if (ObjectUtil.isNotEmpty(vatInvoiceRoll)) {
            vatInvoiceInfo = vatInvoiceRoll;
        }
        HundredWattsVatInvoiceVatInvoiceInfo taxiTicket = singleInvoiceInfos.getTaxiTicket();

        if (ObjectUtil.isNotEmpty(taxiTicket)) {
            vatInvoiceInfo = taxiTicket;
        }
        HundredWattsVatInvoiceVatInvoiceInfo quotaInvoice = singleInvoiceInfos.getQuotaInvoice();

        if (ObjectUtil.isNotEmpty(quotaInvoice)) {
            vatInvoiceInfo = quotaInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo trainTicket = singleInvoiceInfos.getTrainTicket();

        if (ObjectUtil.isNotEmpty(trainTicket)) {
            vatInvoiceInfo = trainTicket;
        }
        HundredWattsVatInvoiceVatInvoiceInfo airTransport = singleInvoiceInfos.getAirTransport();

        if (ObjectUtil.isNotEmpty(airTransport)) {
            vatInvoiceInfo = airTransport;
        }
        HundredWattsVatInvoiceVatInvoiceInfo machinePrintedInvoice = singleInvoiceInfos.getMachinePrintedInvoice();

        if (ObjectUtil.isNotEmpty(machinePrintedInvoice)) {
            vatInvoiceInfo = machinePrintedInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo busInvoice = singleInvoiceInfos.getBusInvoice();

        if (ObjectUtil.isNotEmpty(busInvoice)) {
            vatInvoiceInfo = busInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo shippingInvoice = singleInvoiceInfos.getShippingInvoice();

        if (ObjectUtil.isNotEmpty(shippingInvoice)) {
            vatInvoiceInfo = shippingInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo nonTaxIncomeGeneralBill = singleInvoiceInfos.getNonTaxIncomeGeneralBill();

        if (ObjectUtil.isNotEmpty(nonTaxIncomeGeneralBill)) {
            vatInvoiceInfo = nonTaxIncomeGeneralBill;
        }
        HundredWattsVatInvoiceVatInvoiceInfo tollInvoice = singleInvoiceInfos.getTollInvoice();

        if (ObjectUtil.isNotEmpty(tollInvoice)) {
            vatInvoiceInfo = tollInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo medicalOutpatientInvoice = singleInvoiceInfos.getMedicalOutpatientInvoice();

        if (ObjectUtil.isNotEmpty(medicalOutpatientInvoice)) {
            vatInvoiceInfo = medicalOutpatientInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo medicalHospitalizedInvoice = singleInvoiceInfos.getMedicalHospitalizedInvoice();

        if (ObjectUtil.isNotEmpty(medicalHospitalizedInvoice)) {
            vatInvoiceInfo = medicalHospitalizedInvoice;
        }
        HundredWattsVatInvoiceVatInvoiceInfo otherInvoice = singleInvoiceInfos.getOtherInvoice();

        if (ObjectUtil.isNotEmpty(otherInvoice)) {
            vatInvoiceInfo = otherInvoice;
        }
        vatInvoiceResult.setInvoiceCode(vatInvoiceInfo.getCode());
        vatInvoiceResult.setInvoiceNum(vatInvoiceInfo.getNumber());
        vatInvoiceResult.setTicketCollectionUnit(OcrFilterUtil.bracketTransChinese(vatInvoiceInfo.getBuyer()));
        vatInvoiceResult.setReceiveUnitTaxpayerIdentificationNumber(vatInvoiceInfo.getBuyerTaxID());
        vatInvoiceResult.setTicketReceiveAddress(vatInvoiceInfo.getBuyerAddrTel());
        if ("0".equals(total)) {
            vatInvoiceResult.setInvoiceAmount(vatInvoiceInfo.getTotal());
        } else {
            vatInvoiceResult.setInvoiceAmount(total);
        }
        vatInvoiceResult.setTax(vatInvoiceInfo.getTax());
        vatInvoiceResult.setInvoiceUnit(OcrFilterUtil.bracketTransChinese(vatInvoiceInfo.getSeller()));
        vatInvoiceResult.setInvoiceUnitTaxpayerIdentificationNumber(vatInvoiceInfo.getSellerTaxID());
        vatInvoiceResult.setBillAddress(vatInvoiceInfo.getSellerAddrTel());
        vatInvoiceResult.setIssuer(vatInvoiceInfo.getIssuer());
        vatInvoiceResult.setReviewer(vatInvoiceInfo.getReviewer());
        vatInvoiceResult.setProvince(vatInvoiceInfo.getProvince());
        vatInvoiceResult.setReceiptor(vatInvoiceInfo.getReceiptor());
        vatInvoiceResult.setCheckCode(vatInvoiceInfo.getCheckCode());
        vatInvoiceResult.setRemark(vatInvoiceInfo.getRemark());
        vatInvoiceResult.setOilMark(vatInvoiceInfo.getOilMark());
        vatInvoiceResult.setKind(vatInvoiceInfo.getKind());
        vatInvoiceResult.setQrCodeMark(vatInvoiceInfo.getQRCodeMark());
        vatInvoiceResult.setAgentMark(vatInvoiceInfo.getAgentMark());
        vatInvoiceResult.setSellerBankAccount(vatInvoiceInfo.getSellerBankAccount());
        vatInvoiceResult.setFormName(vatInvoiceInfo.getFormName());
        vatInvoiceResult.setBlockChainMark(vatInvoiceInfo.getFormName());
        vatInvoiceResult.setBuyerBankAccount(vatInvoiceInfo.getBuyerBankAccount());
        vatInvoiceResult.setCompanySealContent(vatInvoiceInfo.getCompanySealContent());
//        vatInvoiceResult.setName(vatInvoiceInfo.getname());
        vatInvoiceResult.setFormType(vatInvoiceInfo.getFormType());
        vatInvoiceResult.setTitle(vatInvoiceInfo.getTitle());
        vatInvoiceResult.setTotalCn(vatInvoiceInfo.getTotalCn());
        vatInvoiceResult.setAcquisitionMark(vatInvoiceInfo.getAcquisitionMark());
        vatInvoiceResult.setTransitMark(vatInvoiceInfo.getTransitMark());
        vatInvoiceResult.setElectronicFullMark(vatInvoiceInfo.getElectronicFullMark());
        vatInvoiceResult.setCiphertext(vatInvoiceInfo.getCiphertext());
        vatInvoiceResult.setCompanySealMark(vatInvoiceInfo.getCompanySealMark());

        String invoiceDate = vatInvoiceInfo.getDate();
        if (StringUtil.isNotBlank(invoiceDate)) {
            invoiceDate = DateUtil.formatDate(invoiceDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            vatInvoiceResult.setInvoiceDate(invoiceDate);
        }

        List<HundredWattsVatInvoiceVatVatInvoiceItemInfos> vatInvoiceItemInfoList = vatInvoiceInfo.getVatInvoiceItemInfos();
        if (CollUtil.isNotEmpty(vatInvoiceItemInfoList)) {
            String taxRate = vatInvoiceItemInfoList.get(0).getTaxRate();
            if (StrUtil.isNotBlank(taxRate)) {
                //客户端税率不需要展示%
                vatInvoiceResult.setTaxRate(taxRate.replace("%", ""));
            }
        } else {
            List<HundredWattsVatInvoiceVatElectronicItems> vatElectronicItems = vatInvoiceInfo.getVatElectronicItems();
            if (CollUtil.isNotEmpty(vatElectronicItems)) {
                String taxRate = "0";
                for (HundredWattsVatInvoiceVatElectronicItems vatElectronicItem : vatElectronicItems) {
                    if (StrUtil.isBlank(vatElectronicItem.getTaxRate())) {
                        continue;
                    }
                    taxRate = vatElectronicItem.getTaxRate();
                }
                vatInvoiceResult.setTaxRate(taxRate.replace("%", ""));
            }
        }
        return vatInvoiceResult;
    }


    public static RoadTransportCertResult hundredWattsRoadTransportCertResultToRoadTransportCertResult(HundredWattsRoadTransportCertResult hundredWattsRoadTransportCertResult) {
        if (hundredWattsRoadTransportCertResult == null) {
            return null;
        }
        RoadTransportCertResult roadTransportCertResult = new RoadTransportCertResult();

        roadTransportCertResult.setRequestId(hundredWattsRoadTransportCertResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsRoadTransportCertResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();

            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_PERMIT_NUMBER.equals(autoName)) {
                if (StrUtil.isNotBlank(autoContent)) {
                    autoContent = autoContent.replaceAll("[\\u4e00-\\u9fa5]", "");
                    roadTransportCertResult.setRoadTransportPermitNumber(autoContent.trim());
                }

            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_CAR_NAME.equals(autoName)) {
                roadTransportCertResult.setCarName(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_CAR_NO.equals(autoName)) {
                roadTransportCertResult.setCarNo(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_CAR_TYPE_NAME.equals(autoName)) {
                roadTransportCertResult.setCarTypeName(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_CAR_LOAD.equals(autoName)) {
                roadTransportCertResult.setCarLoad(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_BUSINESS_LICENSE.equals(autoName)) {
                roadTransportCertResult.setBusinessLicense(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_BUSINESS_SCOPE.equals(autoName)) {
                roadTransportCertResult.setBusinessScope(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_ISSUING_AUTHORITY.equals(autoName)){
                roadTransportCertResult.setIssueAuthority(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_TECHNICAL_RATING_LEVEL.equals(autoName)){
                roadTransportCertResult.setTechnicalRateLevel(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_UNTIL_DATE.equals(autoName)) {
                roadTransportCertResult.setValidUntilDate(autoContent);
                if (StrUtil.isNotBlank(autoContent)) {
                    autoContent = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                    roadTransportCertResult.setValidUntilDateFormat(autoContent);
                }
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_VERIFICATION_UNTIL_DATE.equals(autoName)) {
                roadTransportCertResult.setVerificationUntilDate(autoContent);
                if (StrUtil.isNotBlank(autoContent)) {
                    autoContent = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN7, DateUtil.DATE_FORMAT_PATTERN8);
                    roadTransportCertResult.setVerificationUntilDateFormat(autoContent);
                }
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_CAR_DIMENSIONS.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    String[] carSizeArray = {};
                    //针对返回的长宽高分割设值
                    if (autoContent.contains("毫米")) {
                        autoContent = autoContent.replace("长", "").replace("宽", "").replace("高", "");
                        carSizeArray = autoContent.split("毫米");
                    } else {
                        carSizeArray = autoContent.split("x");
                    }
                    if (carSizeArray.length > 2) {
                        roadTransportCertResult.setCarLength(carSizeArray[0]);
                        String carLength = OcrFilterUtil.filterNumber(carSizeArray[0]);
                        roadTransportCertResult.setCarLengthFormat(carLength);
                        roadTransportCertResult.setCarWidth(carSizeArray[1]);
                        String carWidth = OcrFilterUtil.filterNumber(carSizeArray[1]);
                        roadTransportCertResult.setCarWidthFormat(carWidth);
                        roadTransportCertResult.setCarHeight(carSizeArray[2]);
                        String carHeight = OcrFilterUtil.filterNumber(carSizeArray[2]);
                        roadTransportCertResult.setCarHeightFormat(carHeight);
                    }

                }
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_TIME_DATA.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    roadTransportCertResult.setTimeData(autoContent);
                    String timeData = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                    roadTransportCertResult.setTimeDataFormat(timeData);
                }
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_ADDRESS.equals(autoName)){
                roadTransportCertResult.setAddress(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_ISSUE_AUTHORITY.equals(autoName)){
                roadTransportCertResult.setIssueAuthority(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_TECHNICAL_RATE_DATE.equals(autoName)){
                roadTransportCertResult.setTechnicalRateDate(autoContent.trim());
            }
        }
        return roadTransportCertResult;
    }


    public static VehicleLicenseResult hundredWattsVehicleLicenseResultToVehicleLicenseResult(HundredWattsVehicleLicenseResult hundredWattsVehicleLicenseResult) {
        if (hundredWattsVehicleLicenseResult == null) {
            return null;
        }
        VehicleLicenseResult vehicleLicenseResult = new VehicleLicenseResult();

        vehicleLicenseResult.setRequestId(hundredWattsVehicleLicenseResult.getRequestId());

        HundredWattsFrontInfoResult frontInfo = hundredWattsVehicleLicenseResult.getFrontInfo();
        HundredWattsBackInfoResult backInfo = hundredWattsVehicleLicenseResult.getBackInfo();
        if (ObjectUtil.isNotEmpty(frontInfo)) {
            vehicleLicenseResult.setVehicleNo(frontInfo.getPlateNo());
            vehicleLicenseResult.setVehicleIdentificationNumber(frontInfo.getVin());
            vehicleLicenseResult.setEngineNumber(frontInfo.getEngineNo());
            vehicleLicenseResult.setOwner(frontInfo.getOwner());
            vehicleLicenseResult.setUsage(frontInfo.getUseCharacter());
            vehicleLicenseResult.setVehicleType(frontInfo.getVehicleType());
            vehicleLicenseResult.setBrandModel(frontInfo.getModel());
            vehicleLicenseResult.setAddress(frontInfo.getAddress());
            vehicleLicenseResult.setIssuingAuthority(frontInfo.getSeal());
            vehicleLicenseResult.setIssuingDate(frontInfo.getIssueDate());
            vehicleLicenseResult.setIssuingDateFormat(frontInfo.getIssueDate());
            vehicleLicenseResult.setRegistrationDate(frontInfo.getRegisterDate());
            vehicleLicenseResult.setRegistrationDateFormat(frontInfo.getRegisterDate());
        }
        if (ObjectUtil.isNotEmpty(backInfo)) {
            vehicleLicenseResult.setVehicleNo(backInfo.getPlateNo());
            vehicleLicenseResult.setFileNumber(backInfo.getFileNo());
            vehicleLicenseResult.setApprovedPassengers(backInfo.getAllowNum());
            String totalMass = backInfo.getTotalMass();
            if (StrUtil.isBlank(totalMass) || "--".equals(totalMass)) {
                totalMass = backInfo.getLooadQuality();
                if (StrUtil.isBlank(totalMass) || "--".equals(totalMass)) {
                    totalMass = backInfo.getTotalQuasiMass();
                }
            }
            vehicleLicenseResult.setGrossWeight(totalMass);
            vehicleLicenseResult.setCurbWeight(backInfo.getCurbWeight());
            String looadQuality = backInfo.getLooadQuality();
            if (StrUtil.isBlank(looadQuality) || "--".equals(looadQuality)) {
                looadQuality = backInfo.getTotalQuasiMass();
                if (StrUtil.isBlank(looadQuality) || "--".equals(looadQuality)) {
                    looadQuality = backInfo.getTotalMass();
                }
            }
            vehicleLicenseResult.setApprovedLoad(looadQuality);

            String externalSize = backInfo.getExternalSize();
            if (StringUtil.isNotBlank(externalSize)) {
                //长宽高根据×分割设置值
                String[] externalSizeArray = externalSize.split("×");
                if (externalSizeArray.length > 2) {
                    vehicleLicenseResult.setCarLength(externalSizeArray[0]);
                    String carLength = OcrFilterUtil.filterNumber(externalSizeArray[0]);
                    vehicleLicenseResult.setCarLengthFormat(carLength);
                    vehicleLicenseResult.setCarWidth(externalSizeArray[1]);
                    String carWidth = OcrFilterUtil.filterNumber(externalSizeArray[1]);
                    vehicleLicenseResult.setCarWidthFormat(carWidth);
                    vehicleLicenseResult.setCarHeight(externalSizeArray[2]);
                    String carHeight = OcrFilterUtil.filterNumber(externalSizeArray[2]);
                    vehicleLicenseResult.setCarHeightFormat(carHeight);
                }
            }
            String marks = backInfo.getMarks();
            if (StrUtil.isNotBlank(marks)) {
                marks = marks.replace("强制报废期止:", "").trim();
            }
            vehicleLicenseResult.setRemarks(marks);
            String record = backInfo.getRecord();
            if (StrUtil.isNotBlank(record)) {
                int lastIndex = record.lastIndexOf("至");
                if (lastIndex != -1) {
                    record = record.substring(lastIndex + 1);
                }
            }
            vehicleLicenseResult.setInspectionRecord(record);
            vehicleLicenseResult.setChipNumber(backInfo.getSubPageCode());

            String totalQuasiMass = backInfo.getTotalQuasiMass();
            if (StrUtil.isBlank(totalQuasiMass) || "--".equals(totalQuasiMass)) {
                totalQuasiMass = backInfo.getLooadQuality();
                if (StrUtil.isBlank(totalQuasiMass) || "--".equals(totalQuasiMass)) {
                    totalQuasiMass = backInfo.getTotalMass();
                }
            }
            vehicleLicenseResult.setTowingCapacity(totalQuasiMass);
            vehicleLicenseResult.setFuelType(backInfo.getFuelType());
        }
        List<String> recognizeWarnMsgList = hundredWattsVehicleLicenseResult.getRecognizeWarnMsg();
        if (CollUtil.isNotEmpty(recognizeWarnMsgList)) {
            String recognizeWarnMsg = recognizeWarnMsgList.get(0);
            String riskType = HundredWattsPool.VEHICLE_LICENSE_RESPONSE_POOL.get(recognizeWarnMsg);
            if (StringUtil.isNotBlank(riskType)) {
                vehicleLicenseResult.setRiskType(riskType);
            }
        } else {
            vehicleLicenseResult.setRiskType(HundredWattsPool.VEHICLE_LICENSE_NORMAL);
        }


        return vehicleLicenseResult;
    }

    public static BusinessLicenseResult hundredWattsBusinessLicenseResultToBusinessLicenseResult(HundredWattsBusinessLicenseResult hundredWattsBusinessLicenseResult) {
        if (hundredWattsBusinessLicenseResult == null) {
            return null;
        }
        BusinessLicenseResult businessLicenseResult = new BusinessLicenseResult();

        businessLicenseResult.setRequestId(hundredWattsBusinessLicenseResult.getRequestId());

        businessLicenseResult.setBusinessScope(hundredWattsBusinessLicenseResult.getBusiness());
        businessLicenseResult.setCompositionForm(hundredWattsBusinessLicenseResult.getComposingForm());
        businessLicenseResult.setLegalPerson(hundredWattsBusinessLicenseResult.getPerson());
        businessLicenseResult.setCompanyName(hundredWattsBusinessLicenseResult.getName());

        String period = hundredWattsBusinessLicenseResult.getPeriod();
        if (StrUtil.isNotBlank(period)) {
            String[] periodArray = period.split("至");
            if (periodArray.length > 1) {
                period = periodArray[1];
                businessLicenseResult.setValidityPeriod(period);
                if (!"长期".equals(period)) {
                    period = DateUtil.formatDate(period, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                }
                businessLicenseResult.setValidityPeriodFormat(period);
            }

        }


        String validityStartDate = hundredWattsBusinessLicenseResult.getSetDate();

        if (StrUtil.isNotBlank(validityStartDate)) {
            if (DateUtil.isChineseDateFormat(validityStartDate)) {
                validityStartDate = DateUtil.convertChineseToArabic(validityStartDate);
            }
            businessLicenseResult.setValidityStartDate(validityStartDate);
            businessLicenseResult.setEstablishmentDate(validityStartDate);
            validityStartDate = DateUtil.formatDate(validityStartDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            businessLicenseResult.setValidityStartDateFormat(validityStartDate);
            businessLicenseResult.setEstablishmentDateFormat(validityStartDate);

        }


        String registrationDate = hundredWattsBusinessLicenseResult.getRegistrationDate();
        if (StringUtil.isNotBlank(registrationDate)) {
            businessLicenseResult.setApprovalDate(registrationDate);
            registrationDate = DateUtil.formatDate(registrationDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            businessLicenseResult.setApprovalDateFormat(registrationDate);
        }
        businessLicenseResult.setTaxRegistrationNumber(hundredWattsBusinessLicenseResult.getRegNum());
        businessLicenseResult.setSocialCreditCode(hundredWattsBusinessLicenseResult.getRegNum());
        businessLicenseResult.setAddress(hundredWattsBusinessLicenseResult.getAddress());
        businessLicenseResult.setType(hundredWattsBusinessLicenseResult.getType());
        String capital = hundredWattsBusinessLicenseResult.getCapital();
        businessLicenseResult.setRegisteredCapital(capital);

        if (StrUtil.isNotBlank(capital)) {
            //去除特殊字符
            capital = capital.replace("圆", "").replace("整", "").replace("(", "")
                    .replace("人", "").replace("民", "").replace("币", "")
                    .replace("元", "").replace(")", "");
            int capitalNumber = 0;
            BigDecimal capitalDecimal = BigDecimal.ZERO;
            try {
                capitalNumber = NumberChineseFormatter.chineseToNumber(capital);
            } catch (Exception e) {
                capitalDecimal = NumberChineseFormatter.chineseMoneyToNumber(capital);
            }
            if (capitalNumber != 0) {
                businessLicenseResult.setRegisteredCapitalNumber(String.valueOf(capitalNumber));
            } else {
                businessLicenseResult.setRegisteredCapitalNumber(String.valueOf(capitalDecimal.setScale(0, RoundingMode.DOWN)));
            }
        }

        List<String> recognizeWarnMsgList = hundredWattsBusinessLicenseResult.getRecognizeWarnMsg();
        if (CollUtil.isNotEmpty(recognizeWarnMsgList)) {
            String riskType = HundredWattsPool.BUSINESS_RESPONSE_POOL.get(recognizeWarnMsgList.get(0));
            if (StrUtil.isNotBlank(riskType)) {
                businessLicenseResult.setRiskType(riskType);
            }
        }
        return businessLicenseResult;
    }


    public static DriverLicenseResult hundredWattsDriverLicenseResultToDriverLicenseResult(HundredWattsDriverLicenseResult hundredWattsDriverLicenseResult) {
        if (hundredWattsDriverLicenseResult == null) {
            return null;
        }
        DriverLicenseResult driverLicenseResult = new DriverLicenseResult();
        driverLicenseResult.setRequestId(hundredWattsDriverLicenseResult.getRequestId());
        driverLicenseResult.setName(hundredWattsDriverLicenseResult.getName());
        driverLicenseResult.setAddress(hundredWattsDriverLicenseResult.getAddress());
        driverLicenseResult.setNationality(hundredWattsDriverLicenseResult.getNationality());
        driverLicenseResult.setIssuingAuthority(hundredWattsDriverLicenseResult.getIssuingAuthority());
        driverLicenseResult.setCurrentTime(hundredWattsDriverLicenseResult.getCurrentTime());
        driverLicenseResult.setBirthDate(hundredWattsDriverLicenseResult.getDateOfBirth());
        driverLicenseResult.setBirthDateFormat(hundredWattsDriverLicenseResult.getDateOfBirth());
        driverLicenseResult.setIdNumber(hundredWattsDriverLicenseResult.getCardCode());
        driverLicenseResult.setFirstLicenseDate(hundredWattsDriverLicenseResult.getDateOfFirstIssue());
        driverLicenseResult.setFirstLicenseDateFormat(hundredWattsDriverLicenseResult.getDateOfFirstIssue());
        driverLicenseResult.setGender(hundredWattsDriverLicenseResult.getSex());
        driverLicenseResult.setValidityPeriod(hundredWattsDriverLicenseResult.getEndDate());
        driverLicenseResult.setValidityPeriodFormat(hundredWattsDriverLicenseResult.getEndDate());
        driverLicenseResult.setDrivingLicenseType(hundredWattsDriverLicenseResult.getDrivingLicenseType());
        driverLicenseResult.setRecord(hundredWattsDriverLicenseResult.getRecord());
        driverLicenseResult.setFileNumber(hundredWattsDriverLicenseResult.getArchivesCode());
        driverLicenseResult.setValidStartDate(hundredWattsDriverLicenseResult.getStartDate());
        driverLicenseResult.setValidStartDateFormat(hundredWattsDriverLicenseResult.getStartDate());
        driverLicenseResult.setUntilDate(hundredWattsDriverLicenseResult.getEndDate());
        driverLicenseResult.setUntilDateFormat(hundredWattsDriverLicenseResult.getEndDate());

        List<String> recognizeWarnMsgList = hundredWattsDriverLicenseResult.getRecognizeWarnMsg();
        if (CollUtil.isNotEmpty(recognizeWarnMsgList)) {
            String recognizeWarnMsg = recognizeWarnMsgList.get(0);
            String s = HundredWattsPool.DRIVERS_LICENSE_RESPONSE_POOL.get(recognizeWarnMsg);
            if (StringUtil.isNotBlank(s)) {
                driverLicenseResult.setRiskType(s);
            }
        } else {
            driverLicenseResult.setRiskType(HundredWattsPool.DRIVERS_LICENSE_NORMAL);
        }

        return driverLicenseResult;
    }

    /**
     * 泰普科驾驶证识别结果转换类
     * @param timecapsuleDriverLicenseResult
     * @return
     */
    public static DriverLicenseResult timecapsuleDriverLicenseResultToDriverLicenseResult(TimecapsuleDriverLicenseResult timecapsuleDriverLicenseResult) {
        if (timecapsuleDriverLicenseResult == null) {
            return null;
        }
        DriverLicenseResult driverLicenseResult = new DriverLicenseResult();
        driverLicenseResult.setRequestId(timecapsuleDriverLicenseResult.getRequestId());
        driverLicenseResult.setName(timecapsuleDriverLicenseResult.getName());
        driverLicenseResult.setAddress(timecapsuleDriverLicenseResult.getAddress());
        driverLicenseResult.setNationality(timecapsuleDriverLicenseResult.getNationality());
        driverLicenseResult.setIssuingAuthority(timecapsuleDriverLicenseResult.getIssuingAuthority());
        driverLicenseResult.setCurrentTime(timecapsuleDriverLicenseResult.getCurrentTime());
        driverLicenseResult.setBirthDate(timecapsuleDriverLicenseResult.getBirthDate());
        driverLicenseResult.setBirthDateFormat(timecapsuleDriverLicenseResult.getBirthDateFormat());
        driverLicenseResult.setIdNumber(timecapsuleDriverLicenseResult.getIdNumber());
        driverLicenseResult.setFirstLicenseDate(timecapsuleDriverLicenseResult.getFirstLicenseDate());
        driverLicenseResult.setFirstLicenseDateFormat(timecapsuleDriverLicenseResult.getFirstLicenseDate());
        driverLicenseResult.setGender(timecapsuleDriverLicenseResult.getGender());
        //驾驶证有效开始时间
        String validStartDate = timecapsuleDriverLicenseResult.getValidityPeriod();
        //驾驶证有效截至时间
        String untilDate = timecapsuleDriverLicenseResult.getUntilDate();
        driverLicenseResult.setValidityPeriod(untilDate);
        driverLicenseResult.setValidityPeriodFormat(untilDate);
        driverLicenseResult.setDrivingLicenseType(timecapsuleDriverLicenseResult.getDrivingLicenseType());
        driverLicenseResult.setRecord(timecapsuleDriverLicenseResult.getRecord());
        driverLicenseResult.setFileNumber(timecapsuleDriverLicenseResult.getFileNumber());
        driverLicenseResult.setValidStartDate(validStartDate);
        driverLicenseResult.setValidStartDateFormat(validStartDate);
        driverLicenseResult.setUntilDate(untilDate);
        driverLicenseResult.setUntilDateFormat(untilDate);

        List<String> recognizeWarnMsgList = timecapsuleDriverLicenseResult.getRecognizeWarnMsg();
        if (CollUtil.isNotEmpty(recognizeWarnMsgList)) {
            String recognizeWarnMsg = recognizeWarnMsgList.get(0);
            String s = HundredWattsPool.DRIVERS_LICENSE_RESPONSE_POOL.get(recognizeWarnMsg);
            if (StringUtil.isNotBlank(s)) {
                driverLicenseResult.setRiskType(s);
            }
        } else {
            driverLicenseResult.setRiskType(HundredWattsPool.DRIVERS_LICENSE_NORMAL);
        }

        return driverLicenseResult;
    }

    /**
     * 泰普科行驶证结果转换类
     * @param timecapsuleVehicleLicenseResult
     * @return
     */
    public static VehicleLicenseResult timecapsuleVehicleLicenseResultToVehicleLicenseResult(TimecapsuleVehicleLicenseResult timecapsuleVehicleLicenseResult) {
        if (timecapsuleVehicleLicenseResult == null) {
            return null;
        }
        VehicleLicenseResult vehicleLicenseResult = new VehicleLicenseResult();
        vehicleLicenseResult.setVehicleNo(timecapsuleVehicleLicenseResult.getVehicleNo());
        vehicleLicenseResult.setLicensePlateColor(timecapsuleVehicleLicenseResult.getLicensePlateColor());
        vehicleLicenseResult.setVehicleIdentificationNumber(timecapsuleVehicleLicenseResult.getVehicleIdentificationNumber());
        vehicleLicenseResult.setEngineNumber(timecapsuleVehicleLicenseResult.getEngineNumber());
        vehicleLicenseResult.setOwner(timecapsuleVehicleLicenseResult.getOwner());
        vehicleLicenseResult.setUsage(timecapsuleVehicleLicenseResult.getUsage());
        vehicleLicenseResult.setVehicleType(timecapsuleVehicleLicenseResult.getVehicleType());
        vehicleLicenseResult.setBrandModel(timecapsuleVehicleLicenseResult.getBrandModel());
        vehicleLicenseResult.setAddress(timecapsuleVehicleLicenseResult.getAddress());
        vehicleLicenseResult.setIssuingAuthority(timecapsuleVehicleLicenseResult.getIssuingAuthority());
        vehicleLicenseResult.setIssuingDate(timecapsuleVehicleLicenseResult.getIssuingDate());
        vehicleLicenseResult.setIssuingDateFormat(timecapsuleVehicleLicenseResult.getIssuingDate());
        vehicleLicenseResult.setRegistrationDate(timecapsuleVehicleLicenseResult.getRegistrationDate());
        vehicleLicenseResult.setRegistrationDateFormat(timecapsuleVehicleLicenseResult.getRegistrationDate());
        vehicleLicenseResult.setInspectionRecord(timecapsuleVehicleLicenseResult.getInspectionRecord());
        vehicleLicenseResult.setApprovedLoad(timecapsuleVehicleLicenseResult.getApprovedLoad());
        vehicleLicenseResult.setCarHeight(timecapsuleVehicleLicenseResult.getCarHeight());
        vehicleLicenseResult.setCarHeightFormat(timecapsuleVehicleLicenseResult.getCarHeight());
        vehicleLicenseResult.setExteriorDimensions(timecapsuleVehicleLicenseResult.getExteriorDimensions());
        vehicleLicenseResult.setCarLength(timecapsuleVehicleLicenseResult.getCarLength());
        vehicleLicenseResult.setCarLengthFormat(timecapsuleVehicleLicenseResult.getCarLength());
        vehicleLicenseResult.setCarWidth(timecapsuleVehicleLicenseResult.getCarWidth());
        vehicleLicenseResult.setCarWidthFormat(timecapsuleVehicleLicenseResult.getCarWidth());
        vehicleLicenseResult.setApprovedPassengers(timecapsuleVehicleLicenseResult.getApprovedPassengers());
        vehicleLicenseResult.setGrossWeight(timecapsuleVehicleLicenseResult.getGrossWeight());
        vehicleLicenseResult.setFuelType(timecapsuleVehicleLicenseResult.getFuelType());
        vehicleLicenseResult.setTowingCapacity(timecapsuleVehicleLicenseResult.getTowingCapacity());
        vehicleLicenseResult.setRemarks(timecapsuleVehicleLicenseResult.getRemarks());
        vehicleLicenseResult.setChipNumber(timecapsuleVehicleLicenseResult.getChipNumber());
        vehicleLicenseResult.setFileNumber(timecapsuleVehicleLicenseResult.getFileNumber());
        //默认值
        vehicleLicenseResult.setRiskType(timecapsuleVehicleLicenseResult.getRiskType());
        return vehicleLicenseResult;
    }

    public static LicensePlateResult hundredWattsLicensePlateResultToLicensePlateResult(HundredWattsLicensePlateResult hundredWattsLicensePlateResult) {
        if (hundredWattsLicensePlateResult == null) {
            return null;
        }
        LicensePlateResult licensePlateResult = new LicensePlateResult();

        licensePlateResult.setRequestId(hundredWattsLicensePlateResult.getRequestId());
        licensePlateResult.setNumber(hundredWattsLicensePlateResult.getNumber());
        licensePlateResult.setColor(hundredWattsLicensePlateResult.getColor());

        return licensePlateResult;
    }


    public static QualificationCertificateForRoadTransportPersonnelResult hundredWattsQualificationCertificateForRoadTransportPersonnelResultToQualificationCertificateForRoadTransportPersonnelResult(HundredWattsQualificationCertificateForRoadTransportPersonnelResult hundredWattsQualificationCertificateForRoadTransportPersonnelResult) {
        if (hundredWattsQualificationCertificateForRoadTransportPersonnelResult == null) {
            return null;
        }
        QualificationCertificateForRoadTransportPersonnelResult qualificationCertificateForRoadTransportPersonnelResult = new QualificationCertificateForRoadTransportPersonnelResult();

        qualificationCertificateForRoadTransportPersonnelResult.setRequestId(hundredWattsQualificationCertificateForRoadTransportPersonnelResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsQualificationCertificateForRoadTransportPersonnelResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();
            if (OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_START_DATE.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    //证件会识别出年月日的错误情况
                    if (!autoContent.contains("年月日")) {
                        String validStartDate = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                        qualificationCertificateForRoadTransportPersonnelResult.setValidStartDate(validStartDate);
                    }
                }
            }
            if (OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_PERIOD1.equals(autoName)
                    || OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_PERIOD2.equals(autoName)
                    || OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_VALID_PERIOD3.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    autoContent = autoContent.replace("止", "");
                    if (autoContent.contains("至")) {
                        autoContent = autoContent.substring(autoContent.indexOf("至") + 1);
                    }
                    String validityPeriod = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                    qualificationCertificateForRoadTransportPersonnelResult.setValidityPeriod(validityPeriod);
                }
            }
            if (OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NUMBER1.equals(autoName)
                    || OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NUMBER2.equals(autoName)
                    || OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NUMBER3.equals(autoName)) {
                qualificationCertificateForRoadTransportPersonnelResult.setIdNumber(autoContent.trim());
            }

            if (OcrConstants.HUNDREDWATTS_QUALIFICATION_CERTIFICATE_FOR_ROAD_TRANSPORT_PERSONNEL_NAME.equals(autoName)) {
                qualificationCertificateForRoadTransportPersonnelResult.setName(autoContent.trim());
            }
        }
        return qualificationCertificateForRoadTransportPersonnelResult;
    }

    /**
     * 泰普科道路运输从业人员资格证识别结果转换类
     * @param personnelResult
     * @return
     */
    public static QualificationCertificateForRoadTransportPersonnelResult timecapsuletransportPersonnelTransportPersonnelResult(TimecapsuleQualificationCertificateForRoadTransportPersonnelResult personnelResult) {
        if (personnelResult == null) {
            return null;
        }
        QualificationCertificateForRoadTransportPersonnelResult transportPersonnelResult = new QualificationCertificateForRoadTransportPersonnelResult();
        transportPersonnelResult.setRequestId(personnelResult.getRequestId());
        transportPersonnelResult.setIdNumber(personnelResult.getIdNumber().trim());
        transportPersonnelResult.setName(personnelResult.getName().trim());
        //日期格式兼容
        String validityPeriod = personnelResult.getValidityPeriod();
        if (validityPeriod.contains("年")) {
            validityPeriod = DateUtil.formatDate(validityPeriod, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
        }
        transportPersonnelResult.setValidityPeriod(validityPeriod);
        String validStartDate = personnelResult.getValidStartDate();
        if (validStartDate.contains("年")) {
            validStartDate = DateUtil.formatDate(validStartDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
        }
        transportPersonnelResult.setValidStartDate(validStartDate);
        return transportPersonnelResult;
    }

    public static RoadTransportOperationLicenseResult hundredWattsRoadTransportOperationLicenseResultToRoadTransportOperationLicenseResult(HundredWattsRoadTransportOperationLicenseResult hundredWattsRoadTransportOperationLicenseResult) {
        if (hundredWattsRoadTransportOperationLicenseResult == null) {
            return null;
        }
        RoadTransportOperationLicenseResult roadTransportOperationLicenseResult = new RoadTransportOperationLicenseResult();

        roadTransportOperationLicenseResult.setRequestId(hundredWattsRoadTransportOperationLicenseResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsRoadTransportOperationLicenseResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_NUMBER.equals(autoName)) {
                String licenseNumber = autoContent.trim();
                licenseNumber = StringUtils.deleteWhitespace(licenseNumber.replaceAll("[^0-9]", ""));
                roadTransportOperationLicenseResult.setLicenseNumber(licenseNumber);
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_OWNER_NAME.equals(autoName)) {
                roadTransportOperationLicenseResult.setOwnerName(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_ADDRESS.equals(autoName)) {
                roadTransportOperationLicenseResult.setAddress(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_BUSINESS_SCOPE.equals(autoName)) {
                roadTransportOperationLicenseResult.setBusinessScope(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_ISSUE_UNIT.equals(autoName)) {
                roadTransportOperationLicenseResult.setIssueUnit(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_TITLE.equals(autoName)){
                roadTransportOperationLicenseResult.setTitle(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_SEX.equals(autoName)){
                roadTransportOperationLicenseResult.setSex(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_BIRTH_DATE.equals(autoName)){
                roadTransportOperationLicenseResult.setBirthDate(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_NATIONAL.equals(autoName)){
                roadTransportOperationLicenseResult.setNational(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_HOME_ADDRESS.equals(autoName)){
                roadTransportOperationLicenseResult.setHomeAddress(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_QUALIFICATIONS_TYPE.equals(autoName)){
                roadTransportOperationLicenseResult.setQualificationsType(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_ISSUE_AUTHORITY.equals(autoName)){
                roadTransportOperationLicenseResult.setIssueAuthority(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_CONTINUING_EDUCATION_INFORMATION.equals(autoName)){
                roadTransportOperationLicenseResult.setContinuingEducationInformation(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_INTEGRITY_ASSESSMENT_INFORMATION.equals(autoName)){
                roadTransportOperationLicenseResult.setIntegrityAssessmentInformation(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_INSTITUTION.equals(autoName)){
                roadTransportOperationLicenseResult.setInstitution(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_OPERATION_LICENSE_VALIDITY_DATE.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    autoContent = autoContent.replaceAll("自", "");
                    String[] autoContentArray = autoContent.split("至");
                    if (autoContentArray.length > 1) {
                        String validityStartDate = DateUtil.formatDate(autoContentArray[0], DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                        String expirationDate = DateUtil.formatDate(autoContentArray[1], DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);

                        roadTransportOperationLicenseResult.setIssueDate(validityStartDate);
                        roadTransportOperationLicenseResult.setValidityStartDate(validityStartDate);
                        roadTransportOperationLicenseResult.setExpirationDate(expirationDate);
                    }
                }

            }
        }
        return roadTransportOperationLicenseResult;
    }

    public static WaterTransportOperationLicenseResult hundredWattsWaterTransportOperationLicenseResultToWaterTransportOperationLicenseResult(HundredWattsWaterTransportOperationLicenseResult hundredWattsWaterTransportOperationLicenseResult) {
        if (hundredWattsWaterTransportOperationLicenseResult == null) {
            return null;
        }
        WaterTransportOperationLicenseResult waterTransportOperationLicenseResult = new WaterTransportOperationLicenseResult();

        waterTransportOperationLicenseResult.setRequestId(hundredWattsWaterTransportOperationLicenseResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsWaterTransportOperationLicenseResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();

            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_AIS_IDENTIFICATION_NUMBER.equals(autoName)) {
                waterTransportOperationLicenseResult.setWaterTransportPermitNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_BUSINESS_NAME.equals(autoName)) {
                waterTransportOperationLicenseResult.setBusinessName(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_LEGAL_PERSON.equals(autoName)) {
                waterTransportOperationLicenseResult.setLegalPerson(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_ADDRESS.equals(autoName)) {
                waterTransportOperationLicenseResult.setAddress(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_ECONOMY_TYPE.equals(autoName)) {
                waterTransportOperationLicenseResult.setEconomyType(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_GOODS_TRANSPORTATION.equals(autoName)) {
                waterTransportOperationLicenseResult.setGoodsTransportation(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_OPERATION_DURATION.equals(autoName)) {
                waterTransportOperationLicenseResult.setOperationDuration(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_APPROVING_AUTHORITY_DOCUMENT_NUMBER.equals(autoName)) {
                waterTransportOperationLicenseResult.setApprovingAuthorityDocumentNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_APPROVING_AUTHORITY_START_AND_END_DATE.equals(autoName)) {
                waterTransportOperationLicenseResult.setStartEndDates(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_WATER_TRANSPORT_OPERATION_LICENSE_PASSENGER_TRANSPORTATION.equals(autoName)){
                waterTransportOperationLicenseResult.setPassengerTransportation(autoContent.trim());
            }
        }

        return waterTransportOperationLicenseResult;
    }


    public static ShipTransportOperationCertificateResult hundredWattsShipTransportOperationCertificateResultToShipTransportOperationCertificateResult(HundredWattsShipTransportOperationCertificateResult hundredWattsShipTransportOperationCertificateResult) {
        if (hundredWattsShipTransportOperationCertificateResult == null) {
            return null;
        }
        ShipTransportOperationCertificateResult shipTransportOperationCertificateResult = new ShipTransportOperationCertificateResult();

        shipTransportOperationCertificateResult.setRequestId(hundredWattsShipTransportOperationCertificateResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsShipTransportOperationCertificateResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();

            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_NUMBER.equals(autoName)) {
                shipTransportOperationCertificateResult.setNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_NAME.equals(autoName)) {
                shipTransportOperationCertificateResult.setShipName(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_FLAG_SHIP_PORT.equals(autoName)) {
                shipTransportOperationCertificateResult.setFlagshipPort(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_REGISTRATION_NUMBER.equals(autoName)) {
                shipTransportOperationCertificateResult.setShipRegistrationNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_INSPECTION_REGISTRATION_NUMBER.equals(autoName)) {
                shipTransportOperationCertificateResult.setShipInspectionRegistrationNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_OWNER.equals(autoName)) {
                shipTransportOperationCertificateResult.setShipOwner(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_SHIP_OPERATORS.equals(autoName)) {
                shipTransportOperationCertificateResult.setShipOperators(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_OPERATOR_LICENSE_NUMBER.equals(autoName)) {
                shipTransportOperationCertificateResult.setOperatorLicenseNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_VALID_UNTIL.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    autoContent = autoContent.replace("止", "");
                    if (autoContent.contains("至")) {
                        autoContent = autoContent.substring(autoContent.indexOf("至") + 1);
                    }

                    String validUntil = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN7, DateUtil.DATE_FORMAT_PATTERN8);
                    shipTransportOperationCertificateResult.setValidUntil(validUntil);
                }
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_ISSUE_AUTHORITY.equals(autoName)) {
                shipTransportOperationCertificateResult.setIssueAuthority(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_SHIP_TRANSPORT_OPERATION_CERTIFICATE_ISSUE_DATE.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {
                    String issueDate = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                    shipTransportOperationCertificateResult.setIssueDate(issueDate);
                }

            }
        }

        return shipTransportOperationCertificateResult;
    }


    public static AISCertificateResult hundredWattsAISCertificateResultToAISCertificateResult(HundredWattsAISCertificateResult hundredWattsAISCertificateResult) {
        if (hundredWattsAISCertificateResult == null) {
            return null;
        }
        AISCertificateResult aISCertificateResult = new AISCertificateResult();

        aISCertificateResult.setRequestId(hundredWattsAISCertificateResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsAISCertificateResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();

            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_NUMBER.equals(autoName)) {
                aISCertificateResult.setCertificateNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_SHIP_NAME.equals(autoName)) {
                aISCertificateResult.setShipName(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_AIS_IDENTIFICATION_CODE.equals(autoName)) {
                aISCertificateResult.setAisIdentificationCode(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_UNIT.equals(autoName)) {
                aISCertificateResult.setUnit(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_SHIP_IDENTIFICATION_NUMBER.equals(autoName)) {
                aISCertificateResult.setShipInspectionRegistrationNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_ISSUANCE_DATE.equals(autoName)) {
                if (StringUtil.isNotBlank(autoContent)) {

                    String issuanceDate = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                    aISCertificateResult.setIssuanceDate(issuanceDate);

                }
            }
            if (OcrConstants.HUNDREDWATTS_AIS_EQUIPMENT_PRODUCT_MODEL.equals(autoName)) {
                aISCertificateResult.setAisEquipmentProductModel(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_TITLE.equals(autoName)) {
                aISCertificateResult.setTitle(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_AIS_CERTIFICATE_OPERATOR_CODE.equals(autoName)) {
                aISCertificateResult.setOperatorCode(autoContent.trim());
            }
        }
        return aISCertificateResult;
    }

    public static CrewCompetencyCertificateResult hundredWattsCrewCompetencyCertificateResultToCrewCompetencyCertificateResult(HundredWattsCrewCompetencyCertificateResult hundredWattsCrewCompetencyCertificateResult) {
        if (hundredWattsCrewCompetencyCertificateResult == null) {
            return null;
        }
        CrewCompetencyCertificateResult crewCompetencyCertificateResult = new CrewCompetencyCertificateResult();

        crewCompetencyCertificateResult.setRequestId(hundredWattsCrewCompetencyCertificateResult.getRequestId());

        List<HundredWattsBaseStructuralList> structuralList = hundredWattsCrewCompetencyCertificateResult.getStructuralList();
        if (CollUtil.isEmpty(structuralList)) {
            return null;
        }
        for (HundredWattsBaseStructuralList hundredWattsBaseStructuralList : structuralList) {
            List<HundredWattsBaseGroups> groups = hundredWattsBaseStructuralList.getGroups();
            HundredWattsBaseGroups hundredWattsBaseGroups = groups.get(0);
            List<HundredWattsBaseLines> lines = hundredWattsBaseGroups.getLines();
            if (CollUtil.isEmpty(lines)) {
                continue;
            }
            HundredWattsBaseLines hundredWattsBaseLines = lines.get(0);
            String autoName = hundredWattsBaseLines.getKey().getAutoName();
            String autoContent = hundredWattsBaseLines.getValue().getAutoContent();
            if (OcrConstants.BAIDU_OCR_NAME.equals(autoName)) {
                crewCompetencyCertificateResult.setName(autoContent.trim());
            }

            if (OcrConstants.HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_CERTIFICATE_NUMBER.equals(autoName)) {
                crewCompetencyCertificateResult.setCrewCompetencyCertificateNumber(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_JOB_QUALIFICATIONS.equals(autoName)) {
                crewCompetencyCertificateResult.setJobQualifications(autoContent.trim());
            }
            if (OcrConstants.HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_ISSUE_DATE.equals(autoName)) {
                String issueDate = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                crewCompetencyCertificateResult.setIssueDate(issueDate);
            }
            if (OcrConstants.HUNDREDWATTS_CREW_COMPETENCY_CERTIFICATE_DEADLINE_DATE.equals(autoName)) {
                String expirationDate = DateUtil.formatDate(autoContent, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                crewCompetencyCertificateResult.setExpirationDate(expirationDate);
            }
        }
        return crewCompetencyCertificateResult;
    }


    public static OcrBaiduReq ocrBase64ReqtoOcrBaiduReq(OcrBase64Req ocrBase64Req) {
        if (ocrBase64Req == null) {
            return null;
        }
        OcrBaiduReq ocrBaiduReq = new OcrBaiduReq();
        ocrBaiduReq.setCardSide(ocrBase64Req.getCardSide());
        ocrBaiduReq.setOcrType(ocrBase64Req.getOcrType());
        ocrBaiduReq.setProjectDivision(ocrBase64Req.getProjectDivision());
        ocrBaiduReq.setDetectRisk(ocrBase64Req.getDetectRisk());
        ocrBaiduReq.setDetectQuality(ocrBase64Req.getDetectQuality());
        ocrBaiduReq.setQualityWarn(ocrBase64Req.getQualityWarn());
        ocrBaiduReq.setRiskWarn(ocrBase64Req.getRiskWarn());
        ocrBaiduReq.setDetectDirection(ocrBase64Req.getDetectDirection());
        ocrBaiduReq.setParagraph(ocrBase64Req.getParagraph());
        ocrBaiduReq.setSealTag(ocrBase64Req.getSealTag());
        ocrBaiduReq.setUrl(ocrBase64Req.getImageUrl());
        if (ObjectUtil.isNotEmpty(ocrBase64Req.getCardSide())) {
            ocrBaiduReq.setVehicleLicenseSide(ocrBase64Req.getCardSide().toLowerCase());
            ocrBaiduReq.setIdCardSide(ocrBase64Req.getCardSide().toLowerCase());
            ocrBaiduReq.setDrivingLicenseSide(ocrBase64Req.getCardSide().toLowerCase());
        }


        return ocrBaiduReq;
    }


    public static IdCardResult baiduIdCardResulttoIdCardResult(BaiduIdCardResult baiduIdCardResult) {
        if (baiduIdCardResult == null) {
            return null;
        }
        IdCardResult idCardResult = new IdCardResult();

        // 姓名
        idCardResult.setName(getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_NAME));
        // 性别
        idCardResult.setGender(getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_GENDER));
        // 住址
        idCardResult.setAddress(getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_ADDRESS));
        // 公民身份号码
        idCardResult.setIdCardNo(getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_CARD_NO));
        // 出生
        String birthDate = getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_BIRTH_DATE);
        if (StrUtil.isNotBlank(birthDate)) {
            birthDate = DateUtil.formatDate(birthDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setBirthDate(birthDate);
        }
        // 民族
        idCardResult.setNation(getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_NATION));
        // 失效日期
        String expirationDate = getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_EXPIRATION_DATE);
        if (StrUtil.isNotBlank(expirationDate)) {
            idCardResult.setExpirationDate(expirationDate);
            expirationDate = DateUtil.formatDate(expirationDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setExpirationDateFormat(expirationDate);
        }
        // 签发机关
        idCardResult.setIssuingAuthority(getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_ISSUING_AUTHORITY));
        // 签发日期
        String issueDate = getWord(baiduIdCardResult.getWordsResult(), OcrConstants.BAIDU_OCR_ISSUE_DATE);
        if (StrUtil.isNotBlank(issueDate)) {
            idCardResult.setIssueDate(issueDate);
            issueDate = DateUtil.formatDate(issueDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            idCardResult.setIssueDateFormat(issueDate);
        }
        //身份证图片状态
        idCardResult.setImageStatus(baiduIdCardResult.getImageStatus());
        //身份证风险类型
        idCardResult.setRiskType(baiduIdCardResult.getRiskType());
        //被编辑情况
        idCardResult.setEditTool(baiduIdCardResult.getEditTool());
        //身份证质量类型
        IdCardQuantity ocrCardQuantity = new IdCardQuantity();
        JSONObject ocrCardQuantityObj = JSONUtil.parseObj(baiduIdCardResult.getCardQuality());
        ocrCardQuantity.setIsClear(ocrCardQuantityObj.getStr("IsClear"));
        ocrCardQuantity.setIsClearPropobility(ocrCardQuantityObj.getStr("IsClear_propobility"));
        ocrCardQuantity.setIsComplete(ocrCardQuantityObj.getStr("IsNoCover"));
        ocrCardQuantity.setIsCompletePropobility(ocrCardQuantityObj.getStr("IsNoCover_propobility"));
        ocrCardQuantity.setIsNoCover(ocrCardQuantityObj.getStr("IsComplete"));
        ocrCardQuantity.setIsNoCoverPropobility(ocrCardQuantityObj.getStr("IsComplete_propobility"));
        idCardResult.setCardQuality(ocrCardQuantity);

        idCardResult.setRequestId(String.valueOf(baiduIdCardResult.getLogId()));

        return idCardResult;
    }

    private static String getWord(Map<String, BaiduWords> resultWords, String fieldName) {
        return getWord(resultWords, fieldName, null);
    }

    private static String getWord(Map<String, BaiduWords> resultWords, String fieldName, String defaultValue) {
        if (isNull(resultWords) || !resultWords.containsKey(fieldName)) {
            return defaultValue;
        }
        return resultWords.get(fieldName).getWords();
    }


    public static BankCardResult baiduBankCardResulttoBankCardResult(BaiduBankCardResult baiduBankCardResult) {
        if (baiduBankCardResult == null) {
            return null;
        }
        BankCardResult bankCardResult = new BankCardResult();


        bankCardResult.setBankName(baiduBankCardResult.getBankName());
        bankCardResult.setBankCardNumber(baiduBankCardResult.getBankCardNumber());
        bankCardResult.setValidDate(baiduBankCardResult.getValidDate());
        bankCardResult.setHolderName(baiduBankCardResult.getHolderName());
        bankCardResult.setBankCardType(baiduBankCardResult.getBankCardType());
        bankCardResult.setRequestId(String.valueOf(baiduBankCardResult.getLogId()));

        return bankCardResult;
    }


    public static VatInvoiceResult baiduVatInvoiceResulttoVatInvoiceResult(BaiduVatInvoiceResult baiduVatInvoiceResult) {
        if (baiduVatInvoiceResult == null) {
            return null;
        }
        VatInvoiceResult vatInvoiceResult = new VatInvoiceResult();

        BaiduVatInvoice baiduVatInvoice = baiduVatInvoiceResult.getWordsResult();

        vatInvoiceResult.setInvoiceCode(baiduVatInvoice.getInvoiceCode());
        vatInvoiceResult.setInvoiceNum(baiduVatInvoice.getInvoiceNum());
        vatInvoiceResult.setInvoiceDate(baiduVatInvoice.getInvoiceDate());
        List<BaiduVatInvoiceCommodityBaseDTO> commodityAmountList = baiduVatInvoice.getCommodityAmount();
        if (CollUtil.isNotEmpty(commodityAmountList)) {
            vatInvoiceResult.setInvoiceAmount(commodityAmountList.get(0).getWord());
        }
        List<BaiduVatInvoiceCommodityBaseDTO> commodityUnitList = baiduVatInvoice.getCommodityUnit();
        if (CollUtil.isNotEmpty(commodityUnitList)) {
            vatInvoiceResult.setInvoiceUnit(commodityUnitList.get(0).getWord());
        }
        vatInvoiceResult.setTax(baiduVatInvoice.getTotalTax());
        vatInvoiceResult.setInvoiceUnitTaxpayerIdentificationNumber(baiduVatInvoice.getPurchaserRegisterNum());
        vatInvoiceResult.setBillAddress(baiduVatInvoice.getPurchaserAddress());
        vatInvoiceResult.setTicketCollectionUnit(baiduVatInvoice.getPurchaserName());
        vatInvoiceResult.setReceiveUnitTaxpayerIdentificationNumber(baiduVatInvoice.getSellerRegisterNum());
        vatInvoiceResult.setTicketReceiveAddress(baiduVatInvoice.getSellerAddress());
        vatInvoiceResult.setIssuer(baiduVatInvoice.getNoteDrawer());
        vatInvoiceResult.setReviewer(baiduVatInvoice.getChecker());
        vatInvoiceResult.setProvince(baiduVatInvoice.getProvince());
        vatInvoiceResult.setReceiptor(baiduVatInvoice.getPayee());
        List<BaiduVatInvoiceCommodityBaseDTO> commodityTaxRateList = baiduVatInvoice.getCommodityTaxRate();
        if (CollUtil.isNotEmpty(commodityTaxRateList)) {
            vatInvoiceResult.setTaxRate(commodityTaxRateList.get(0).getWord());
        }
        vatInvoiceResult.setCheckCode(baiduVatInvoice.getCheckCode());
        vatInvoiceResult.setRequestId(String.valueOf(baiduVatInvoiceResult.getLogId()));

        return vatInvoiceResult;
    }


    public static RoadTransportCertResult baiduRoadTransportLicenseResulttoRoadTransportCertResult(BaiduRoadTransportLicenseResult baiduRoadTransportLicenseResult) {
        if (baiduRoadTransportLicenseResult == null) {
            return null;
        }
        RoadTransportCertResult roadTransportCertResult = new RoadTransportCertResult();

        // 车牌号
        roadTransportCertResult.setCarNo(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_NO_AND_COLOR));
        // 号牌颜色
        roadTransportCertResult.setLicensePlateColor(getLicensePlateColor(roadTransportCertResult.getCarNo()));
        // 车辆类型
        roadTransportCertResult.setCarTypeName(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_TYPE));
        // 车辆载重
        roadTransportCertResult.setCarLoad(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_LOAD));
        // 车辆长度
        roadTransportCertResult.setCarLength(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_LENGTH));
        // 车辆宽度
        roadTransportCertResult.setCarWidth(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_WIDTH));
        // 车辆高度
        roadTransportCertResult.setCarHeight(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_HEIGHT));
        // 业户名称
        roadTransportCertResult.setCarName(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CAR_NAME));


        String validUntilDateStr = getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_UNTIL_DATE);
        roadTransportCertResult.setValidUntilDate(validUntilDateStr);
        if (StrUtil.isNotBlank(validUntilDateStr)) {
            validUntilDateStr = DateUtil.formatDate(validUntilDateStr, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            roadTransportCertResult.setValidUntilDateFormat(validUntilDateStr);
        }

        String verificationUntilDate = getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.HUNDREDWATTS_ROAD_TRANSPORT_VERIFICATION_UNTIL_DATE);
        roadTransportCertResult.setVerificationUntilDate(verificationUntilDate);
        if (StrUtil.isNotBlank(verificationUntilDate)) {
            verificationUntilDate = DateUtil.formatDate(verificationUntilDate, DateUtil.DATE_FORMAT_PATTERN7, DateUtil.DATE_FORMAT_PATTERN8);
            roadTransportCertResult.setVerificationUntilDateFormat(verificationUntilDate);
        }
        // 发证时间
        String timeData = getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_TIME_DATA);
        if (StrUtil.isNotBlank(timeData)) {
            roadTransportCertResult.setTimeData(timeData);
            timeData = DateUtil.formatDate(timeData, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            roadTransportCertResult.setTimeDataFormat(timeData);
        }
        // 经营许可证
        roadTransportCertResult.setBusinessLicense(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ROAD_BUSINESS_LICENSE));
        // 经营范围
        roadTransportCertResult.setBusinessScope(getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_BUSINESS_SCOPE));
        // 道路运输证号
        String roadTransportPermitNumber = getMultipleWord(baiduRoadTransportLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ROAD_TRANSPORT_PERMIT_NUMBER);
        if (StrUtil.isNotBlank(roadTransportPermitNumber)) {
            roadTransportPermitNumber = roadTransportPermitNumber.replaceAll("[\\u4e00-\\u9fa5]", "");
        }
        roadTransportCertResult.setRoadTransportPermitNumber(roadTransportPermitNumber);

        roadTransportCertResult.setRequestId(String.valueOf(baiduRoadTransportLicenseResult.getLogId()));


        return roadTransportCertResult;
    }

    /**
     * 获取单个word 无默认值
     */
    private static String getMultipleWord(Map<String, List<BaiduWord>> resultWords, String fieldName) {
        return getMultipleWord(resultWords, fieldName, null);
    }

    /**
     * 获取单个word 并设置默认值
     */
    private static String getMultipleWord(Map<String, List<BaiduWord>> resultWords, String fieldName, String defaultValue) {
        if (isNull(resultWords) || !resultWords.containsKey(fieldName)) {
            return defaultValue;
        }
        return resultWords.get(fieldName).get(0).getWord();
    }

    private static String getLicensePlateColor(String vehicleNo) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(vehicleNo)) {
            Pattern regex = Pattern.compile("\\((.*?)\\)");
            Matcher matcher = regex.matcher(vehicleNo);
            if (matcher.find()) {
                return matcher.group(1);
            }
        }
        return "";
    }


    public static VehicleLicenseResult baiduVehicleLicenseResulttoVehicleLicenseResult(BaiduVehicleLicenseResult baiduVehicleLicenseResult) {
        if (baiduVehicleLicenseResult == null) {
            return null;
        }
        VehicleLicenseResult vehicleLicenseResult = new VehicleLicenseResult();

        // 号牌号码
        vehicleLicenseResult.setVehicleNo(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VEHICLE_NO));
        // 号牌颜色
        vehicleLicenseResult.setLicensePlateColor(getLicensePlateColor(vehicleLicenseResult.getVehicleNo()));
        // 车辆识别代号
        vehicleLicenseResult.setVehicleIdentificationNumber(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VEHICLE_IDENTIFICATION_NUMBER));
        // 发动机号码
        vehicleLicenseResult.setEngineNumber(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ENGINE_NUMBER));
        // 所有人
        vehicleLicenseResult.setOwner(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_OWNER));
        // 使用性质
        vehicleLicenseResult.setUsage(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_USAGE));
        // 车辆类型
        vehicleLicenseResult.setVehicleType(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VEHICLE_TYPE));
        // 品牌型号
        vehicleLicenseResult.setBrandModel(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_BRAND_MODEL));
        // 住址
        vehicleLicenseResult.setAddress(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ADDRESS));
        // 发证单位
        vehicleLicenseResult.setIssuingAuthority(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VEHICLE_LICENSE_ISSUING_AUTHORITY));
        // 发证日期
        String issuingDate = getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ISSUING_DATE);
        if (StrUtil.isNotBlank(issuingDate)) {
            issuingDate = DateUtil.formatDate(issuingDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            vehicleLicenseResult.setIssuingDate(issuingDate);
            vehicleLicenseResult.setIssuingDateFormat(issuingDate);
        }
        // 注册日期
        String registrationDate = getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_REGISTRATION_DATE);
        if (StrUtil.isNotBlank(registrationDate)) {
            registrationDate = DateUtil.formatDate(registrationDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            vehicleLicenseResult.setRegistrationDate(registrationDate);
            vehicleLicenseResult.setRegistrationDateFormat(registrationDate);
        }
        // 检验记录
        vehicleLicenseResult.setInspectionRecord(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_INSPECTION_RECORD));
        // 核定载质量
        vehicleLicenseResult.setApprovedLoad(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_APPROVED_LOAD));
        // 整备质量
        vehicleLicenseResult.setCurbWeight(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CURB_WEIGHT));
        // 外廓尺寸
        vehicleLicenseResult.setExteriorDimensions(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_EXTERIOR_DIMENSIONS));
        // 核定载人数
        vehicleLicenseResult.setApprovedPassengers(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_APPROVED_PASSENGERS));
        // 总质量
        vehicleLicenseResult.setGrossWeight(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_GROSS_WEIGHT));
        // 燃油类型
        vehicleLicenseResult.setFuelType(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_FUEL_TYPE));
        // 准牵引总质量
        vehicleLicenseResult.setTowingCapacity(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_TOWING_CAPACITY));
        // 备注
        vehicleLicenseResult.setRemarks(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_REMARKS));
        // 证芯编号
        vehicleLicenseResult.setChipNumber(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CHIP_NUMBER));
        // 档案编号
        vehicleLicenseResult.setFileNumber(getWord(baiduVehicleLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_FILE_NUMBER));
        // 车辆尺寸
        if (StrUtil.isNotBlank(vehicleLicenseResult.getExteriorDimensions())) {
            BaiduVehicleSize sizeDTO = parsingSize(vehicleLicenseResult.getExteriorDimensions());
            vehicleLicenseResult.setCarLength(sizeDTO.getLength());
            vehicleLicenseResult.setCarWidth(sizeDTO.getWidth());
            vehicleLicenseResult.setCarHeight(sizeDTO.getHeight());
        }
        //图片质量检查
        String[] warnInfos = baiduVehicleLicenseResult.getWarnInfos();
        if (ObjectUtil.isNotEmpty(warnInfos) && warnInfos.length > 0) {
            vehicleLicenseResult.setWarnInfos(Arrays.asList(baiduVehicleLicenseResult.getWarnInfos()));
        }
        //驾驶证类型
        vehicleLicenseResult.setRiskType(baiduVehicleLicenseResult.getRiskType());
        //被编辑情况
        vehicleLicenseResult.setEditTool(baiduVehicleLicenseResult.getEditTool());

        vehicleLicenseResult.setRequestId(String.valueOf(baiduVehicleLicenseResult.getLogId()));


        return vehicleLicenseResult;
    }

    /**
     * 解析车辆外廓尺寸
     */
    private static BaiduVehicleSize parsingSize(String sizeStr) {
        BaiduVehicleSize sizeDTO = new BaiduVehicleSize();
        String pattern = "(\\d+)[*Xx](\\d+)[*Xx](\\d+)mm";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(sizeStr);
        if (matcher.find()) {
            String length = matcher.group(1);
            String width = matcher.group(2);
            String height = matcher.group(3);
            sizeDTO.setLength(length);
            sizeDTO.setWidth(width);
            sizeDTO.setHeight(height);
        }
        return sizeDTO;
    }


    public static DriverLicenseResult baiduDriverLicenseResulttoDriverLicenseResult(BaiduDriverLicenseResult baiduDriverLicenseResult) {
        if (baiduDriverLicenseResult == null) {
            return null;
        }
        DriverLicenseResult driverLicenseResult = new DriverLicenseResult();
        // 姓名
        driverLicenseResult.setName(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_NAME));
        // 出生日期
        String birthDate = getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_BIRTH_DATE);
        if (StrUtil.isNotBlank(birthDate)) {
            birthDate = DateUtil.formatDate(birthDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            driverLicenseResult.setBirthDate(birthDate);
            driverLicenseResult.setBirthDateFormat(birthDate);

        }
        driverLicenseResult.setIdNumber(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ID_NUMBER));
        // 住址
        driverLicenseResult.setAddress(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ADDRESS));
        // 初次领证日期
        String firstLicenseDate = getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_FIRST_LICENSE_DATE);
        if (StrUtil.isNotBlank(firstLicenseDate)) {
            firstLicenseDate = DateUtil.formatDate(firstLicenseDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            driverLicenseResult.setFirstLicenseDate(firstLicenseDate);
            driverLicenseResult.setFirstLicenseDateFormat(firstLicenseDate);
        }
        // 国籍
        driverLicenseResult.setNationality(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_NATIONALITY));
        // 准驾车型
        driverLicenseResult.setDrivingLicenseType(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.DRIVING_LICENSE_TYPE));
        // 性别
        driverLicenseResult.setGender(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_GENDER));
        // 发证单位
        driverLicenseResult.setIssuingAuthority(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ISSUING_AUTHORITY));
        // 有效期限
        String validityPeriod = getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VALIDITY_PERIOD);
        if (StrUtil.isNotBlank(validityPeriod)) {
            if ("长期".equals(validityPeriod)) {
                driverLicenseResult.setValidityPeriod(validityPeriod);
                driverLicenseResult.setValidityPeriodFormat(validityPeriod);
            } else {
                driverLicenseResult.setValidityPeriod(validityPeriod);
                validityPeriod = DateUtil.formatDate(validityPeriod, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
                driverLicenseResult.setValidityPeriodFormat(validityPeriod);
            }
        }
        // 有效期限至
        String untilDate = getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_UNTIL_DATE);
        if (StrUtil.isNotBlank(untilDate)) {
            driverLicenseResult.setUntilDate(untilDate);
            untilDate = DateUtil.formatDate(untilDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            driverLicenseResult.setUntilDateFormat(untilDate);
        }
        // 有效起始日期
        String validStartDate = getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VALID_START_DATE);
        if (StrUtil.isNotBlank(validStartDate)) {
            driverLicenseResult.setValidStartDate(validStartDate);
            validStartDate = DateUtil.formatDate(validStartDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            driverLicenseResult.setValidStartDateFormat(validStartDate);
        }
        // 失效日期
        String expirationDate = getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_EXPIRATION_DATE);
        if (StrUtil.isNotBlank(expirationDate)) {
            driverLicenseResult.setExpirationDate(expirationDate);
            expirationDate = DateUtil.formatDate(expirationDate, DateUtil.DATE_FORMAT_PATTERN9, DateUtil.DATE_FORMAT_PATTERN2);
            driverLicenseResult.setExpirationDateFormat(expirationDate);
        }
        // 状态
        driverLicenseResult.setStatus(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_STATUS));
        // 档案编号
        driverLicenseResult.setFileNumber(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_FILE_NUMBER));
        // 生成时间
        driverLicenseResult.setCreationTime(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CREATION_TIME));
        // 当前时间
        driverLicenseResult.setCurrentTime(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CURRENT_TIME));
        // 条形码下编号
        driverLicenseResult.setBarcodeNumber(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_BARCODE_NUMBER));
        // 累积记分
        driverLicenseResult.setAccumulatedScore(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ACCUMULATED_SCORE));
        // 记录
        driverLicenseResult.setRecord(getWord(baiduDriverLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_RECORD));
        //图片质量检查
        driverLicenseResult.setWarnInfos(baiduDriverLicenseResult.getWarnInfos());
        //驾驶证类型
        driverLicenseResult.setRiskType(baiduDriverLicenseResult.getRiskType());
        //被编辑情况
        driverLicenseResult.setEditTool(baiduDriverLicenseResult.getEditTool());
        driverLicenseResult.setRequestId(String.valueOf(baiduDriverLicenseResult.getLogId()));

        return driverLicenseResult;
    }


    public static BusinessLicenseResult baiduBusinessLicenseResulttoBusinessLicenseResult(BaiduBusinessLicenseResult baiduBusinessLicenseResult) {
        if (baiduBusinessLicenseResult == null) {
            return null;
        }
        BusinessLicenseResult businessLicenseResult = new BusinessLicenseResult();

        // 设置经营范围
        businessLicenseResult.setBusinessScope(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_BUSINESS_SCOPE));
        // 设置组成形式
        businessLicenseResult.setCompositionForm(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_COMPOSITION_FORM));
        // 设置法人
        businessLicenseResult.setLegalPerson(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_LEGAL_PERSON));
        // 设置证件编号
        businessLicenseResult.setCertificateNumber(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_CERTIFICATE_NUMBER));
        // 设置注册资本
        String registeredCapital = getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_REGISTERED_CAPITAL);
        businessLicenseResult.setRegisteredCapital(registeredCapital);
        if (StrUtil.isNotBlank(registeredCapital)) {

            boolean containsNumber = ReUtil.contains("\\d", registeredCapital);
            if (containsNumber) {
                long number = NumberUtil.parseInt(registeredCapital); // 提取数字部分并转换为整数
                String s = NumberChineseFormatter.format(Double.parseDouble(String.valueOf(number)), true);
                registeredCapital = s + registeredCapital.replaceAll("\\d", "");
            }

            //去除特殊字符
            registeredCapital = registeredCapital.replace("圆", "").replace("整", "").replace("(", "")
                    .replace("人", "").replace("民", "").replace("币", "")
                    .replace("元", "").replace(")", "");
            int capitalNumber = 0;
            BigDecimal capitalDecimal = BigDecimal.ZERO;
            try {
                capitalNumber = NumberChineseFormatter.chineseToNumber(registeredCapital);
            } catch (Exception e) {
                capitalDecimal = NumberChineseFormatter.chineseMoneyToNumber(registeredCapital);
            }
            if (capitalNumber != 0) {
                businessLicenseResult.setRegisteredCapitalNumber(String.valueOf(capitalNumber));
            } else {
                businessLicenseResult.setRegisteredCapitalNumber(String.valueOf(capitalDecimal.setScale(0, RoundingMode.DOWN)));
            }
        }
        // 设置单位名称
        businessLicenseResult.setCompanyName(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_COMPANY_NAME));
        // 设置有效期
        String validityPeriod = getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VALIDITY_PERIOD2);
        if (StrUtil.isNotBlank(validityPeriod)) {
            if ("长期".equals(validityPeriod)) {
                businessLicenseResult.setValidityPeriod(validityPeriod);
                businessLicenseResult.setValidityPeriodFormat(validityPeriod);
            } else {
                validityPeriod = DateUtil.formatDate(validityPeriod, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
                businessLicenseResult.setValidityPeriod(validityPeriod);
                businessLicenseResult.setValidityPeriodFormat(validityPeriod);

            }
        }
        // 设置社会信用代码
        businessLicenseResult.setSocialCreditCode(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_SOCIAL_CREDIT_CODE));
        // 设置实收资本
        businessLicenseResult.setPaidCapital(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_PAID_CAPITAL));
        // 设置有效期起始日期
        String validityStartDate = getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_VALIDITY_START_DATE);
        if (StrUtil.isNotBlank(validityStartDate)) {
            businessLicenseResult.setValidityStartDate(validityStartDate);
            validityStartDate = DateUtil.formatDate(validityStartDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            businessLicenseResult.setValidityStartDateFormat(validityStartDate);
        }
        // 设置核准日期
        String approvalDate = getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_APPROVAL_DATE);
        if (StrUtil.isNotBlank(approvalDate)) {
            businessLicenseResult.setApprovalDate(approvalDate);
            approvalDate = DateUtil.formatDate(approvalDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            businessLicenseResult.setApprovalDateFormat(approvalDate);
        }
        // 设置成立日期
        String establishmentDate = getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ESTABLISHMENT_DATE);
        if (StrUtil.isNotBlank(establishmentDate)) {
            businessLicenseResult.setEstablishmentDate(establishmentDate);
            establishmentDate = DateUtil.formatDate(establishmentDate, DateUtil.DATE_FORMAT_PATTERN4, DateUtil.DATE_FORMAT_PATTERN2);
            businessLicenseResult.setEstablishmentDateFormat(establishmentDate);
        }
        // 设置税务登记号
        businessLicenseResult.setTaxRegistrationNumber(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_SOCIAL_CREDIT_CODE));
        // 设置地址
        businessLicenseResult.setAddress(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_ADDRESS));
        // 设置登记机关
        businessLicenseResult.setRegistrationAuthority(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_REGISTRATION_AUTHORITY));
        // 设置类型
        businessLicenseResult.setType(getWord(baiduBusinessLicenseResult.getWordsResult(), OcrConstants.BAIDU_OCR_TYPE));
        // 营业执照类型
        businessLicenseResult.setRiskType(baiduBusinessLicenseResult.getRiskType());

        businessLicenseResult.setRequestId(String.valueOf(baiduBusinessLicenseResult.getLogId()));


        return businessLicenseResult;
    }


    public static VatInvoiceResult invoicetoVatInvoiceResult(Invoice invoice) {
        if (ObjectUtil.isEmpty(invoice)){
            return new VatInvoiceResult();
        }
        VatInvoiceResult vatInvoiceResult = new VatInvoiceResult();
        vatInvoiceResult.setInvoiceCode(invoice.getCode());
        vatInvoiceResult.setInvoiceNum(invoice.getNumber());
        vatInvoiceResult.setInvoiceDate(invoice.getDate());
        vatInvoiceResult.setInvoiceAmount(String.valueOf(invoice.getTotalAmount()));
        vatInvoiceResult.setTax(String.valueOf(invoice.getTaxAmount()));
        vatInvoiceResult.setInvoiceUnit(invoice.getSellerName());
        vatInvoiceResult.setInvoiceUnitTaxpayerIdentificationNumber(invoice.getSellerCode());
        vatInvoiceResult.setTicketCollectionUnit(invoice.getBuyerName());
        vatInvoiceResult.setReceiveUnitTaxpayerIdentificationNumber(invoice.getBuyerCode());
//        vatInvoiceResult.setIssuer(invoice.getSellerCode());//发行人
        vatInvoiceResult.setTaxRate(String.valueOf(invoice.getTaxRate().setScale(0, RoundingMode.DOWN)));
        vatInvoiceResult.setType(String.valueOf(invoice.getType()));
        return vatInvoiceResult;
    }
}
