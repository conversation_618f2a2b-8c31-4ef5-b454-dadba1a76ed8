package com.wzc.common.ocr.config;

import cn.hutool.core.util.ObjectUtil;
import com.wzc.common.ocr.client.*;
import com.wzc.common.ocr.common.base.OcrClientHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ocr服务自动装配
 *
 * <AUTHOR>
 * @date 2023/12/14
 */
@Slf4j
public class OcrAutoConfig {


    @Bean
    public OcrProperties ocrProperties() {
        return new OcrProperties();
    }

    @Bean
    public TimecapsuleProperties timecapsuleProperties() {
        return new TimecapsuleProperties();
    }

    @Bean
    public HundredWattsProperties hundredWattsProperties() {
        return new HundredWattsProperties();
    }

    @Bean
    public BaiduProperties baiduProperties() {
        return new BaiduProperties();
    }

    @Bean
    public RandomSupplierClient randomSuppilerClient(Map<String, OcrClientHandler> ocrClientHandlerMap) {
        return new RandomSupplierClient(ocrClientHandlerMap);
    }

    @Bean
    public TimecapsuleClient timecapsuleClient(TimecapsuleProperties timecapsuleProperties) {
        return new TimecapsuleClient(timecapsuleProperties);
    }
    @Bean
    public HundredWattsClient hundredWattsClient(HundredWattsProperties hundredWattsProperties) {
        return new HundredWattsClient(hundredWattsProperties);
    }

    @Bean
    public BaiduClient baiduClient(BaiduProperties baiduProperties) {
        return new BaiduClient(baiduProperties);
    }


    @Bean
    public OcrClient ocrClient(OcrProperties ocrProperties, List<OcrClientHandler> ocrClientHandlerList, RandomSupplierClient randomSupplierClient) {
        return new OcrClient(ocrProperties, ocrClientHandlerMap(ocrClientHandlerList), randomSupplierClient);
    }
    @Bean("ocrClientHandlerMap")
    public Map<String, OcrClientHandler> ocrClientHandlerMap(List<OcrClientHandler> ocrClientHandlerList) {
        return ocrClientHandlerList.stream()
                .peek(handler -> {
                    if (ObjectUtil.isEmpty(handler.getType())) {
                        throw new RuntimeException(String.format("方法名%s未定义", handler.getType()));
                    }
                }).collect(Collectors.toMap(OcrClientHandler::getType, Function.identity()));
    }

}
