package com.wzc.common.ocr.common.enums;

public enum VatinvoiceTypeEnum {
    ELECTRONIC_INVOICES_SPECIAL_VAT_INVOICES("1", "电子发票(增值税专用发票)"),
    ELECTRONIC_INVOICE_FREE_LIFE ("2", "电子发票(普通发票)"),
    PAPER_VAT_INVOICE("3", "纸质(增值税专用发票)"),
    PAPER_GENERAL_VAT_INVOICE("4", "纸质(增值税普通发票)"),
    ;

    private final String code;

    private final String name;

    VatinvoiceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
