package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

/**
 * 佰瓦磅单识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsIdCardAdvancedInfo {

    /**
     * 裁剪后身份证照片的base64编码
     */
    @Alias("IdCard")
    private String idCard;

    /**
     * 图片质量分数
     */
    @Alias("Quality")
    private Integer quality;

    /**
     * 身份证边框不完整告警阈值分数
     */
    @Alias("BorderCodeValue")
    private Integer borderCodeValue;

    /**
     * 告警信息
     */
    @Alias("WarnInfos")
    private List<Integer> warnInfos;
}
