package com.wzc.common.ocr.dto.base.result;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 驾驶证识别结果
 *
 * <AUTHOR>
 * @date 2023/12/15
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DriverLicenseResult extends OcrBaseResult {

    /**
     * "姓名
     */
    private String name;
    /**
     * 出生日期
     */
    private String birthDate;
    /**
     * 出生日期格式化字符串(yyyy-MM-dd)
     */
    private String birthDateFormat;
    /**
     * 证号
     */
    private String idNumber;
    /**
     * 住址
     */
    private String address;
    /**
     * 初次领证日期
     */
    private String firstLicenseDate;
    /**
     * 初次领证日期格式化字符串(yyyy-MM-dd)
     */
    private String firstLicenseDateFormat;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 准驾车型
     */
    private String drivingLicenseType;
    /**
     * 性别
     */
    private String gender;
    /**
     * 发证单位
     */
    private String issuingAuthority;
    /**
     * 有效期限
     */
    private String validityPeriod;
    /**
     * 有效期限格式化字符串(yyyy-MM-dd)
     */
    private String validityPeriodFormat;
    /**
     * 有效期至
     */
    private String untilDate;
    /**
     * 有效期至格式化字符串(yyyy-MM-dd)
     */
    private String untilDateFormat;
    /**
     * 有效起始日期(电子驾照)
     */
    private String validStartDate;
    /**
     * 有效起始日期格式化字符串(yyyy-MM-dd)
     */
    private String validStartDateFormat;
    /**
     * 失效日期(电子驾照)
     */
    private String expirationDate;
    /**
     * 失效日期格式化字符串(yyyy-MM-dd)
     */
    private String expirationDateFormat;
    /**
     * 状态
     */
    private String status;
    /**
     * 档案编号
     */
    private String fileNumber;
    /**
     * 生成时间
     */
    private String creationTime;
    /**
     * 当前时间
     */
    private String currentTime;
    /**
     * 条形码下编号
     */
    private String barcodeNumber;
    /**
     * 累积积分
     */
    private String accumulatedScore;
    /**
     * 记录
     */
    private String record;

    /**
     * 图片质量检查
     */
    private List<String> warnInfos;

    /**
     * 行驶证类型
     */
    private String riskType;

    /**
     * 被编辑情况
     */
    private String editTool;

    /**
     * 请求id
     */
    private String requestId;
}
