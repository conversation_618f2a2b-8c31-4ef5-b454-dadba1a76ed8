package com.wzc.common.ocr.dto.supplier.baidu;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * ocr身份证识别结果
 *
 * <AUTHOR>
 * @date 2023年06月26日
 */
@Data
public class BaiduDriverLicenseResult extends BaiduBaseResult implements Serializable {

    /**
     * 唯一的log id，用于问题定位
     */
    @Alias("log_id")
    private Long logId;

    /**
     * 图像方向，当 detect_direction=true 时返回该字段。
     * -1：未定义
     * 0：正向
     * 1：逆时针90度
     * 2：逆时针180度
     * 3：逆时针270度
     */
    @Alias("direction")
    private Integer direction;

    /**
     * 识别结果数，表示words_result的元素个数
     */
    @Alias("words_result_num")
    private Integer wordsResultNum;

    /**
     * 识别结果
     */
    @Alias("words_result")
    private Map<String, BaiduWords> wordsResult;

    /**
     * 识别结果字符串
     */
    @Alias("words")
    private String words;

    /**
     * 当输入参数 driving_license_side=front，且 quality_warn=true 时输出的告警提示
     * 可能的值包括：
     * - shield：驾驶证证照存在遮挡告警提示
     * - incomplete：驾驶证证照边框不完整告警提示
     */
    @Alias("warn_infos")
    private List<String> warnInfos;

    /**
     * 当输入参数 risk_warn=true 时返回识出的驾驶证的类型
     * 可能的值包括：
     * - normal：正常驾驶证
     * - copy：复印件
     * - screen：翻拍
     */
    @Alias("risk_type")
    private String riskType;

    /**
     * 当输入参数 risk_warn=true 时返回，如果检测驾驶证被编辑过，该字段指定编辑软件名称
     * 如：Adobe Photoshop CC 2014 (Macintosh)
     * 如果没有被编辑过则返回值为空
     */
    @Alias("edit_tool")
    private String editTool;
}
