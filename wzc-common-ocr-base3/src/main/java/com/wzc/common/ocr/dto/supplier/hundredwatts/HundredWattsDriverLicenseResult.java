package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.List;

/**
 * 佰瓦驾驶证识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsDriverLicenseResult {

    /**
     *
     */
    @Alias("Name")
    private String name;
    /**
     *
     */
    @<PERSON><PERSON>("Sex")
    private String sex;
    /**
     *
     */
    @<PERSON><PERSON>("Nationality")
    private String nationality;
    /**
     *
     */
    @Alias("Address")
    private String address;
    /**
     *
     */
    @<PERSON>as("DateOfBirth")
    private String dateOfBirth;
    /**
     *
     */
    @<PERSON>as("DateOfFirstIssue")
    private String dateOfFirstIssue;
    /**
     *
     */
    @Alias("Class")
    private String drivingLicenseType;
    /**
     *
     */
    @Alias("StartDate")
    private String startDate;
    /**
     *
     */
    @Alias("EndDate")
    private String endDate;
    /**
     *
     */
    @Alias("CardCode")
    private String cardCode;
    /**
     *
     */
    @Alias("ArchivesCode")
    private String archivesCode;
    /**
     *
     */
    @Alias("Record")
    private String record;
    /**
     *
     */
    @Alias("RecognizeWarnCode")
    private List<Integer> recognizeWarnCode;

    /**
     *
     */
    @Alias("RecognizeWarnMsg")
    private List<String> recognizeWarnMsg;
    /**
     *
     */
    @Alias("IssuingAuthority")
    private String issuingAuthority;
    /**
     *
     */
    @Alias("State")
    private String state;
    /**
     *
     */
    @Alias("CumulativeScore")
    private String cumulativeScore;
    /**
     *
     */
    @Alias("CurrentTime")
    private String currentTime;
    /**
     *
     */
    @Alias("GenerateTime")
    private String generateTime;
    /**
     *
     */
    @Alias("BackPageName")
    private String backPageName;
    /**
     *
     */
    @Alias("BackPageCardCode")
    private String backPageCardCode;
    /**
     *
     */
    @Alias("RequestId")
    private String requestId;
}



