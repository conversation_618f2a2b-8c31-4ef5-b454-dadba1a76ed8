package com.wzc.common.ocr.dto.base.result;/**
 * Create By hjh on 2023/10/19
 */

import lombok.Data;

/**
 *@ClassName OcrCardQuantity
 *@Description 身份证图片质量指标
 *<AUTHOR>
 *@Date 2023/10/19 17:18
 *@Version 1.0
 **/
@Data
public class IdCardQuantity {

    /**
     * 是否清晰
     */
    private String isClear;

    /**
     * 是否清晰概率
     */
    private String isClearPropobility;

    /**
     * 是否边框/四角完整
     */
    private String isComplete;

    /**
     * 是否边框/四角完整概率
     */
    private String isCompletePropobility;

    /**
     * 是否头像、关键字段无遮挡/马赛克
     */
    private String isNoCover;

    /**
     * 是否头像、关键字段无遮挡/马赛克概率
     */
    private String isNoCoverPropobility;
}
