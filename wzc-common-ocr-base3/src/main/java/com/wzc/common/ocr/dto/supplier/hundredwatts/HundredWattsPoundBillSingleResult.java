package com.wzc.common.ocr.dto.supplier.hundredwatts;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * 佰瓦磅单识别结果
 *
 * <AUTHOR>
 * @date 2023/12/18
 */
@Data
public class HundredWattsPoundBillSingleResult {

    /**
     * 皮重
     */
    @Alias("皮重")
    private HundredWattsPoundBillSingleBaseResult tareWeight;
    /**
     * 净重
     */
    @Alias("净重")
    private HundredWattsPoundBillSingleBaseResult netWeight;
    /**
     * 毛重
     */
    @Alias("毛重")
    private HundredWattsPoundBillSingleBaseResult grossWeight;

    /**
     * 车船号
     */
    @Alias("车船号")
    private HundredWattsPoundBillSingleBaseResult vehicleNo;
    /**
     * 日期
     */
    @Alias("日期")
    private HundredWattsPoundBillSingleBaseResult date;
    /**
     * 时间
     */
    @Alias("时间")
    private HundredWattsPoundBillSingleBaseResult time;

}
