package com.wzc.common.user.rpc.api;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.common.user.rpc.dto.MateDataListDTO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface BaseMateDataApi{

    @Operation(
            summary = "批量查询车辆信息",
            description = "批量查询车辆信息"
    )
    @PostMapping({"/inner/api/metadata/v1/car/query/list"})
    RestResponse<List<MateDataListDTO>> queryCarListByIds(@RequestBody List<Long> ids);

    @Operation(
            summary = "批量查询船舶信息",
            description = "批量查询船舶信息"
    )
    @PostMapping({"/inner/api/metadata/v1/ship/query/list"})
    RestResponse<List<MateDataListDTO>> queryShipList(@RequestBody List<Long> ids);
}
