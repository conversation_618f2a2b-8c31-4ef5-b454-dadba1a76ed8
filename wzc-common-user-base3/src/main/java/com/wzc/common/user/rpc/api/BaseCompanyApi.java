package com.wzc.common.user.rpc.api;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.common.user.rpc.dto.CompanyBaseDTO;
import com.wzc.common.user.rpc.qo.CompanyBaseQO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

public interface BaseCompanyApi {

    @PostMapping({"/inner/api/user/v1/company/queryById"})
    RestResponse<CompanyBaseDTO> queryByCompanyId(@RequestBody @Valid CompanyBaseQO qo);

    /**
     * 查询组织架构
     * @param idsQo
     * @return
     */
    @PostMapping("/inner/api/user/v1/common/batch/query/company")
    RestResponse<List<CompanyBaseDTO>> batchQuerySource(@Valid @RequestBody List<Long> idsQo);
}
