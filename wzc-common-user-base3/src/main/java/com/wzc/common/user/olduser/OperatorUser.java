package com.wzc.common.user.olduser;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @Author: sutao
 * @Date: 2019/11/1
 * @Description:
 */
@Data
@ToString
public class OperatorUser {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 用户code
     */
    private String userCode;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 操作人账号（82端用）
     */
    private String account;

    /**
     * 角色类型
     */
    private Integer roleType;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 角色用户名
     */
    private String roleName;

    /**
     * 公司id（如果登录的是货主角色那么这个属性有值）
     */
    private Long companyId;

    /**
     * 公司名字(如果登录的是货主角色那么这个属性有值）
     */
    private String companyName;

    /**
     * 登录设备 1-安卓；2-IOS；3-web
     */
    private Integer loginSource;

    /**
     * 登录设备号
     */
    private String loginDevice;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 子公司id
     */
    private String subCompanyId;

    /**
     * 子角色id
     */
    private String subRoleId;

    /**
     * 上级账号id
     */
    private Long parentRoleId;

    /**
     * 是否为托运公司(1.是 2.否)
     */
    private Integer trustFlag;

    /**
     * 数据权限
     */
    private List<Object> authData;

    /**
     * 当前选择公司
     */
    private String chooseCompany;

    /**
     * tokenKey
     */
    private String tokenKey;

    /**
     * 管理员账号:1-是,2-否
     */
    private Integer adminAccount;
}