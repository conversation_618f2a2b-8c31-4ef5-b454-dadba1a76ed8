package com.wzc.common.map.utils;

import com.wzc.common.map.model.address.AddressComponent;
import com.wzc.common.map.model.address.AddressResult;
import com.wzc.common.string.StringPool;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.awt.geom.Point2D;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 围栏内校验工具
 * <AUTHOR> Wei
 */
public class LocationUtils {

    @Data
    @AllArgsConstructor
    public static class Point {

        /**
         * 经度
         */
        private String lon;

        /**
         * 维度
         */
        private String lat;


        public Point(String location) {
            String[] split = location.split(",");
            BigDecimal num1 = new BigDecimal(split[0]);
            BigDecimal num2 = new BigDecimal(split[1]);
            if (num1.compareTo(num2) > 0) {
                this.lon = split[0];
                this.lat = split[1];
            } else {
                this.lon = split[1];
                this.lat = split[0];
            }
        }

        /**
         * 纬度在前坐标格式
         * 例如："38.2343432423,106.453453234234"
         * @return 纬度在前坐标格式
         */
        public String getLatLon(){
            return this.lat+","+this.lon;
        }

        /**
         * 经度在前坐标格式
         * 例如："106.453453234234,38.2343432423"
         * @return 纬度在前坐标格式
         */
        public String getLonLat(){
            return this.lon+","+this.lat;
        }
    }

    public static final double EARTH_RADIUS = 6378.137;
    public static final int N_2 = 2;
    public static final int N_1000 = 1000;
    public static final double N_180 = 180.0;

    /**
     * 是否在圆形围栏内
     *
     * @param center   中心点
     * @param location 定位坐标
     * @param radius   圆形围栏半径
     * @return false围栏外 true 围栏内
     */
    public static boolean isInCircular(Point center, Point location, BigDecimal radius) {
        BigDecimal distance = getDistance(center, location);
        return distance.compareTo(radius) < 0;
    }

    /**
     * 判断是否在多边形区域内
     *
     * @param location  要判断的点
     * @param pointList 多边形围栏点
     * @return false围栏外 true 围栏内
     */
    public static boolean isInPolygon(Point location, List<Point> pointList) {
        // 将要判断的横纵坐标组成一个点
        Point2D.Double point = new Point2D.Double(Double.parseDouble(location.getLon()), Double.parseDouble(location.getLat()));
        // 将区域各顶点的横纵坐标放到一个点集合里面
        List<Point2D.Double> points = new ArrayList<>();
        for (Point p : pointList) {
            Point2D.Double polygonPoint = new Point2D.Double(Double.parseDouble(p.getLon()), Double.parseDouble(p.getLat()));
            points.add(polygonPoint);
        }
        return areaCheck(point, points);
    }


    /**
     * 行政区围栏
     * @param location 定位
     * @param address 地址
     * @return false围栏外 true 围栏内
     */
    public static boolean isInDivision(String location,String address){
        Point point = new Point(location);
        AddressResult addressResult = ZBaiduUtils.addressReverseGeocoding(point.getLat(), point.getLon());
        if(Objects.isNull(addressResult)){
            return false;
        }
        if(Objects.isNull(addressResult.getAddressComponent())){
            return false;
        }
        AddressComponent addressComponent = addressResult.getAddressComponent();
        String province = addressComponent.getProvince();
        String city = addressComponent.getCity();
        String district = addressComponent.getDistrict();
        String districtArr = province + city + district;
        String cityArr = province + city;
        if (address.equals(districtArr)) {
            return true;
        } else if (address.equals(cityArr)) {
            return true;
        } else return address.equals(province);
    }


    /**
     * 判断是否在多边形区域内
     *
     * @param point   要判断的点
     * @param polygon 区域点集合
     */
    private static boolean areaCheck(Point2D.Double point, List<Point2D.Double> polygon) {
        java.awt.geom.GeneralPath generalPath = new java.awt.geom.GeneralPath();
        Point2D.Double first = polygon.get(0);
        // 通过移动到指定坐标（以双精度指定），将一个点添加到路径中
        generalPath.moveTo(first.x, first.y);
        polygon.remove(0);
        for (Point2D.Double d : polygon) {
            // 通过绘制一条从当前坐标到新指定坐标（以双精度指定）的直线，将一个点添加到路径中。
            generalPath.lineTo(d.x, d.y);
        }
        // 将几何多边形封闭
        generalPath.lineTo(first.x, first.y);
        generalPath.closePath();
        // 测试指定的 Point2D 是否在 Shape 的边界内。
        return generalPath.contains(point);
    }

    /**
     * 计算定位到中心点的直线距离
     *
     * @param center   中心点
     * @param location 位置
     * @return 直线距离 单位米
     */
    private static BigDecimal getDistance(Point center, Point location) {
        double lon1 = Double.parseDouble(center.getLon()) * Math.PI / N_180;
        double lat1 = Double.parseDouble(center.getLat()) * Math.PI / N_180;
        double lon2 = Double.parseDouble(location.getLon()) * Math.PI / N_180;
        double lat2 = Double.parseDouble(location.getLat()) * Math.PI / N_180;
        double dLon = lon2 - lon1;
        double dLat = lat2 - lat1;
        double a = Math.pow(Math.sin(dLat / N_2), N_2) + Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(dLon / N_2), N_2);
        double c = N_2 * Math.asin(Math.sqrt(a));
        double distance = c * EARTH_RADIUS * N_1000;
        return new BigDecimal(distance).setScale(4, RoundingMode.HALF_UP);
    }


    /**
     * 经纬度转换
     * @param location 经纬度
     */
    public static String getLocation(String location) {
        String[] split = location.split(StringPool.COMMA);
        String lon = split[0];
        String lat = split[1];
        BigDecimal toLon = new BigDecimal(split[0]);
        BigDecimal toLat = new BigDecimal(split[1]);
        if (toLat.compareTo(toLon) > 0) {
            lon = split[1];
            lat = split[0];
        }
        return lon + "," + lat;
    }
}
