package com.wzc.common.map.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wzc.common.map.constants.ZConstants;
import com.wzc.common.map.model.circle.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 百度-常用工具类
 *
 * <AUTHOR>
 * @Date: 2020-12-30 17:05
 * @Remark: 百度文档地址 http://lbsyun.baidu.com/index.php?title=webapi/guide/webservice-geocoding-abroad
 * @Version 1.0
 */
@Slf4j
public class ZBaiduAddressUtils {

    /**
     * 逆地理编码(根据坐标获取详细地址)  弃用，请调用third-party feign调用
     *  /inner/api/third-party/v1/track/reverseGeocoding
     * @param location 坐标格式:经度,纬度
     * @return
     */
    @Deprecated
    public static JSONObject getAddress(String location) {
        if (StringUtils.isBlank(location)) {
            return null;
        }
        String[] split = location.split(",");
        if (split.length > 1) {
            location = split[1] + "," + split[0];
        }
        String url = "http://api.map.baidu.com/reverse_geocoding/v3/" +
                "?ak=" + ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK + "&output=json&location=" + location;
        HttpGet get = new HttpGet(url);
        try {
            HttpClient client = new DefaultHttpClient();
            HttpResponse response = client.execute(get);
            if (response.getEntity() != null) {
                String responseString = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONUtil.parseObj(responseString);
                if (jsonObject.getInt("status") == 0) {
                    String result = jsonObject.getStr("result");
                    JSONObject jsonDate = JSONUtil.parseObj(result);
                    return jsonDate;
                }
            }
        } catch (Exception e) {
            log.info("查询逆地理编码(根据坐标获取详细地址)异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 创建圆形围栏
     *
     * @param createCircleFenceInfo
     * @return
     */
    public static Integer createCircleFence(CreateCircleFenceInfo createCircleFenceInfo) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;

        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(550000).setConnectTimeout(550000)
                .setConnectionRequestTimeout(550000).setStaleConnectionCheckEnabled(true).build();

        client = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
        try {
            URIBuilder uriBuilder = new URIBuilder("http://yingyan.baidu.com/api/v3/fence/createcirclefence");

            HttpPost post = new HttpPost(uriBuilder.build());

            // 构造消息头
            post.setHeader("Charset", "UTF-8");
            post.setHeader("Connection", "Keep-Alive");
            post.setHeader("Content-type", "application/x-www-form-urlencoded");

            HashMap map = new HashMap();
            map.put("ak", ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK);
            map.put("service_id", ZConstants.SERVICE_ID);
            map.put("fence_name", createCircleFenceInfo.getFence_name());
            map.put("monitored_person", createCircleFenceInfo.getMonitored_person());
            map.put("longitude", createCircleFenceInfo.getLongitude());
            map.put("latitude", createCircleFenceInfo.getLatitude());
            map.put("radius", createCircleFenceInfo.getRadius());
            map.put("coord_type", createCircleFenceInfo.getCoord_type());
            map.put("denoise", createCircleFenceInfo.getDenoise());
            Iterator<Map.Entry> it = map.entrySet().iterator();
            List params = new ArrayList();
            while (it.hasNext()) {
                Map.Entry entry = it.next();
                NameValuePair pair = new BasicNameValuePair(entry.getKey().toString(), entry.getValue().toString());
                params.add(pair);
            }

            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            response = client.execute(post);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    JSONObject jsonObject = JSONUtil.parseObj(EntityUtils.toString(resEntity, "UTF-8"));
                    int status = jsonObject.getInt("status");
                    if (status == 0) {
                        return jsonObject.getInt("fence_id");
                    }
                }
            }
        } catch (Exception e) {
            log.info("创建圆形围栏异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 创建多边形围栏
     *
     * @param createPolygonFenceInfo
     * @return
     */
    public static Integer createPolygonFence(CreatePolygonFenceInfo createPolygonFenceInfo) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;

        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(550000).setConnectTimeout(550000)
                .setConnectionRequestTimeout(550000).setStaleConnectionCheckEnabled(true).build();

        client = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
        try {
            URIBuilder uriBuilder = new URIBuilder("http://yingyan.baidu.com/api/v3/fence/createpolygonfence");

            HttpPost post = new HttpPost(uriBuilder.build());

            // 构造消息头
            post.setHeader("Charset", "UTF-8");
            post.setHeader("Connection", "Keep-Alive");
            post.setHeader("Content-type", "application/x-www-form-urlencoded");

            HashMap map = new HashMap();
            map.put("ak", ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK);
            map.put("service_id", ZConstants.SERVICE_ID);
            map.put("fence_name", createPolygonFenceInfo.getFence_name());
            map.put("monitored_person", createPolygonFenceInfo.getMonitored_person());
            map.put("vertexes", createPolygonFenceInfo.getVertexes());
            map.put("coord_type", createPolygonFenceInfo.getCoord_type());
            map.put("denoise", createPolygonFenceInfo.getDenoise());

            Iterator<Map.Entry> it = map.entrySet().iterator();

            List params = new ArrayList();

            while (it.hasNext()) {
                Map.Entry entry = it.next();
                NameValuePair pair = new BasicNameValuePair(entry.getKey().toString(), entry.getValue().toString());
                params.add(pair);
            }

            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            response = client.execute(post);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    JSONObject jsonObject = JSONUtil.parseObj(EntityUtils.toString(resEntity, "UTF-8"));
                    int status = jsonObject.getInt("status");
                    if (status == 0) {
                        return jsonObject.getInt("fence_id");
                    }
                }
            }
        } catch (Exception e) {
            log.info("创建多边形围栏异常:{}", e.getMessage());
        }
        return null;
    }


    /**
     * 创建行政区围栏
     *
     * @param createDistrictFenceInfo
     * @return
     */
    public static Integer createDistrictFence(CreateDistrictFenceInfo createDistrictFenceInfo) {
        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;

        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(550000).setConnectTimeout(550000)
                .setConnectionRequestTimeout(550000).setStaleConnectionCheckEnabled(true).build();

        client = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
        try {
            URIBuilder uriBuilder = new URIBuilder("http://yingyan.baidu.com/api/v3/fence/createdistrictfence");

            HttpPost post = new HttpPost(uriBuilder.build());

            // 构造消息头
            post.setHeader("Charset", "UTF-8");
            post.setHeader("Connection", "Keep-Alive");
            post.setHeader("Content-type", "application/x-www-form-urlencoded");

            HashMap map = new HashMap();
            map.put("ak", ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK);
            map.put("service_id", ZConstants.SERVICE_ID);
            map.put("fence_name", createDistrictFenceInfo.getFence_name());
            map.put("monitored_person", createDistrictFenceInfo.getMonitored_person());
            map.put("keyword", createDistrictFenceInfo.getKeyword());
            map.put("denoise", createDistrictFenceInfo.getDenoise());

            Iterator<Map.Entry> it = map.entrySet().iterator();

            List params = new ArrayList();
            while (it.hasNext()) {
                Map.Entry entry = it.next();
                NameValuePair pair = new BasicNameValuePair(entry.getKey().toString(), entry.getValue().toString());
                params.add(pair);
            }
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            response = client.execute(post);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    JSONObject jsonObject = JSONUtil.parseObj(EntityUtils.toString(resEntity, "UTF-8"));
                    int status = jsonObject.getInt("status");
                    if (status == 0) {
                        return jsonObject.getInt("fence_id");
                    }
                }
            }
        } catch (Exception e) {
            log.info("创建行政区围栏异常:{}", e.getMessage());
        }
        return null;
    }


    /**
     * 删除围栏
     *
     * @param deleteFenceInfo
     */
    public static String deleteFence(DeleteFenceInfo deleteFenceInfo) {

        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;

        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(550000).setConnectTimeout(550000)
                .setConnectionRequestTimeout(550000).setStaleConnectionCheckEnabled(true).build();

        client = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
        try {
            URIBuilder uriBuilder = new URIBuilder("http://yingyan.baidu.com/api/v3/fence/delete");

            HttpPost post = new HttpPost(uriBuilder.build());

            // 构造消息头
            post.setHeader("Charset", "UTF-8");
            post.setHeader("Connection", "Keep-Alive");
            post.setHeader("Content-type", "application/x-www-form-urlencoded");

            HashMap map = new HashMap();
            map.put("ak", ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK);
            map.put("service_id", ZConstants.SERVICE_ID);
            map.put("monitored_person", deleteFenceInfo.getMonitored_person());
            map.put("fence_ids", deleteFenceInfo.getFence_ids());

            Iterator<Map.Entry> it = map.entrySet().iterator();

            List params = new ArrayList();
            while (it.hasNext()) {
                Map.Entry entry = it.next();
                NameValuePair pair = new BasicNameValuePair(entry.getKey().toString(), entry.getValue().toString());
                params.add(pair);
            }
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            response = client.execute(post);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    JSONObject jsonObject = JSONUtil.parseObj(EntityUtils.toString(resEntity, "UTF-8"));
                    int status = jsonObject.getInt("status");
                    if (status == 0) {
                        return jsonObject.getStr("fence_ids");
                    }
                }
            }
        } catch (Exception e) {
            log.info("删除围栏异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 查询监控对象相对围栏的状态
     *
     * @param queryStatusInfo
     * @return
     */
    public static JSONArray queryStatus(QueryStatusInfo queryStatusInfo) {
        String url = "http://yingyan.baidu.com/api/v3/fence/querystatus?service_id=" +
                ZConstants.SERVICE_ID + "&monitored_person=" +
                queryStatusInfo.getMonitored_person() + "&fence_ids=" +
                queryStatusInfo.getFence_ids() +
                "&ak=" + ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK;
        HttpGet get = new HttpGet(url);
        try {
            HttpClient client = new DefaultHttpClient();
            HttpResponse response = client.execute(get);
            if (response.getEntity() != null) {
                String responseString = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONUtil.parseObj(responseString);
                if (jsonObject.getInt("status") == 0) {
                    JSONArray monitored_statuses = jsonObject.getJSONArray("monitored_statuses");
                    return monitored_statuses;
                }
            }
        } catch (Exception e) {
            log.info("查询监控对象相对围栏的状态异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据坐标查询监控对象相对围栏的状态
     *
     * @param queryStatusInfo
     * @return
     */
    public static JSONArray queryStatusByLocation(QueryStatusInfo queryStatusInfo) {
        String url = "http://yingyan.baidu.com/api/v3/fence/querystatusbylocation" +
                "?service_id=" + ZConstants.SERVICE_ID +
                "&ak=" + ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK +
                "&monitored_person=" + queryStatusInfo.getMonitored_person() +
                "&longitude=" + queryStatusInfo.getLongitude() +
                "&latitude=" + queryStatusInfo.getLatitude() +
                "&coord_type=" + queryStatusInfo.getCoord_type();
        HttpGet get = new HttpGet(url);
        try {
            HttpClient client = new DefaultHttpClient();
            HttpResponse response = client.execute(get);
            if (response.getEntity() != null) {
                String responseString = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONUtil.parseObj(responseString);
                if (jsonObject.getInt("status") == 0) {
                    JSONArray monitored_statuses = jsonObject.getJSONArray("monitored_statuses");
                    return monitored_statuses;
                }
            }
        } catch (Exception e) {
            log.info("查询监控对象相对围栏的状态异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据活跃时间查询entity
     *
     * @param time 时间 时间戳
     * @return
     */
    public static Integer getEntityByTime(String time) {

        String url = "http://yingyan.baidu.com/api/v3/entity/list" +
                "?ak=" + ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK + "&service_id=" + ZConstants.SERVICE_ID + "&filter=active_time:" +
                time;
        HttpGet get = new HttpGet(url);
        try {
            HttpClient client = new DefaultHttpClient();
            HttpResponse response = client.execute(get);
            if (response.getEntity() != null) {
                String responseString = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONUtil.parseObj(responseString);
                if (jsonObject.getInt("status") == 0) {
                    Integer result = jsonObject.getInt("total");
                    return result;
                }
            }
        } catch (Exception e) {
            log.info("根据活跃时间查询entity异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 查询轨迹
     *
     * @param req
     */
    public static JSONObject trackAnalysis(TrackAnalysisReq req) {

        CloseableHttpClient client = null;
        CloseableHttpResponse response = null;

        RequestConfig defaultRequestConfig = RequestConfig.custom().setSocketTimeout(550000).setConnectTimeout(550000)
                .setConnectionRequestTimeout(550000).setStaleConnectionCheckEnabled(true).build();

        client = HttpClients.custom().setDefaultRequestConfig(defaultRequestConfig).build();
        try {
            URIBuilder uriBuilder = new URIBuilder("http://api.map.baidu.com/trackmatch/v1/track");

            HttpPost post = new HttpPost(uriBuilder.build());

            // 构造消息头
            post.setHeader("Charset", "UTF-8");
            post.setHeader("Connection", "Keep-Alive");
            post.setHeader("Content-type", "application/x-www-form-urlencoded");

            HashMap map = new HashMap();
            map.put("ak", ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK);
            map.put("standard_track", req.getStandard_track());
            map.put("track", req.getTrack());

            Iterator<Map.Entry> it = map.entrySet().iterator();

            List params = new ArrayList();
            while (it.hasNext()) {
                Map.Entry entry = it.next();
                NameValuePair pair = new BasicNameValuePair(entry.getKey().toString(), entry.getValue().toString());
                params.add(pair);
            }
            post.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            response = client.execute(post);
            if (response != null) {
                HttpEntity resEntity = response.getEntity();
                if (resEntity != null) {
                    JSONObject jsonObject = JSONUtil.parseObj(EntityUtils.toString(resEntity, "UTF-8"));
                    return jsonObject;
                }
            }
        } catch (Exception e) {
            log.info("轨迹重合度匹配异常:{}", e.getMessage());
        }
        return null;
    }

    /**
     * 轨迹查询与纠偏
     *
     * @param req
     * @return
     */
    public static JSONObject getTrack(GetTrackReq req) {
        String url = "http://yingyan.baidu.com/api/v3/track/gettrack" +
                "?service_id=" + ZConstants.SERVICE_ID +
                "&ak=" + ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK +
                "&entity_name=" + req.getEntity_name() +
                "&start_time=" + req.getStart_time() +
                "&start_time=" + req.getEnd_time() +
                "&is_processed=" + req.getIs_processed() +
                "&page_index=" + req.getPage_index() +
                "&page_size=" + req.getPage_size();
        HttpGet get = new HttpGet(url);
        try {
            HttpClient client = new DefaultHttpClient();
            HttpResponse response = client.execute(get);
            if (response.getEntity() != null) {
                String responseString = EntityUtils.toString(response.getEntity());
                return JSONUtil.parseObj(responseString);
            }
        } catch (Exception e) {
            log.info("轨迹查询与纠偏:{}", e.getMessage());
        }
        return null;
    }


    /**
     * 逆地理编码(根据坐标获取详细地址)
     *
     * @param address 坐标地址
     * @return
     */
    public static JSONObject getLocation(String address) {
        if (StringUtils.isBlank(address)) {
            return null;
        }

        String url = "https://api.map.baidu.com/geocoding/v3/?address=" + address + "&output=json&ak=" +
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK;
        HttpGet get = new HttpGet(url);
        try {
            HttpClient client = new DefaultHttpClient();
            HttpResponse response = client.execute(get);
            if (response.getEntity() != null) {
                String responseString = EntityUtils.toString(response.getEntity());
                JSONObject jsonObject = JSONUtil.parseObj(responseString);
                return jsonObject;
            }
        } catch (Exception e) {
            log.info("查询逆地理编码(根据坐标获取详细地址)异常:{}", e.getMessage());
        }
        return null;
    }
}
