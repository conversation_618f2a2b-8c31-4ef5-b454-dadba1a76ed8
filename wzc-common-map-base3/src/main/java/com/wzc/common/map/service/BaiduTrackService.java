package com.wzc.common.map.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidubce.services.lps.model.DirectionResponse;
import com.baidubce.services.lps.model.RouteMatrixResponse;
import com.wzc.common.map.constants.ZConstants;
import com.wzc.common.map.model.track.BaiduPlanRouteBatchReq;
import com.wzc.common.map.model.track.BaiduPlanRoutePlanReq;
import com.wzc.common.map.model.track.BaiduRoutePlanningReq;
import com.wzc.common.map.model.track.BaiduTrackMatchReq;
import com.wzc.common.map.utils.ZBaiduLogisticsDirectionTrackUtils;
import com.wzc.common.map.utils.ZBaiduRoutePlanningUtils;
import com.wzc.common.map.utils.ZBaiduTrackMatchUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class BaiduTrackService {

    /**
     * 货车路线规划
     *
     * @param req
     * @return
     */
    public DirectionResponse.PathRoute planRoute(BaiduPlanRoutePlanReq req) {
        // 联网获取数据
        String resultStr = ZBaiduLogisticsDirectionTrackUtils.planRoute(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getOrigin(),
                req.getDestination(),
                req.getWaypoints(),
                req.getTactics(),
                req.getHeight(),
                req.getWidth(),
                req.getWeight(),
                req.getLength(),
                req.getAxleWeight(),
                req.getAxleCount(),
                req.getIsTrailer()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度货车路径规划接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        JSONObject result = resultJson.getJSONObject("result");
        if (null != result) {
            result.remove("restriction");
        }
        return JSONUtil.toBean(result, DirectionResponse.PathRoute.class);
    }

    /**
     * 经验路线规划
     *
     * @param req
     * @return
     */
    public DirectionResponse.PathRoute routePlanning(BaiduRoutePlanningReq req) {
        // 联网获取数据
        String resultStr = ZBaiduRoutePlanningUtils.routePlanning(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getOrigin(),
                req.getDestination()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度经验路线规划接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        JSONObject result = resultJson.getJSONObject("result");
        if (null != result) {
            result.remove("restriction");
        }
        return JSONUtil.toBean(result, DirectionResponse.PathRoute.class);
    }

    /**
     * 调用百度轨迹重合率接口
     *
     * @param req
     * @return
     */
    public String getTrackMatch(BaiduTrackMatchReq req) {
        // 联网获取数据
        String resultStr = ZBaiduTrackMatchUtils.getTrackMatch(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getStandardTrack(),
                req.getTrack(),
                req.getStandardOption(),
                req.getOption(),
                req.getCoordTypeInput(),
                req.getCoordTypeOutput(),
                req.getSn()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度轨迹重合率接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        return JSONUtil.toBean(resultJson, String.class);
    }

    /**
     * 货车批量算路查询接口
     *
     * @param req
     * @return
     */
    public RouteMatrixResponse.Distance planRouteBatch(BaiduPlanRouteBatchReq req) {
        // 联网获取数据
        String resultStr = ZBaiduLogisticsDirectionTrackUtils.planRouteBatch(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getOrigins(),
                req.getDestinations(),
                req.getCoordType(),
                req.getHeight(),
                req.getWidth(),
                req.getWeight(),
                req.getLength(),
                req.getAxleWeight(),
                req.getAxleCount(),
                req.getIsTrailer(),
                req.getPlateProvince(),
                req.getPlateNumber(),
                req.getPlateColor(),
                req.getDepartureTime(),
                req.getTactics(),
                req.getAvoidType(),
                req.getUserMark(),
                req.getSn()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度货车批量算路接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        JSONObject result = resultJson.getJSONObject("result");
        if (null != result) {
            result.remove("restriction");
        }
        return JSONUtil.toBean(result, RouteMatrixResponse.Distance.class);
    }
}
