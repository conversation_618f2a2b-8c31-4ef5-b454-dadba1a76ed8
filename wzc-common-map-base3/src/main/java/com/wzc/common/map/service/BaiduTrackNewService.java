package com.wzc.common.map.service;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidubce.services.lps.model.DirectionResponse;
import com.baidubce.services.lps.model.RouteMatrixResponse;
import com.wzc.common.map.constants.ZConstants;
import com.wzc.common.map.model.track.*;
import com.wzc.common.map.utils.ZBaiduLogisticsDirectionTrackNewUtils;
import com.wzc.common.map.utils.ZBaiduLogisticsDirectionTrackUtils;
import com.wzc.common.map.utils.ZBaiduRoutePlanningUtils;
import com.wzc.common.map.utils.ZBaiduTrackMatchUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class BaiduTrackNewService {

    /**
     * 货车路线规划
     *
     * @param req
     * @return
     */
    public BaiduPlanRoutePlanRes planRoute(BaiduPlanRoutePlanReq req) {
        // 联网获取数据
        String resultStr = ZBaiduLogisticsDirectionTrackNewUtils.planRoute(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getOrigin(),
                req.getDestination(),
                req.getWaypoints(),
                req.getTactics(),
                req.getHeight(),
                req.getWidth(),
                req.getWeight(),
                req.getLength(),
                req.getAxleWeight(),
                req.getAxleCount(),
                req.getIsTrailer()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度货车路径规划接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        BaiduPlanRoutePlanRes baiduPlanRoutePlanRes=new BaiduPlanRoutePlanRes();
        // 返回的结果
        JSONObject result = resultJson.getJSONObject("result");
        if (null != result) {
            result.remove("restriction");
            String trajectorySid=result.getStr("trajectory_sid");
            baiduPlanRoutePlanRes.setTrajectorySid(trajectorySid);
        }
        DirectionResponse.PathRoute pathRoute=JSONUtil.toBean(result, DirectionResponse.PathRoute.class);
        baiduPlanRoutePlanRes.setPathRoute(pathRoute);
        return baiduPlanRoutePlanRes;
    }
/*
    *//**
     * 经验路线规划
     *
     * @param req
     * @return
     *//*
    public DirectionResponse.PathRoute routePlanning(BaiduRoutePlanningReq req) {
        // 联网获取数据
        String resultStr = ZBaiduRoutePlanningUtils.routePlanning(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getOrigin(),
                req.getDestination()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度经验路线规划接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        JSONObject result = resultJson.getJSONObject("result");
        if (null != result) {
            result.remove("restriction");
        }
        return JSONUtil.toBean(result, DirectionResponse.PathRoute.class);
    }

    *//**
     * 调用百度轨迹重合率接口
     *
     * @param req
     * @return
     *//*
    public String getTrackMatch(BaiduTrackMatchReq req) {
        // 联网获取数据
        String resultStr = ZBaiduTrackMatchUtils.getTrackMatch(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getStandardTrack(),
                req.getTrack(),
                req.getStandardOption(),
                req.getOption(),
                req.getCoordTypeInput(),
                req.getCoordTypeOutput(),
                req.getSn()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度轨迹重合率接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        return JSONUtil.toBean(resultJson, String.class);
    }

    *//**
     * 货车批量算路查询接口
     *
     * @param req
     * @return
     *//*
    public RouteMatrixResponse.Distance planRouteBatch(BaiduPlanRouteBatchReq req) {
        // 联网获取数据
        String resultStr = ZBaiduLogisticsDirectionTrackUtils.planRouteBatch(
                ZConstants.BAIDU_HAWKEYE_WEBSERVER_TRACK_AK,
                req.getOrigins(),
                req.getDestinations(),
                req.getCoordType(),
                req.getHeight(),
                req.getWidth(),
                req.getWeight(),
                req.getLength(),
                req.getAxleWeight(),
                req.getAxleCount(),
                req.getIsTrailer(),
                req.getPlateProvince(),
                req.getPlateNumber(),
                req.getPlateColor(),
                req.getDepartureTime(),
                req.getTactics(),
                req.getAvoidType(),
                req.getUserMark(),
                req.getSn()
        );
        // 请求结果转为JsonObject
        JSONObject resultJson = JSONUtil.parseObj(resultStr);
        if (resultJson.getInt("status") != 0) {
            log.warn("百度货车批量算路接口: param: {}, result: {}", JSONUtil.toJsonStr(req), resultJson);
        }
        // 返回的结果
        JSONObject result = resultJson.getJSONObject("result");
        if (null != result) {
            result.remove("restriction");
        }
        return JSONUtil.toBean(result, RouteMatrixResponse.Distance.class);
    }*/

    public static void main(String[] args) {

        BaiduPlanRoutePlanReq req=new BaiduPlanRoutePlanReq();
        req.setOrigin("44.35838179672341,85.70826948253733");
        req.setDestination("39.913581,116.404763");
        req.setTactics(0);
        req.setHeight(1.8);
        req.setWidth(1.9);
        req.setWeight(2.5);
        req.setLength(4.2);
        req.setAxleWeight(2);
        req.setAxleCount(2);
        req.setIsTrailer(1);

        BaiduPlanRoutePlanRes baiduPlanRoutePlanRes = BaiduTrackNewService.planRoute(req);
        log.warn("百度货车路径规划接口result: {}", JSONUtil.toJsonStr(baiduPlanRoutePlanRes));

    }
}
