package com.wzc.common.map.utils;

import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/9/15
 * @Remark: 百度文档地址 https://lbsyun.baidu.com/index.php?title=webapi/direction-api-v2
 */
@Slf4j
public class ZBaiduRoutePlanningUtils {

    /**
     * 经验路线规划
     *
     * @param ak          ak
     * @param origin      起点坐标     格式为：纬度,经度。如：21.22345,112.11478
     * @param destination 格式与起点坐标相同
     * @return 响应
     */
    public static String routePlanning(
            String ak,
            String origin,
            String destination
    ) {
        String urlAccessToken;
        urlAccessToken = "https://api.map.baidu.com/direction/v2/driving?"
                + "ak=" + ak
                + "&origin=" + origin
                + "&destination=" + destination;
        String responseString = HttpUtil.get(urlAccessToken, 5000);
        if (null == responseString) {
            log.error("BaiduMap Service Error: {}", "经验路线规划接口调用失败");
            return "-1";
        }
        return responseString;
    }
}
