package com.wzc.common.map.model.gps;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.wzc.common.string.StringPool;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class GpsDTO {

    double lon;
    double lat;

    @Override
    public String toString(){
        return StrUtil.concat(true, Convert.toStr(lon), StringPool.COMMA,Convert.toStr(lat));
    }
}
