package com.wzc.common.map.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 百度-货车路线规划
 * 200点以内，暂时用不到
 *
 * <AUTHOR>
 * @Date: 2020-08-13 15:05
 * @Remark: 百度文档地址 http://lbsyun.baidu.com/index.php?title=webapi/direction-api-truck
 * @Version 1.0
 */
@Slf4j
public class ZBaiduLogisticsDirectionTrackUtils {

    /**
     * 货车路径规划查询接口  弃用  请调用  /inner/api/third-party/v1/track/planRoute
     *
     * @param ak
     * @param origin      起点坐标     格式为：纬度,经度。如：21.22345,112.11478
     * @param destination 终点坐标     格式与起点坐标相同
     * @param waypoints   途经点坐标   格式：支持20个以内的有序途径点。多个途径点坐标按顺序以英文竖线符号分隔，示例： 40.465,116.314|40.232,116.352|40.121,116.453
     * @param tactics     驾驶策略
     *                    0：默认 （时间优先）
     *                    1：距离优先
     *                    3：不走高速
     *                    7：经济路线
     *                    4：高速优先
     *                    5：躲避拥堵
     *                    6：少收费     *
     * @param height      车辆高度     单位：米，取值[0,5.0]，默认1.8，会按照填写数字进行限行规避
     * @param width       车辆宽度     单位：米，取值[0,3.0]，默认1.9，会按照填写数字进行限行规避
     * @param weight      车辆总重     车辆总重=车辆自身重量+货物重量，单位：吨，取值[0,100]，默认2.5，会按照填写数字进行限行规避
     * @param length      车辆长度     单位：米，取值[0,20.0]，默认4.2，会按照填写数字进行限行规避
     * @param axle_weight 轴重         单位：吨，取值[0,50]，默认2，会按照填写数字进行限行规避
     * @param axle_count  轴数         取值[0,50]，默认2，会按照填写数字进行限行规避
     * @param is_trailer  是否是挂车    0否1是
     * @return
     */
    @Deprecated
    public static String planRoute(
            String ak,
            String origin,
            String destination,
            String waypoints,
            Integer tactics,
            Double height,
            Double width,
            Double weight,
            Double length,
            Integer axle_weight,
            Integer axle_count,
            Integer is_trailer
    ) {
        String urlAccessToken = "http://api.map.baidu.com/logistics_direction/v1/truck?"
                + "ak=" + ak
                + "&origin=" + origin
                + "&destination=" + destination
                + (StrUtil.isEmpty(waypoints) ? "" : ("&waypoints=" + waypoints))
                + "&height=" + height
                + "&width=" + width
                + "&weight=" + weight
                + "&length=" + length
                + "&axle_weight=" + axle_weight
                + "&axle_count=" + axle_count
//                + "&is_trailer=" + is_trailer
                + "&tactics=" + tactics;
        String responseString = HttpUtil.get(urlAccessToken, 5000);
        if (null == responseString) {
            log.error("BaiduMap Service Error: {}", "货车路径规划查询接口调用失败");
            return "-1" ;
        }
        return responseString;
    }

    /**
     * 货车批量算路查询接口
     *
     * @param ak             起点坐标串 格式为：纬度,经度|纬度,经度。如：21.22345,112.11478|21.47832,112.37854 注：起终点个数乘积不超过50
     * @param origins        终点坐标串 格式与起点坐标相同 注：起终点个数乘积不超过50
     * @param destinations   输入坐标类型
     *                       坐标类型，可选参数，默认为bd09ll 允许的值为：
     *                       bd09ll（百度经纬度坐标）
     *                       bd09mc（百度墨卡托坐标）
     *                       gcj02（国测局加密坐标）
     *                       wgs84（gps设备获取的坐标）
     * @param coord_type     车辆高度  单位：米，取值[0,5.0]，默认1.8，会按照填写数字进行限行规避，字段类型double，单位：米
     * @param height         车辆宽度 单位：米，取值[0,3.0]，默认1.9，会按照填写数字进行限行规避，字段类型double，单位：米
     * @param width          车辆总重 车辆总重=车辆自身重量+货物重量，单位：吨，取值[0,100]，默认2.5，会按照填写数字进行限行规避，字段类型double，车辆总重=车辆自身重量+货物重量，单位：吨
     * @param weight         车辆长度
     * @param length         单位：米，取值[0,20.0]，默认4.2，会按照填写数字进行限行规避，字段类型double，单位：米
     * @param axle_weight    轴重 单位：吨，取值[0,50]，默认2，会按照填写数字进行限行规避，字段类型double，单位：吨
     * @param axle_count     轴数 取值[0,50]，默认2，会按照填写数字进行限行规避字段类型int64
     * @param is_trailer     是否是挂车
     *                       0：不是(默认)
     *                       1：是
     * @param plate_province 车牌号省份 默认：空字串
     * @param plate_number   车牌号（省份以外号码） 默认：空字串
     * @param plate_color    车牌颜色
     *                       0：蓝色（默认）
     *                       1：黄
     *                       2：黑
     *                       3：白
     *                       4：绿
     * @param departure_time 出发时间 Unix时间戳(秒)，默认为当前时间，一期支持未来3天内的区间：（now_timestamp - 600, now_timestamp + 3 * 86400）
     * @param tactics        驾驶策略：
     *                       0：默认 （时间
     *                       1：距离优先 货车政策交规（如交通部门发布的分时段区域限行政策）剥离
     * @param avoid_type     货车政策交规（如交通部门发布的分时段区域限行政策）剥离
     *                       0：政策交规默认生效；
     *                       1：算路时忽略针对货车的政策交规（道路上实体交通标牌限制仍正常生效）
     *                       3：不走高速
     *                       6：少收费
     * @param user_mark      用户标识
     *                       规避自定义区域时的特殊字段
     *                       格式：大小写字母、数字、英文逗号、英文分号
     * @param sn             用户的权限签名，若用户所用AK的校验方式为SN校验时该参数必须。参考：SN校验
     * @return
     */
    public static String planRouteBatch(
            String ak,
            String origins,
            String destinations,
            String coord_type,
            Double height,
            Double width,
            Double weight,
            Double length,
            Integer axle_weight,
            Integer axle_count,
            Integer is_trailer,
            String plate_province,
            String plate_number,
            Integer plate_color,
            Integer departure_time,
            Integer tactics,
            Integer avoid_type,
            String user_mark,
            String sn
    ) {
        String urlAccessToken = "http://api.map.baidu.com/logistics_routematrix/v1/truck?"
                + "ak=" + ak
                + "&origins=" + origins
                + "&destinations=" + destinations
                + "&coord_type=" + coord_type
                + "&height=" + height
                + "&width=" + width
                + "&weight=" + weight
                + "&length=" + length
                + "&axle_weight=" + axle_weight
                + "&axle_count=" + axle_count
//                + "&is_trailer=" + is_trailer
                + (StrUtil.isEmpty(plate_province) ? "" : ("&plate_province=" + plate_province))
                + (StrUtil.isEmpty(plate_number) ? "" : ("&plate_province=" + plate_province))
                + "&plate_color=" + plate_color
                + "&departure_time=" + departure_time
                + "&tactics=" + tactics
                + "&avoid_type=" + avoid_type
                + (StrUtil.isEmpty(user_mark) ? "" : ("&user_mark=" + user_mark))
                + (StrUtil.isEmpty(sn) ? "" : ("&sn=" + sn));
        String responseString = HttpUtil.get(urlAccessToken, 5000);
        if (null == responseString) {
            log.error("BaiduMap Service Error: {}", "货车批量算路查询接口调用失败");
            return "-1" ;
        }
        return responseString;
    }
}
