package com.wzc.common.map.model.matrix;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "百度-路网矩阵-添加矩阵（结果）")
@Data
@ToString
public class BaiduCreateMatrixRes {

    @Schema(description = "路网ID（百度返回）")
    private String id;

    @Schema(description = "路网版本（百度返回 增加网点删除网点更新网点 路网版本会变化，路网ID不变）")
    private String commitId;

    @Schema(description = "RUNNING, FINISHED, ERROR 计算状态")
    private String status;
}
