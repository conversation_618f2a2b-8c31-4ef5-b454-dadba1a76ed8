package com.wzc.common.map.model.track;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class BaiduPlanRouteBatchReq {

    /**
     * 起点坐标串
     * 格式为：纬度,经度|纬度,经度。如：21.22345,112.11478|21.47832,112.37854 注：起终点个数乘积不超过50
     */
    @Schema(description = "起点坐标串", required = true)
    private String origins;

    /**
     * 终点坐标串
     * 格式与起点坐标相同 注：起终点个数乘积不超过50
     */
    @Schema(description = "终点坐标串", required = true)
    private String destinations;

    /**
     * 输入坐标类型
     * 坐标类型，可选参数，默认为bd09ll 允许的值为：
     * bd09ll（百度经纬度坐标）
     * bd09mc（百度墨卡托坐标）
     * gcj02（国测局加密坐标）
     * wgs84（gps设备获取的坐标）
     */
    @Schema(description = "输入坐标类型")
    private String coordType = "bd09ll";

    /**
     * 车辆高度
     * 单位：米，取值[0,5.0]，默认1.8，会按照填写数字进行限行规避，字段类型double，单位：米
     */
    @Schema(description = "车辆高度")
    private Double height = 1.8;

    /**
     * 车辆宽度
     * 单位：米，取值[0,3.0]，默认1.9，会按照填写数字进行限行规避，字段类型double，单位：米
     */
    @Schema(description = "车辆宽度")
    private Double width = 1.9;

    /**
     * 车辆总重
     * 车辆总重=车辆自身重量+货物重量，单位：吨，取值[0,100]，默认2.5，会按照填写数字进行限行规避，字段类型double，车辆总重=车辆自身重量+货物重量，单位：吨
     */
    @Schema(description = "车辆总重")
    private Double weight = 2.5;

    /**
     * 车辆长度
     * 单位：米，取值[0,20.0]，默认4.2，会按照填写数字进行限行规避，字段类型double，单位：米
     */
    @Schema(description = "车辆长度")
    private Double length = 4.2;

    /**
     * 轴重
     * 单位：吨，取值[0,50]，默认2，会按照填写数字进行限行规避，字段类型double，单位：吨
     */
    @Schema(description = "轴重")
    private Integer axleWeight = 2;

    /**
     * 轴数
     * 取值[0,50]，默认2，会按照填写数字进行限行规避字段类型int64
     */
    @Schema(description = "轴数")
    private Integer axleCount = 2;

    /**
     * 是否是挂车
     * 0：不是(默认)
     * 1：是
     */
    @Schema(description = "是否是挂车")
    private Integer isTrailer = 0;

    /**
     * 车牌号省份
     * 默认：空字串
     */
    @Schema(description = "车牌号省份")
    private String plateProvince = "";

    /**
     * 车牌号（省份以外号码）
     * 默认：空字串
     */
    @Schema(description = "车牌号（省份以外号码）")
    private String plateNumber = "";

    /**
     * 车牌颜色
     * 0：蓝色（默认）
     * 1：黄
     * 2：黑
     * 3：白
     * 4：绿
     */
    @Schema(description = "车牌号（省份以外号码）")
    private Integer plateColor = 0;

    /**
     * 出发时间
     * Unix时间戳(秒)，默认为当前时间，一期支持未来3天内的区间：（now_timestamp - 600, now_timestamp + 3 * 86400）
     */
    @Schema(description = "出发时间")
    private Integer departureTime = Math.toIntExact(System.currentTimeMillis());

    /**
     * 驾驶策略：
     * 0：默认 （时间优先）
     * 1：距离优先
     * 3：不走高速
     * 6：少收费
     */
    @Schema(description = "驾驶策略")
    private Integer tactics = 0;

    /**
     * 货车政策交规（如交通部门发布的分时段区域限行政策）剥离
     * 0：政策交规默认生效；
     * 1：算路时忽略针对货车的政策交规（道路上实体交通标牌限制仍正常生效）
     */
    @Schema(description = "货车政策交规（如交通部门发布的分时段区域限行政策）剥离")
    private Integer avoidType = 0;

    /**
     * 用户标识
     * 规避自定义区域时的特殊字段
     * 格式：大小写字母、数字、英文逗号、英文分号
     */
    @Schema(description = "用户标识")
    private String userMark;

    /**
     * 用户的权限签名，若用户所用AK的校验方式为SN校验时该参数必须。参考：SN校验
     */
    @Schema(description = "用户的权限签名，若用户所用AK的校验方式为SN校验时该参数必须。参考：SN校验")
    private String sn;
}
