package com.wzc.common.report.config;

import com.wzc.common.string.StringPool;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "report")
public class ReportProperties {
	private Boolean enabled = true;
	private Boolean disableHttpSessionReportCache = false;
	private Boolean disableFileProvider = true;
	private String fileStoreDir = StringPool.EMPTY;
	private Boolean debug = false;
}
