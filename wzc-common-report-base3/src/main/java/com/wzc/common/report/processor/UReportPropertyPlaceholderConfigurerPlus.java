package com.wzc.common.report.processor;

import com.bstek.ureport.UReportPropertyPlaceholderConfigurer;
import com.wzc.common.report.config.ReportProperties;
import com.wzc.common.util.spring.PropertyUtil;
import org.springframework.beans.factory.annotation.Value;

import java.util.Properties;

public class UReportPropertyPlaceholderConfigurerPlus extends UReportPropertyPlaceholderConfigurer {

	public UReportPropertyPlaceholderConfigurerPlus(ReportProperties properties) {
		Properties props = new Properties();
		props.setProperty("ureport.disableHttpSessionReportCache", properties.getDisableHttpSessionReportCache().toString());
		props.setProperty("ureport.disableFileProvider", properties.getDisableFileProvider().toString());
		props.setProperty("ureport.fileStoreDir", properties.getFileStoreDir());
		props.setProperty("ureport.debug", properties.getDebug().toString());
		this.setProperties(props);
	}
}
