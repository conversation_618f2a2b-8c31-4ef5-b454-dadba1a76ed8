<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wzc</groupId>
        <artifactId>wzc-common-parent-base3</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>

    <artifactId>wzc-common-adapter</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <version>3.1.7</version>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-cache-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-openfeign-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-log-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
    </dependencies>
</project>