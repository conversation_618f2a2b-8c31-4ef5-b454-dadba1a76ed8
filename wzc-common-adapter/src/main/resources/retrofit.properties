# =====================
# Retrofit 全局配置
# =====================

# 全局转换器工厂（JSON/基础类型转换）
retrofit.global-converter-factories[0]=com.github.lianjiatech.retrofit.spring.boot.core.BasicTypeConverterFactory
retrofit.global-converter-factories[1]=retrofit2.converter.jackson.JacksonConverterFactory

# 全局调用适配器工厂（扩展的CallAdapterFactory已内置，此处无需重复配置）
# retrofit.global-call-adapter-factories=

# =====================
# 日志打印配置
# =====================
# 是否启用请求/响应日志
retrofit.global-log.enable=true
# 日志级别（basic/headers/body）
retrofit.global-log.log-level=info
# 日志打印策略
retrofit.global-log.log-strategy=basic
# 是否聚合打印请求日志（减少重复日志）
retrofit.global-log.aggregate=true

# =====================
# 重试机制配置
# =====================
# 是否启用全局重试
retrofit.global-retry.enable=false
# 重试间隔时间（毫秒）
retrofit.global-retry.interval-ms=100
# 最大重试次数
retrofit.global-retry.max-retries=1
# 触发重试的规则（非2xx响应/IO异常）
retrofit.global-retry.retry-rules[0]=response_status_not_2xx
retrofit.global-retry.retry-rules[1]=occur_io_exception

# =====================
# 超时配置（毫秒）
# =====================
# 读取超时
retrofit.global-timeout.read-timeout-ms=10000
# 写入超时
retrofit.global-timeout.write-timeout-ms=10000
# 连接超时
retrofit.global-timeout.connect-timeout-ms=10000
# 完整调用超时（0表示不限制）
retrofit.global-timeout.call-timeout-ms=0

# =====================
# 熔断降级配置
# =====================
# 降级类型（none/sentinel/resilience4j）
retrofit.degrade.degrade-type=resilience4j

# ----------
# Resilience4j 配置
# ----------
# 是否启用Resilience4j熔断
retrofit.degrade.global-resilience4j-degrade.enable=true
# 从CircuitBreakerConfigRegistry获取配置的名称
retrofit.degrade.global-resilience4j-degrade.circuit-breaker-config-name=defaultCircuitBreakerConfig

# =====================
# 其他配置
# =====================
# 是否自动设置PathMathInterceptor为原型作用域（解决线程安全问题）
retrofit.auto-set-prototype-scope-for-path-math-interceptor=true
