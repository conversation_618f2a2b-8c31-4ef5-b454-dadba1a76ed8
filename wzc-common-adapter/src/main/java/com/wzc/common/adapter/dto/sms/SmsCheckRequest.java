package com.wzc.common.adapter.dto.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 短信状态检查请求
 * 用于检查是否可以向指定手机号发送短信
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SmsCheckRequest {
    
    /**
     * 请求ID（通过该id可以追踪我车接口在调整个请求及以及响应报文）
     */
    @JsonProperty("requestId")
    private String requestId;
    
    /**
     * 我车车用户ID
     */
    @JsonProperty("userId")
    private Long userId;
    
    /**
     * 用户角色
     */
    @JsonProperty("userType")
    private String userType;
    
    /**
     * 企业ID（该服务使用的企业）
     */
    @JsonProperty("companyId")
    private Long companyId;
    
    /**
     * 服务场景（金融找车，若有新增，提前沟通）
     */
    @JsonProperty("fwScene")
    private String fwScene;
    
    /**
     * 请求参数
     */
    @JsonProperty("param")
    private SmsParam param;
    
    /**
     * 短信参数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SmsParam {
        
        /**
         * 接收短信手机号码
         */
        @JsonProperty("phone")
        private String phone;
        
        /**
         * 短信内容
         */
        @JsonProperty("message")
        private String message;
    }
}