package com.wzc.common.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Token响应DTO
 * 用于解析第三方API返回的token信息
 *
 * <AUTHOR>
 */
@Data
public class TokenResponse extends BaseResponse<TokenData> {

    /**
     * Token数据内部类
     */
    @Data
    public static class TokenData {
        @JsonProperty("access_token")
        private String accessToken;

        @JsonProperty("expires_in")
        private Long expiresIn;
    }

    /**
     * 获取访问token
     *
     * @return 访问token字符串
     */
    public String getAccessToken() {
        TokenData data = getData();
        return data != null ? data.getAccessToken() : null;
    }

    /**
     * 获取过期时间（秒）
     *
     * @return 过期时间
     */
    public Long getExpiresIn() {
        TokenData data = getData();
        return data != null ? data.getExpiresIn() : null;
    }

    /**
     * 获取token值（兼容旧方法）
     *
     * @return token字符串
     */
    public String getToken() {
        return getAccessToken();
    }
}
