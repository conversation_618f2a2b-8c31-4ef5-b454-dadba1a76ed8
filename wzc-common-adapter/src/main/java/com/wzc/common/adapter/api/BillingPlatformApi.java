package com.wzc.common.adapter.api;

import com.github.lianjiatech.retrofit.spring.boot.core.RetrofitClient;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.Intercept;
import com.wzc.common.adapter.dto.BaseResponse;
import com.wzc.common.adapter.dto.TokenResponse;
import com.wzc.common.adapter.dto.sms.SmsCheckRequest;
import com.wzc.common.adapter.dto.sms.SmsCheckResponse;
import com.wzc.common.adapter.interceptor.TokenInterceptor;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * 计费平台API接口定义
 * 包含token获取和业务接口
 *
 * <AUTHOR>
 */
@RetrofitClient(baseUrl = "${billing-platform.api.base-url}")
public interface BillingPlatformApi {
    
    /**
     * 获取token接口
     * 
     * @param appKey 应用Key
     * @param appSecret 应用Secret
     * @param timestamp 当前系统时间的毫秒级时间戳
     * @return token响应对象
     */
    @GET("/api/applyToken")
    TokenResponse getToken(@Query("appKey") String appKey, 
                           @Query("appSecret") String appSecret,
                           @Query("timestamp") long timestamp);
    
    /**
     * 检查短信发送状态接口
     * 用于检查是否可以向指定手机号发送短信
     * 
     * @param request 短信检查请求
     * @return 短信检查响应
     */
    @POST("/commons/api/messageCheckFwStatus")
    @Intercept(handler = TokenInterceptor.class, include = "/**")
    BaseResponse<SmsCheckResponse> checkSmsStatus(@Body SmsCheckRequest request);
    
    /**
     * 业务数据获取接口示例
     * 使用TokenInterceptor进行拦截，自动添加token
     * 
     * @param param 请求参数
     * @return 业务数据
     */
    @GET("/api/business/data")
    @Intercept(handler = TokenInterceptor.class, include = "/**")
    BaseResponse<Object> getBusinessData(@Query("param") String param);
}