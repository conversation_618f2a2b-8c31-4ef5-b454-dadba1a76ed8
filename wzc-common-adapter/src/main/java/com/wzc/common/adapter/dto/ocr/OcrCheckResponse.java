package com.wzc.common.adapter.dto.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OCR识别状态检查响应DTO
 * 用于接收OCR识别状态检查的结果
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcrCheckResponse {
    
    /**
     * 请求ID
     */
    @JsonProperty("requestId")
    private String requestId;
    
    /**
     * 是否可以进行OCR识别
     */
    @JsonProperty("canOcr")
    private Boolean canOcr;
    
    /**
     * 响应消息
     */
    @JsonProperty("message")
    private String message;
    
    /**
     * 错误码（如果有错误）
     */
    @JsonProperty("errorCode")
    private String errorCode;
    
    /**
     * 额外信息
     */
    @JsonProperty("extraInfo")
    private String extraInfo;
}
