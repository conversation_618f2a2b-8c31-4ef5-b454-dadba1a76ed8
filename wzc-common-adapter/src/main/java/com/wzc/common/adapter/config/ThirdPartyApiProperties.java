package com.wzc.common.adapter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 第三方API配置属性类
 * 用于配置第三方API的基础URL、认证信息和token缓存相关参数
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "third-party.api")
public class ThirdPartyApiProperties {
    /**
     * API基础URL
     */
    private String baseUrl;
    
    /**
     * 应用Key，用于获取token
     */
    private String appKey;
    
    /**
     * 应用Secret，用于获取token
     */
    private String appSecret;
    
    /**
     * token缓存key
     */
    private String tokenCacheKey = "THIRD_PARTY_API_TOKEN";
    
    /**
     * token有效期(秒)，默认2小时
     */
    private long tokenExpireSeconds = 7200;
}