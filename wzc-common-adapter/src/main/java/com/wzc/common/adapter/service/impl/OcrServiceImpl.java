package com.wzc.common.adapter.service.impl;

import com.wzc.common.adapter.api.BillingPlatformApi;
import com.wzc.common.adapter.dto.BaseResponse;
import com.wzc.common.adapter.dto.ocr.OcrCheckRequest;
import com.wzc.common.adapter.dto.ocr.OcrCheckResponse;
import com.wzc.common.adapter.enums.BillingPlatformApiResultCode;
import com.wzc.common.adapter.exception.BillingPlatformApiException;
import com.wzc.common.adapter.service.OcrService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * OCR服务实现类
 * 提供OCR识别状态检查功能，并记录日志
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OcrServiceImpl implements OcrService {
    
    private final BillingPlatformApi billingPlatformApi;
    
    @Override
    public OcrCheckResponse checkOcrStatus(Long userId, String userType, Long companyId, String fwScene, String dispatchNo) {
        // 生成请求ID
        String requestId = UUID.randomUUID().toString().replace("-", "");
        
        try {
            log.info("开始检查OCR识别状态 - 用户ID: {}, 运单号: {}, 请求ID: {}", userId, dispatchNo, requestId);
            
            // 构建请求参数
            OcrCheckRequest.OcrParam param = OcrCheckRequest.OcrParam.builder()
                    .dispatchNo(dispatchNo)
                    .build();
            
            OcrCheckRequest request = OcrCheckRequest.builder()
                    .requestId(requestId)
                    .userId(userId)
                    .userType(userType)
                    .companyId(companyId)
                    .fwScene(fwScene)
                    .parm(param)
                    .build();
            
            // 调用计费平台API
            BaseResponse<OcrCheckResponse> response = billingPlatformApi.checkOcrStatus(request);
            
            if (response.isSuccess()) {
                OcrCheckResponse result = response.getData();
                if (result != null) {
                    result.setRequestId(requestId); // 确保返回请求ID
                    log.info("OCR识别状态检查成功 - 运单号: {}, 可识别: {}, 消息: {}", 
                            dispatchNo, result.getCanOcr(), result.getMessage());
                    
                    // 记录检查日志
                    recordOcrCheckLog(dispatchNo, result, userType, companyId, fwScene);
                    
                    return result;
                } else {
                    log.error("OCR识别状态检查失败 - 运单号: {}, 响应数据为空", dispatchNo);
                    throw new BillingPlatformApiException(BillingPlatformApiResultCode.SERVICE_ABNORMAL);
                }
            } else {
                log.error("OCR识别状态检查失败 - 运单号: {}, 错误码: {}, 错误信息: {}", 
                        dispatchNo, response.getCode(), response.getMsg());
                
                // 记录失败日志
                recordOcrCheckFailLog(dispatchNo, response.getMsg(), userType, companyId, fwScene);
                
                throw new BillingPlatformApiException(response.getCode(), response.getMsg());
            }
            
        } catch (BillingPlatformApiException e) {
            log.error("OCR识别状态检查业务异常 - 运单号: {}, 错误: {}", dispatchNo, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("OCR识别状态检查系统异常 - 运单号: {}", dispatchNo, e);
            
            // 记录失败日志
            recordOcrCheckFailLog(dispatchNo, e.getMessage(), userType, companyId, fwScene);
            
            throw new BillingPlatformApiException(BillingPlatformApiResultCode.SERVICE_ABNORMAL, "OCR识别状态检查失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录OCR检查日志
     *
     * @param dispatchNo 运单号
     * @param result 检查结果
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     */
    private void recordOcrCheckLog(String dispatchNo, OcrCheckResponse result, String userType, Long companyId, String fwScene) {
        try {
            // 注释掉日志记录代码，因为相关依赖不存在
            /*
            OcrDTO ocrDTO = OcrDTO.builder()
                    .dispatchNo(dispatchNo)
                    .canOcr(result.getCanOcr())
                    .message(result.getMessage())
                    .requestId(result.getRequestId())
                    .statusEnums(Boolean.TRUE.equals(result.getCanOcr()) ? 
                            EmbeddingStatusEnums.SUCCESS : EmbeddingStatusEnums.FAIL)
                    .userIdentity(Integer.parseInt(userType))
                    .companyId(companyId)
                    .build();
            
            // 构建业务场景
            BusinessSceneOcrEnums businessScene = getBusinessSceneByFwScene(fwScene);
            CompositeEmbeddingBiz biz = CompositeEmbeddingBiz.builder()
                    .businessScene(businessScene)
                    .build();
            
            // 记录日志
            EmbeddingOcrLog.ocrInfo(ocrDTO, biz);
            */
            log.info("OCR检查记录 - 运单号: {}, 结果: {}", dispatchNo, result.getCanOcr());
        } catch (Exception e) {
            log.error("记录OCR检查日志异常", e);
        }
    }
    
    /**
     * 记录OCR检查失败日志
     *
     * @param dispatchNo 运单号
     * @param errorMsg 错误信息
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     */
    private void recordOcrCheckFailLog(String dispatchNo, String errorMsg, String userType, Long companyId, String fwScene) {
        try {
            // 注释掉日志记录代码，因为相关依赖不存在
            /*
            OcrDTO ocrDTO = OcrDTO.builder()
                    .dispatchNo(dispatchNo)
                    .canOcr(false)
                    .message("检查失败: " + errorMsg)
                    .requestId(UUID.randomUUID().toString())
                    .statusEnums(EmbeddingStatusEnums.FAIL)
                    .userIdentity(Integer.parseInt(userType))
                    .companyId(companyId)
                    .build();
            
            // 构建业务场景
            BusinessSceneOcrEnums businessScene = getBusinessSceneByFwScene(fwScene);
            CompositeEmbeddingBiz biz = CompositeEmbeddingBiz.builder()
                    .businessScene(businessScene)
                    .build();
            
            // 记录日志
            EmbeddingOcrLog.ocrInfo(ocrDTO, biz);
            */
            log.error("OCR检查失败记录 - 运单号: {}, 错误: {}", dispatchNo, errorMsg);
        } catch (Exception e) {
            log.error("记录OCR检查失败日志异常", e);
        }
    }
}
