package com.wzc.common.adapter.enums;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 第三方API结果状态码枚举
 * 
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ThirdPartyApiResultCode implements BizError {
    
    /**
     * 请求成功
     */
    SUCCESS(200, "请求成功"),
    
    /**
     * token失效
     */
    TOKEN_INVALID(401, "token失效"),
    
    /**
     * 服务器内部错误
     */
    SERVER_ERROR(500, "服务器内部错误"),
    
    /**
     * 请求参数错误
     */
    PARAM_ERROR(40301, "请求参数错误"),
    
    /**
     * 平台不支持的服务类型
     */
    SERVICE_TYPE_NOT_SUPPORTED(40302, "平台不支持的服务类型"),
    
    /**
     * 客户未订阅服务
     */
    SERVICE_NOT_SUBSCRIBED(40303, "客户未订阅服务"),
    
    /**
     * 客户服务已经失效
     */
    SERVICE_EXPIRED(40304, "客户服务已经失效"),
    
    /**
     * 客户服务异常，请联系客服
     */
    SERVICE_ABNORMAL(40305, "客户服务异常，请联系客服"),
    
    /**
     * 客户服务已欠费
     */
    SERVICE_ARREARS(40306, "客户服务已欠费");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态说明
     */
    private final String msg;
}