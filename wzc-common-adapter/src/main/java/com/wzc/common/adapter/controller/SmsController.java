package com.wzc.common.adapter.controller;

import com.wzc.common.adapter.dto.sms.SmsCheckResponse;
import com.wzc.common.adapter.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信控制器
 * 提供短信状态检查接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/sms")
public class SmsController {
    
    @Autowired
    private SmsService smsService;
    
    /**
     * 检查是否可以向指定手机号发送短信
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     * @param phone 手机号
     * @param message 短信内容
     * @return 短信检查结果
     */
    @PostMapping("/check")
    public SmsCheckResponse checkSmsStatus(
            @RequestParam("userId") Long userId,
            @RequestParam("userType") String userType,
            @RequestParam("companyId") Long companyId,
            @RequestParam("fwScene") String fwScene,
            @RequestParam("phone") String phone,
            @RequestParam("message") String message) {
        
        return smsService.checkSmsStatus(userId, userType, companyId, fwScene, phone, message);
    }
}