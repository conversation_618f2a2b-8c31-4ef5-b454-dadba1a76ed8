package com.wzc.common.adapter.exception;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import com.baidu.mapcloud.cloudnative.common.model.BizException;
import com.wzc.common.adapter.enums.BillingPlatformApiResultCode;

/**
 * 计费平台API异常
 * 
 * <AUTHOR>
 */
public class BillingPlatformApiException extends BizException {
    
    /**
     * 构造函数
     * 
     * @param error 业务错误
     */
    public BillingPlatformApiException(BizError error) {
        super(error);
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param msg 错误信息
     */
    public BillingPlatformApiException(Integer code, String msg) {
        super(code, msg);
    }
    
    /**
     * 构造函数
     * 
     * @param resultCode 结果状态码枚举
     * @param args 格式化参数
     */
    public BillingPlatformApiException(BillingPlatformApiResultCode resultCode, Object... args) {
        super(resultCode.getCode(), String.format(resultCode.getMsg(), args));
    }
    
    /**
     * 提高性能
     *
     * @return Throwable
     */
    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
    
    /**
     * 根据状态码创建异常
     * 
     * @param code 状态码
     * @param message 错误信息
     * @return 计费平台API异常
     */
    public static BillingPlatformApiException fromCode(Integer code, String message) {
        for (BillingPlatformApiResultCode resultCode : BillingPlatformApiResultCode.values()) {
            if (resultCode.getCode().equals(code)) {
                return new BillingPlatformApiException(resultCode);
            }
        }
        return new BillingPlatformApiException(code, message);
    }
}