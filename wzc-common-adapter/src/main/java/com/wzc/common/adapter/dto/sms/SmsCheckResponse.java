package com.wzc.common.adapter.dto.sms;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 短信状态检查响应
 * 用于检查是否可以向指定手机号发送短信
 * 
 * <AUTHOR>
 */
@Data
public class SmsCheckResponse {

    /**
     * 请求ID
     */
    @JsonProperty("requestId")
    private String requestId;

    /**
     * 是否可以发送短信
     */
    @JsonProperty("success")
    private Boolean success;
    
    /**
     * 状态码
     */
    @JsonProperty("statusCode")
    private String statusCode;
    
    /**
     * 状态描述
     */
    @JsonProperty("statusDesc")
    private String statusDesc;
    
    /**
     * 消息ID（如果可以发送）
     */
    @JsonProperty("messageId")
    private String messageId;
}