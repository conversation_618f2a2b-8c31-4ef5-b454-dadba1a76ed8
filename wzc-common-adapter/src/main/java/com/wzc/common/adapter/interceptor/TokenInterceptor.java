package com.wzc.common.adapter.interceptor;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import com.wzc.common.adapter.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * Token拦截器
 * 用于自动为请求添加Authorization头，实现token认证
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TokenInterceptor extends BasePathMatchInterceptor {
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private TokenService getTokenService() {
        return applicationContext.getBean(TokenService.class);
    }
    
    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        Request request = chain.request();
        String token = getTokenService().getToken();
        Request newRequest = request.newBuilder()
                .header("Authorization", "Bearer " + token)
                .build();
        return chain.proceed(newRequest);
    }
}