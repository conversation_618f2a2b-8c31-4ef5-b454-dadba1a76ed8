package com.wzc.common.adapter.interceptor;

import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;
import com.wzc.common.adapter.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.Interceptor.Chain;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.IOException;

/**
 * Token拦截器
 * 用于自动为请求添加token认证信息
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TokenInterceptor extends BasePathMatchInterceptor {
    
    @Autowired
    private TokenService tokenService;
    
    /**
     * 拦截请求，添加token认证信息
     *
     * @param request 原始请求
     * @return 添加了token的请求
     */
    @Override
    protected Request doIntercept(Request request) {
        // 获取token
        String token = tokenService.getToken();
        
        // 将token添加到请求头
        return request.newBuilder()
                .header("Authorization", "Bearer " + token)
                .build();
    }
}