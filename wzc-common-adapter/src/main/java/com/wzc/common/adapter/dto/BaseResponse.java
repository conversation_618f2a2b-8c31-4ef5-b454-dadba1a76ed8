package com.wzc.common.adapter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wzc.common.adapter.enums.ThirdPartyApiResultCode;
import com.wzc.common.adapter.exception.ThirdPartyApiException;
import lombok.Data;

/**
 * 第三方API基础响应
 * 
 * <AUTHOR>
 */
@Data
public class BaseResponse<T> {
    /**
     * 状态码
     */
    @JsonProperty("code")
    private Integer code;
    
    /**
     * 状态说明
     */
    @JsonProperty("msg")
    private String msg;
    
    /**
     * 响应数据
     */
    @JsonProperty("data")
    private T data;
    
    /**
     * 判断是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return code != null && code == ThirdPartyApiResultCode.SUCCESS.getCode();
    }
    
    /**
     * 获取数据，如果失败则抛出异常
     * 
     * @return 数据
     */
    public T getDataOrThrow() {
        if (isSuccess()) {
            return data;
        }
        throw ThirdPartyApiException.fromCode(code, msg);
    }
}