package com.wzc.common.adapter.service.impl;

import com.wzc.be.log.embedding.core.enums.EmbeddingStatusEnums;
import com.wzc.be.log.embedding.core.model.CompositeEmbeddingBiz;
import com.wzc.be.log.embedding.core.model.CompositeEmbeddingType;
import com.wzc.be.log.embedding.sms.enums.BusinessSceneSmsEnums;
import com.wzc.be.log.embedding.sms.model.SmsDTO;
import com.wzc.be.log.embedding.sms.utils.EmbeddingSmsLog;
import com.wzc.common.adapter.api.ThirdPartyApi;
import com.wzc.common.adapter.dto.BaseResponse;
import com.wzc.common.adapter.dto.sms.SmsCheckRequest;
import com.wzc.common.adapter.dto.sms.SmsCheckResponse;
import com.wzc.common.adapter.enums.ThirdPartyApiResultCode;
import com.wzc.common.adapter.exception.ThirdPartyApiException;
import com.wzc.common.adapter.service.SmsService;
import com.wzc.common.adapter.service.TokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 短信服务实现类
 * 提供短信状态检查功能，并记录日志
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {
    
    @Autowired
    private ThirdPartyApi thirdPartyApi;
    
    @Autowired
    private TokenService tokenService;
    
    @Override
    public SmsCheckResponse checkSmsStatus(Long userId, String userType, Long companyId, String fwScene, String phone, String message) {
        // 生成请求ID
        String requestId = UUID.randomUUID().toString().replace("-", "");
        
        // 构建短信参数
        SmsCheckRequest.SmsParam smsParam = SmsCheckRequest.SmsParam.builder()
                .phone(phone)
                .message(message)
                .build();
        
        // 构建请求对象
        SmsCheckRequest request = SmsCheckRequest.builder()
                .requestId(requestId)
                .userId(userId)
                .userType(userType)
                .companyId(companyId)
                .fwScene(fwScene)
                .param(smsParam)
                .build();
        
        log.info("检查短信状态请求: {}", request);
        
        try {
            // 调用API
            BaseResponse<SmsCheckResponse> response = thirdPartyApi.checkSmsStatus(request);
            
            // 处理token失效的情况，自动刷新token并重试
            if (response != null && ThirdPartyApiResultCode.TOKEN_INVALID.getCode().equals(response.getCode())) {
                log.info("Token已失效，自动刷新token并重试");
                tokenService.refreshToken();
                response = thirdPartyApi.checkSmsStatus(request);
            }
            
            if (response == null) {
                throw new ThirdPartyApiException(ThirdPartyApiResultCode.SERVER_ERROR);
            }
            
            if (!response.isSuccess()) {
                throw ThirdPartyApiException.fromCode(response.getCode(), response.getMsg());
            }
            
            SmsCheckResponse result = response.getData();
            log.info("短信状态检查结果: {}", result);
            
            // 记录短信检查日志
            recordSmsCheckLog(phone, message, result, userType, companyId, fwScene);
            
            return result;
        } catch (ThirdPartyApiException e) {
            // 记录失败日志
            recordSmsCheckFailLog(phone, message, e.getMessage(), userType, companyId, fwScene);
            throw e;
        } catch (Exception e) {
            log.error("检查短信状态异常", e);
            // 记录失败日志
            recordSmsCheckFailLog(phone, message, e.getMessage(), userType, companyId, fwScene);
            throw new ThirdPartyApiException(ThirdPartyApiResultCode.SERVER_ERROR);
        }
    }
    
    /**
     * 记录短信检查日志
     *
     * @param phone 手机号
     * @param content 短信内容
     * @param result 检查结果
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     */
    private void recordSmsCheckLog(String phone, String content, SmsCheckResponse result, String userType, Long companyId, String fwScene) {
        try {
            SmsDTO smsDTO = SmsDTO.builder()
                    .phone(phone)
                    .content(content)
                    .messageId(result.getMessageId())
                    .statusEnums(Boolean.TRUE.equals(result.getSuccess()) ? 
                            EmbeddingStatusEnums.SUCCESS : EmbeddingStatusEnums.FAIL)
                    .userIdentity(Integer.parseInt(userType))
                    .companyId(companyId)
                    .build();
            
            // 构建业务场景
            BusinessSceneSmsEnums businessScene = getBusinessSceneByFwScene(fwScene);
            CompositeEmbeddingBiz biz = CompositeEmbeddingBiz.builder()
                    .businessScene(businessScene)
                    .build();
            
            // 记录日志
            EmbeddingSmsLog.smsInfo(smsDTO, biz);
        } catch (Exception e) {
            log.error("记录短信检查日志异常", e);
        }
    }
    
    /**
     * 记录短信检查失败日志
     *
     * @param phone 手机号
     * @param content 短信内容
     * @param errorMsg 错误信息
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     */
    private void recordSmsCheckFailLog(String phone, String content, String errorMsg, String userType, Long companyId, String fwScene) {
        try {
            SmsDTO smsDTO = SmsDTO.builder()
                    .phone(phone)
                    .content(content + " [错误: " + errorMsg + "]")
                    .messageId(UUID.randomUUID().toString())
                    .statusEnums(EmbeddingStatusEnums.FAIL)
                    .userIdentity(Integer.parseInt(userType))
                    .companyId(companyId)
                    .build();
            
            // 构建业务场景
            BusinessSceneSmsEnums businessScene = getBusinessSceneByFwScene(fwScene);
            CompositeEmbeddingBiz biz = CompositeEmbeddingBiz.builder()
                    .businessScene(businessScene)
                    .build();
            
            // 记录日志
            EmbeddingSmsLog.smsInfo(smsDTO, biz);
        } catch (Exception e) {
            log.error("记录短信检查失败日志异常", e);
        }
    }
    
    /**
     * 根据服务场景获取业务场景枚举
     *
     * @param fwScene 服务场景
     * @return 业务场景枚举
     */
    private BusinessSceneSmsEnums getBusinessSceneByFwScene(String fwScene) {
        try {
            int sceneCode = Integer.parseInt(fwScene);
            for (BusinessSceneSmsEnums scene : BusinessSceneSmsEnums.values()) {
                if (scene.getCode() == sceneCode) {
                    return scene;
                }
            }
            return BusinessSceneSmsEnums.EXTERNAL_INTERFACE_CALL; // 默认为外部接口调用
        } catch (NumberFormatException e) {
            return BusinessSceneSmsEnums.EXTERNAL_INTERFACE_CALL;
        }
    }
}