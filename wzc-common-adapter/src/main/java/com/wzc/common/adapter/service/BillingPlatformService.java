package com.wzc.common.adapter.service;

import com.wzc.common.adapter.api.BillingPlatformApi;
import com.wzc.common.adapter.dto.BaseResponse;
import com.wzc.common.adapter.enums.BillingPlatformApiResultCode;
import com.wzc.common.adapter.exception.BillingPlatformApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 计费平台服务
 * 提供调用计费平台API的业务方法
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class BillingPlatformService {
    
    @Autowired
    private BillingPlatformApi billingPlatformApi;
    
    @Autowired
    private TokenService tokenService;
    
    /**
     * 获取业务数据
     *
     * @param param 请求参数
     * @return 业务数据
     */
    public Object getBusinessData(String param) {
        try {
            BaseResponse<?> response = billingPlatformApi.getBusinessData(param);
            
            // 处理token失效的情况，自动刷新token并重试
            if (response != null && BillingPlatformApiResultCode.TOKEN_INVALID.getCode().equals(response.getCode())) {
                log.info("Token已失效，自动刷新token并重试");
                tokenService.refreshToken();
                response =  billingPlatformApi.getBusinessData(param);
            }
            
            if (response == null) {
                throw new BillingPlatformApiException(BillingPlatformApiResultCode.SERVER_ERROR);
            }
            
            return response.getDataOrThrow();
        } catch (BillingPlatformApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("调用计费平台业务接口异常", e);
            throw new BillingPlatformApiException(BillingPlatformApiResultCode.SERVER_ERROR);
        }
    }
    
    /**
     * 处理计费平台API响应
     *
     * @param response 响应对象
     * @param <T> 数据类型
     * @return 业务数据
     */
    public <T> T handleResponse(BaseResponse<T> response) {
        if (response == null) {
            throw new BillingPlatformApiException(BillingPlatformApiResultCode.SERVER_ERROR);
        }
        
        if (BillingPlatformApiResultCode.TOKEN_INVALID.getCode().equals(response.getCode())) {
            // token失效，刷新token
            tokenService.refreshToken();
            throw new BillingPlatformApiException(BillingPlatformApiResultCode.TOKEN_INVALID);
        }
        
        if (!response.isSuccess()) {
            throw BillingPlatformApiException.fromCode(response.getCode(), response.getMsg());
        }
        
        return response.getData();
    }
}