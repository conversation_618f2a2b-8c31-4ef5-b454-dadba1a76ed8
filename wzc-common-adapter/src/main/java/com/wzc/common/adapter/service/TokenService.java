package com.wzc.common.adapter.service;

import com.wzc.common.adapter.api.ThirdPartyApi;
import com.wzc.common.adapter.config.ThirdPartyApiProperties;
import com.wzc.common.adapter.dto.TokenResponse;
import com.wzc.common.adapter.enums.ThirdPartyApiResultCode;
import com.wzc.common.adapter.exception.ThirdPartyApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Token服务
 * 负责获取、缓存和刷新第三方API的访问令牌
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TokenService {
    
    @Autowired
    private ThirdPartyApi thirdPartyApi;
    
    @Autowired
    private ThirdPartyApiProperties properties;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 获取token，优先从缓存获取，缓存不存在则重新请求
     *
     * @return 有效的访问令牌
     */
    public String getToken() {
        String cacheKey = properties.getTokenCacheKey();
        String token = redisTemplate.opsForValue().get(cacheKey);
        
        if (token == null || token.isEmpty()) {
            log.info("Token不存在或已过期，重新获取token");
            token = refreshToken();
        }
        
        return token;
    }
    
    /**
     * 刷新token
     * 从第三方API获取新的token并缓存
     *
     * @return 新的访问令牌
     */
    public synchronized String refreshToken() {
        String cacheKey = properties.getTokenCacheKey();
        String token = redisTemplate.opsForValue().get(cacheKey);
        if (token != null && !token.isEmpty()) {
            return token;
        }
        
        try {
            // 获取当前时间戳（毫秒）
            long timestamp = System.currentTimeMillis();
            
            TokenResponse response = thirdPartyApi.getToken(
                    properties.getAppKey(), 
                    properties.getAppSecret(),
                    timestamp
            );
            
            if (response != null && response.isSuccess()) {
                token = response.getToken();
                // 设置缓存，有效期为2小时，提前5分钟过期以避免边界问题
                long expireSeconds = properties.getTokenExpireSeconds() - 300;
                redisTemplate.opsForValue().set(cacheKey, token, expireSeconds, TimeUnit.SECONDS);
                log.info("成功获取新token，有效期：{}秒", expireSeconds);
                return token;
            } else {
                String errorMsg = response != null ? response.getMsg() : "返回为空";
                log.error("获取token失败：{}", errorMsg);
                if (response != null) {
                    throw ThirdPartyApiException.fromCode(response.getCode(), response.getMsg());
                } else {
                    throw new ThirdPartyApiException(ThirdPartyApiResultCode.SERVER_ERROR);
                }
            }
        } catch (ThirdPartyApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取token异常", e);
            throw new ThirdPartyApiException(ThirdPartyApiResultCode.SERVER_ERROR);
        }
    }
}
