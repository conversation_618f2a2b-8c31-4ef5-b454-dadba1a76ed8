package com.wzc.common.adapter.service;

import org.springframework.data.redis.core.StringRedisTemplate;
import com.wzc.common.adapter.api.BillingPlatformApi;
import com.wzc.common.adapter.config.BillingPlatformApiProperties;
import com.wzc.common.adapter.dto.TokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Token服务
 * 负责获取和刷新计费平台API的访问令牌
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TokenService {
    
    @Autowired
    private BillingPlatformApi billingPlatformApi;
    
    @Autowired
    private BillingPlatformApiProperties properties;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 获取token
     * 优先从缓存获取，缓存不存在则重新获取
     *
     * @return token字符串
     */
    public String getToken() {
        String cacheKey = properties.getTokenCacheKey();
        String token = redisTemplate.opsForValue().get(cacheKey);
        
        if (token == null) {
            return refreshToken();
        }
        
        return token;
    }
    
    /**
     * 刷新token
     * 调用API获取新token并缓存
     *
     * @return 新token
     */
    public String refreshToken() {
        String cacheKey = properties.getTokenCacheKey();
        String token;
        
        try {
            TokenResponse response = billingPlatformApi.getToken(
                    properties.getAppKey(), 
                    properties.getAppSecret(),
                    System.currentTimeMillis()
            );
            
            if (response != null && response.getAccessToken() != null) {
                token = response.getAccessToken();
                long expireSeconds = response.getExpiresIn() != null ? 
                        response.getExpiresIn() - 60 : properties.getTokenExpireSeconds() - 60;
                redisTemplate.opsForValue().set(cacheKey, token, java.time.Duration.ofSeconds(expireSeconds));
                log.info("成功获取新token，有效期：{}秒", expireSeconds);
                return token;
            } else {
                log.error("获取token失败，返回为空");
                throw new RuntimeException("获取计费平台API token失败");
            }
        } catch (Exception e) {
            log.error("获取token异常", e);
            throw new RuntimeException("获取计费平台API token异常: " + e.getMessage(), e);
        }
    }
}