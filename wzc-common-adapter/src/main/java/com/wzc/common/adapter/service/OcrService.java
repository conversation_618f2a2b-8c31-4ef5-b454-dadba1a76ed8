package com.wzc.common.adapter.service;

import com.wzc.common.adapter.dto.ocr.OcrCheckResponse;

/**
 * OCR服务接口
 * 提供OCR识别状态检查功能
 *
 * <AUTHOR>
 */
public interface OcrService {
    
    /**
     * 检查OCR识别状态
     * 检查指定运单号是否可以进行OCR识别
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @param companyId 企业ID
     * @param fwScene 服务场景
     * @param dispatchNo 运单号
     * @return OCR检查响应
     */
    OcrCheckResponse checkOcrStatus(Long userId, String userType, Long companyId, String fwScene, String dispatchNo);
}
