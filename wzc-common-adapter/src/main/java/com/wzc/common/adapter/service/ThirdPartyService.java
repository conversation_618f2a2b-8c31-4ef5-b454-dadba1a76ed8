package com.wzc.common.adapter.service;

import com.wzc.common.adapter.api.ThirdPartyApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 第三方服务
 * 提供调用第三方API的业务方法
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdPartyService {
    
    @Autowired
    private ThirdPartyApi thirdPartyApi;
    
    /**
     * 获取业务数据
     *
     * @param param 请求参数
     * @return 业务数据
     */
    public Object getBusinessData(String param) {
        try {
            return thirdPartyApi.getBusinessData(param);
        } catch (Exception e) {
            log.error("调用第三方业务接口异常", e);
            throw new RuntimeException("调用第三方业务接口异常: " + e.getMessage(), e);
        }
    }
}
