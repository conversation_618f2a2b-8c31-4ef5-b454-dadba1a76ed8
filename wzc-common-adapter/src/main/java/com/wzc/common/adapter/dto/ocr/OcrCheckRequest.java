package com.wzc.common.adapter.dto.ocr;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OCR识别状态检查请求DTO
 * 用于检查OCR识别是否可用
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OcrCheckRequest {
    
    /**
     * 请求ID（通过这个ID可以通过我们接口查询到请求来源）
     */
    private String requestId;
    
    /**
     * 我数据用户ID
     */
    private Long userId;
    
    /**
     * 用户角色
     */
    private String userType;
    
    /**
     * 企业ID（该服务使用的企业）
     */
    private Long companyId;
    
    /**
     * 服务场景（参照接口文档，有有新增、提醒咨询）
     */
    private String fwScene;
    
    /**
     * 请求参数
     */
    private OcrParam parm;
    
    /**
     * OCR参数内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OcrParam {
        /**
         * 运单号
         */
        private String dispatchNo;
    }
}
