package com.wzc.common.adapter.service;

import com.wzc.common.adapter.dto.sms.SmsCheckResponse;

/**
 * 短信服务接口
 * 提供短信状态检查功能
 *
 * <AUTHOR>
 */
public interface SmsService {
    
    /**
     * 检查是否可以向指定手机号发送短信
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     * @param phone 手机号
     * @param message 短信内容
     * @return 短信检查结果
     */
    SmsCheckResponse checkSmsStatus(Long userId, String userType, Long companyId, String fwScene, String phone, String message);
}