package com.wzc.common.adapter.service;

import com.wzc.common.adapter.api.ThirdPartyApi;
import com.wzc.common.adapter.dto.BaseResponse;
import com.wzc.common.adapter.dto.sms.SmsCheckRequest;
import com.wzc.common.adapter.dto.sms.SmsCheckResponse;
import com.wzc.common.adapter.enums.ThirdPartyApiResultCode;
import com.wzc.common.adapter.exception.ThirdPartyApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * 短信服务
 * 提供短信状态检查功能
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsService {
    
    @Autowired
    private ThirdPartyApi thirdPartyApi;
    
    @Autowired
    private TokenService tokenService;
    
    /**
     * 检查是否可以向指定手机号发送短信
     *
     * @param userId 用户ID
     * @param userType 用户类型
     * @param companyId 公司ID
     * @param fwScene 服务场景
     * @param phone 手机号
     * @param message 短信内容
     * @return 短信检查结果
     */
    public SmsCheckResponse checkSmsStatus(Long userId, String userType, Long companyId, String fwScene, String phone, String message) {
        // 生成请求ID
        String requestId = UUID.randomUUID().toString().replace("-", "");
        
        // 构建短信参数
        SmsCheckRequest.SmsParam smsParam = SmsCheckRequest.SmsParam.builder()
                .phone(phone)
                .message(message)
                .build();
        
        // 构建请求对象
        SmsCheckRequest request = SmsCheckRequest.builder()
                .requestId(requestId)
                .userId(userId)
                .userType(userType)
                .companyId(companyId)
                .fwScene(fwScene)
                .param(smsParam)
                .build();
        
        log.info("检查短信状态请求: {}", request);
        
        try {
            // 调用API
            BaseResponse<SmsCheckResponse> response = thirdPartyApi.checkSmsStatus(request);
            
            // 处理token失效的情况，自动刷新token并重试
            if (response != null && ThirdPartyApiResultCode.TOKEN_INVALID.getCode().equals(response.getCode())) {
                log.info("Token已失效，自动刷新token并重试");
                tokenService.refreshToken();
                response = thirdPartyApi.checkSmsStatus(request);
            }
            
            if (response == null) {
                throw new ThirdPartyApiException(ThirdPartyApiResultCode.SERVER_ERROR);
            }
            
            if (!response.isSuccess()) {
                throw ThirdPartyApiException.fromCode(response.getCode(), response.getMsg());
            }
            
            SmsCheckResponse result = response.getData();
            log.info("短信状态检查结果: {}", result);
            
            return result;
        } catch (ThirdPartyApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("检查短信状态异常", e);
            throw new ThirdPartyApiException(ThirdPartyApiResultCode.SERVER_ERROR);
        }
    }
}