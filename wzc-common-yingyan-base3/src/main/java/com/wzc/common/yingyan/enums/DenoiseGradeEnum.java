package com.wzc.common.yingyan.enums;

public enum DenoiseGradeEnum {
    NOT_DE_NOISE(0, "不去噪"),
    LEVEL_1(1, "去噪等级1"),
    LEVEL_2(2, "去噪等级2"),
    LEVEL_3(3, "去噪等级3"),
    LEVEL_4(4, "去噪等级4"),
    LEVEL_5(5, "去噪等级5");

    private final Integer code;

    private final String name;

    DenoiseGradeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
