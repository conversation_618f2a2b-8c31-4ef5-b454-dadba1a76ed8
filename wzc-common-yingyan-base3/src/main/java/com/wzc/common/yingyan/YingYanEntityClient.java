package com.wzc.common.yingyan;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import com.wzc.common.yingyan.constant.YingYanConstant;
import com.wzc.common.yingyan.enums.YingyanResultCode;
import com.wzc.common.yingyan.exception.YingYanServiceException;
import com.wzc.common.yingyan.req.EntityAroundSearchReq;
import com.wzc.common.yingyan.req.EntityBoundSearchReq;
import com.wzc.common.yingyan.req.EntityDistrictSearchReq;
import com.wzc.common.yingyan.req.EntityListReq;
import com.wzc.common.yingyan.req.EntityPolygonSearchReq;
import com.wzc.common.yingyan.req.EntityReq;
import com.wzc.common.yingyan.req.EntitySearchReq;
import com.wzc.common.yingyan.res.EntityListRes;
import com.wzc.common.yingyan.res.YingYanBaseRes;
import com.wzc.common.yingyan.utils.YingyanHttpUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * entity 管理类接口实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class YingYanEntityClient {


    private final YingyanHttpUtil yingyanHttpUtil;

    public YingYanEntityClient(YingyanHttpUtil yingyanHttpUtil) {
        this.yingyanHttpUtil = yingyanHttpUtil;
    }

    /**
     * 添加一个新的entity
     */
    public YingYanBaseRes entityAdd(EntityReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (StrUtil.isBlank(req.getEntityName())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_NAME_EMPTY);
        }
        params.put("entity_name", req.getEntityName());
        if (StrUtil.isNotBlank(req.getEntityDesc())) {
            params.put("entity_desc", req.getEntityDesc());
        }
        if (MapUtil.isNotEmpty(req.getColumnKey())) {
            params.putAll(req.getColumnKey());
        }
        if (StrUtil.isNotBlank(req.getSn())) {
            params.put("sn", req.getSn());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_ADD, Method.POST, params, YingYanBaseRes.class);
    }

    /**
     * 修改entity
     */
    public YingYanBaseRes entityUpdate(EntityReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (StrUtil.isBlank(req.getEntityName())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_NAME_EMPTY);
        }
        params.put("entity_name", req.getEntityName());
        if (StrUtil.isNotBlank(req.getEntityDesc())) {
            params.put("entity_desc", req.getEntityDesc());
        }
        if (MapUtil.isNotEmpty(req.getColumnKey())) {
            params.putAll(req.getColumnKey());
        }
        if (StrUtil.isNotBlank(req.getSn())) {
            params.put("sn", req.getSn());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_UPDATE, Method.POST, params, YingYanBaseRes.class);
    }

    /**
     * 删除entity
     */
    public YingYanBaseRes entityDelete(EntityReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (StrUtil.isBlank(req.getEntityName())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_NAME_EMPTY);
        }
        params.put("entity_name", req.getEntityName());
        if (StrUtil.isNotBlank(req.getEntityDesc())) {
            params.put("entity_desc", req.getEntityDesc());
        }
        if (MapUtil.isNotEmpty(req.getColumnKey())) {
            params.putAll(req.getColumnKey());
        }
        if (StrUtil.isNotBlank(req.getSn())) {
            params.put("sn", req.getSn());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_DELETE, Method.POST, params, YingYanBaseRes.class);
    }

    /**
     * 查询entity
     */
    public EntityListRes queryEntityList(EntityListReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (ObjectUtil.isNotNull(req.getFilter())) {
            List<String> filterParam = new ArrayList<>();
            if (StrUtil.isNotBlank(req.getFilter().getEntityNames())) {
                filterParam.add("entity_names:" + req.getFilter().getEntityNames());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getActiveTime())) {
                filterParam.add("active_time:" + req.getFilter().getActiveTime());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getInactiveTime())) {
                filterParam.add("inactive_time:" + req.getFilter().getInactiveTime());
            }
            if (CollUtil.isNotEmpty(filterParam)) {
                params.put("filter", String.join("|", filterParam));
            }
        }
        if (StrUtil.isNotBlank(req.getCoordTypeOutput())) {
            params.put("coord_type_output", req.getCoordTypeOutput());
        }
        if (ObjectUtil.isNotNull(req.getPageIndex())) {
            params.put("page_index", String.valueOf(req.getPageIndex()));
        }
        if (ObjectUtil.isNotNull(req.getPageSize())) {
            params.put("page_size", String.valueOf(req.getPageSize()));
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_LIST, Method.GET, params, EntityListRes.class);
    }


    /**
     * search——关键字搜索
     */
    public EntityListRes queryEntitySearch(EntitySearchReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (ObjectUtil.isNotNull(req.getFilter())) {
            List<String> filterParam = new ArrayList<>();
            if (StrUtil.isNotBlank(req.getFilter().getEntityNames())) {
                filterParam.add("entity_names:" + req.getFilter().getEntityNames());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getActiveTime())) {
                filterParam.add("active_time:" + req.getFilter().getActiveTime());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getInactiveTime())) {
                filterParam.add("inactive_time:" + req.getFilter().getInactiveTime());
            }
            if (CollUtil.isNotEmpty(filterParam)) {
                params.put("filter", String.join("|", filterParam));
            }
        }
        if (StrUtil.isNotBlank(req.getCoordTypeOutput())) {
            params.put("coord_type_output", req.getCoordTypeOutput());
        }
        if (ObjectUtil.isNotNull(req.getPageIndex())) {
            params.put("page_index", String.valueOf(req.getPageIndex()));
        }
        if (ObjectUtil.isNotNull(req.getPageSize())) {
            params.put("page_size", String.valueOf(req.getPageSize()));
        }
        if (ObjectUtil.isNotNull(req.getQuery())) {
            params.put("query", req.getQuery());
        }
        if (ObjectUtil.isNotNull(req.getSortby())) {
            params.put("sortby", req.getSortby());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_SEARCH, Method.GET, params, EntityListRes.class);
    }

    /**
     * search——矩形范围搜索
     */
    public EntityListRes queryEntityBoundSearch(EntityBoundSearchReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (ObjectUtil.isNull(req.getBounds())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_BOUNDS_EMPTY);
        }
        params.put("bounds", req.getBounds());
        if (ObjectUtil.isNotNull(req.getFilter())) {
            List<String> filterParam = new ArrayList<>();
            if (StrUtil.isNotBlank(req.getFilter().getEntityNames())) {
                filterParam.add("entity_names:" + req.getFilter().getEntityNames());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getActiveTime())) {
                filterParam.add("active_time:" + req.getFilter().getActiveTime());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getInactiveTime())) {
                filterParam.add("inactive_time:" + req.getFilter().getInactiveTime());
            }
            if (CollUtil.isNotEmpty(filterParam)) {
                params.put("filter", String.join("|", filterParam));
            }
        }
        if (StrUtil.isNotBlank(req.getCoordTypeOutput())) {
            params.put("coord_type_output", req.getCoordTypeOutput());
        }
        if (ObjectUtil.isNotNull(req.getPageIndex())) {
            params.put("page_index", String.valueOf(req.getPageIndex()));
        }
        if (ObjectUtil.isNotNull(req.getPageSize())) {
            params.put("page_size", String.valueOf(req.getPageSize()));
        }
        if (ObjectUtil.isNotNull(req.getSortby())) {
            params.put("sortby", req.getSortby());
        }

        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_BOUNDSEARCH, Method.GET, params, EntityListRes.class);
    }

    /**
     * search——周边搜索
     */
    public EntityListRes queryEntityAroundSearch(EntityAroundSearchReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (ObjectUtil.isNull(req.getCenter()) || ObjectUtil.isNull(req.getRadius())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_CENTER_AND_RADIUS_EMPTY);
        }
        params.put("center", req.getCenter());
        params.put("radius", req.getRadius());

        if (ObjectUtil.isNotNull(req.getFilter())) {
            List<String> filterParam = new ArrayList<>();
            if (StrUtil.isNotBlank(req.getFilter().getEntityNames())) {
                filterParam.add("entity_names:" + req.getFilter().getEntityNames());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getActiveTime())) {
                filterParam.add("active_time:" + req.getFilter().getActiveTime());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getInactiveTime())) {
                filterParam.add("inactive_time:" + req.getFilter().getInactiveTime());
            }
            if (CollUtil.isNotEmpty(filterParam)) {
                params.put("filter", String.join("|", filterParam));
            }
        }
        if (StrUtil.isNotBlank(req.getCoordTypeOutput())) {
            params.put("coord_type_output", req.getCoordTypeOutput());
        }
        if (ObjectUtil.isNotNull(req.getPageIndex())) {
            params.put("page_index", String.valueOf(req.getPageIndex()));
        }
        if (ObjectUtil.isNotNull(req.getPageSize())) {
            params.put("page_size", String.valueOf(req.getPageSize()));
        }
        if (ObjectUtil.isNotNull(req.getSortby())) {
            params.put("sortby", req.getSortby());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_AROUNDSEARCH, Method.GET, params, EntityListRes.class);
    }

    /**
     * search——多边形搜索
     */
    public EntityListRes queryEntityPolygonSearch(EntityPolygonSearchReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (ObjectUtil.isNull(req.getVertexes())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_VERTEXES_EMPTY);
        }
        params.put("vertexes", req.getVertexes());

        if (ObjectUtil.isNotNull(req.getFilter())) {
            List<String> filterParam = new ArrayList<>();
            if (StrUtil.isNotBlank(req.getFilter().getEntityNames())) {
                filterParam.add("entity_names:" + req.getFilter().getEntityNames());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getActiveTime())) {
                filterParam.add("active_time:" + req.getFilter().getActiveTime());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getInactiveTime())) {
                filterParam.add("inactive_time:" + req.getFilter().getInactiveTime());
            }
            if (CollUtil.isNotEmpty(filterParam)) {
                params.put("filter", String.join("|", filterParam));
            }
        }
        if (StrUtil.isNotBlank(req.getCoordTypeOutput())) {
            params.put("coord_type_output", req.getCoordTypeOutput());
        }
        if (ObjectUtil.isNotNull(req.getPageIndex())) {
            params.put("page_index", String.valueOf(req.getPageIndex()));
        }
        if (ObjectUtil.isNotNull(req.getPageSize())) {
            params.put("page_size", String.valueOf(req.getPageSize()));
        }
        if (ObjectUtil.isNotNull(req.getSortby())) {
            params.put("sortby", req.getSortby());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_POLYGONSEARCH, Method.GET, params, EntityListRes.class);
    }

    /**
     * districtsearch —— 行政区搜索
     */
    public EntityListRes queryEntityDistrictSearch(EntityDistrictSearchReq req) {
        Map<String, Object> params = new HashMap<>(YingYanConstant.PARAMS_INITIAL_CAPACITY);
        if (ObjectUtil.isNull(req.getKeyword())) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_ENTITY_KEYWORD_EMPTY);
        }
        params.put("keyword", req.getKeyword());
        if (ObjectUtil.isNotNull(req.getFilter())) {
            List<String> filterParam = new ArrayList<>();
            if (StrUtil.isNotBlank(req.getFilter().getEntityNames())) {
                filterParam.add("entity_names:" + req.getFilter().getEntityNames());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getActiveTime())) {
                filterParam.add("active_time:" + req.getFilter().getActiveTime());
            }
            if (ObjectUtil.isNotNull(req.getFilter().getInactiveTime())) {
                filterParam.add("inactive_time:" + req.getFilter().getInactiveTime());
            }
            if (CollUtil.isNotEmpty(filterParam)) {
                params.put("filter", String.join("|", filterParam));
            }
        }
        if (StrUtil.isNotBlank(req.getCoordTypeOutput())) {
            params.put("coord_type_output", req.getCoordTypeOutput());
        }
        if (ObjectUtil.isNotNull(req.getPageIndex())) {
            params.put("page_index", String.valueOf(req.getPageIndex()));
        }
        if (ObjectUtil.isNotNull(req.getPageSize())) {
            params.put("page_size", String.valueOf(req.getPageSize()));
        }
        if (ObjectUtil.isNotNull(req.getSortby())) {
            params.put("sortby", req.getSortby());
        }
        return yingyanHttpUtil.execute(YingYanConstant.ENTITY_DISTRICTSEARCH, Method.GET, params, EntityListRes.class);
    }
}