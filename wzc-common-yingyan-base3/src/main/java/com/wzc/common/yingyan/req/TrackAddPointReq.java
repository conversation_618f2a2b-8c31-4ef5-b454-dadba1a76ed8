package com.wzc.common.yingyan.req;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class TrackAddPointReq {

    /**
     * entity名称，作为其唯一标识
     */
    @Alias("entity_name")
    private String entityName;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 定位时间
     */
    @Alias("loc_time")
    private Long locTime;
    /**
     * 坐标类型
     */
    @Alias("coord_type_input")
    private String coordTypeInput;
    /**
     * 速度 单位：km/h
     */
    private Double speed;
    /**
     * 楼层
     */
    private String floor;
    /**
     * 方向
     */
    private Integer direction;
    /**
     * 高度
     */
    private Double height;
    /**
     * 定位精度 单位：m
     */
    private String radius;

    /**
     * 自定义字段派车单号
     */
    private String dispatchCarNo;

}
