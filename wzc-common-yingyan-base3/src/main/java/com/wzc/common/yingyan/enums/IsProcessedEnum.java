package com.wzc.common.yingyan.enums;

public enum IsProcessedEnum {
    OPEN(1, "打开"),
    CLOSE(0, "关闭")
    ;
    private final Integer code;

    private final String name;

    IsProcessedEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
