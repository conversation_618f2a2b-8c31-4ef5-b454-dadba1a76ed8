package com.wzc.common.yingyan.req;

import lombok.Data;

/**
 * 停留点查询请求
 *
 * <AUTHOR>
 */
@Data
public class AnalysisStayPointReq {

    /**
     * 终端名称
     */
    private String entityName;
    /**
     * 起始时间
     */
    private Long startTime;
    /**
     * 结束时间
     */
    private Long endTime;
    /**
     * 停留时间(单位：秒，默认值：600）
     */
    private Integer stayTime;
    /**
     * 停留半径
     */
    private Integer stayRadius;
    /**
     * 纠偏选项
     */
    private ProcessOptionReq processOption;
    /**
     * 返回结果的坐标类型
     */
    private String coordTypeOutput;
    /**
     * sn
     */
    private String sn;
}
