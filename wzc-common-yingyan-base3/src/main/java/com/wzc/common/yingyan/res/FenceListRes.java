package com.wzc.common.yingyan.res;

import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 围栏列表返回数据
 *
 * <AUTHOR>
 */
@Data
@ToString
public class FenceListRes extends YingYanBaseRes {

    /**
     * 返回删除成功的围栏 id 列表
     */
    private Integer total;
    /**
     * 本页返回的结果数量
     */
    private Integer size;
    /**
     * 围栏列表
     */
    private List<FencesRes> fences;

    @Data
    @ToString
    public class FencesRes {
        /**
         * 围栏唯一标识
         */
        private Integer fenceId;
        /**
         * 围栏名称
         */
        private String fenceName;
        /**
         * 围栏的监控对象
         * 1. 该围栏仅监控一个entity时，返回entity_name
         * 2. 该围栏监控service下的所有entity时，返回#allentity
         * 3. 该围栏监控service下的部分entity时，返回#partofentity
         */
        private String monitoredPerson;
        /**
         * 围栏的形状
         * circle：圆形
         * polygon：多边形
         * polyline：线型
         * district：行政区划
         */
        private String shape;
        /**
         * 经度
         */
        private Double longitude;
        /**
         * 纬度
         */
        private Double latitude;
        /**
         * 半径
         */
        private Double radius;
        /**
         * 多边形和线型围栏的顶点列表
         */
        private List<String> vertexes;
        /**
         * 偏移距离
         */
        private Double offset;
        /**
         * 返回的坐标类型
         */
        private String coordType;
        /**
         * 围栏去噪参数
         */
        private Integer denoise;
        /**
         * 行政区划描述
         */
        private String district;
        /**
         * create_time
         */
        private String createTime;
        /**
         * modify_time
         */
        private String modifyTime;

    }

}
