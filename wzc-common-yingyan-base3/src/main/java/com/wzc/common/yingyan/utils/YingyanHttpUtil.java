package com.wzc.common.yingyan.utils;


import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.wzc.common.yingyan.config.YingYanProperties;
import com.wzc.common.yingyan.constant.YingYanConstant;
import com.wzc.common.yingyan.enums.YingyanResultCode;
import com.wzc.common.yingyan.exception.YingYanServiceException;
import com.wzc.common.yingyan.res.AnalysisStayPointRes;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class YingyanHttpUtil {


    private final YingYanProperties yingYanProperties;

    public YingyanHttpUtil(YingYanProperties yingYanProperties) {
        this.yingYanProperties = yingYanProperties;
    }

    public <T> T execute(String url, Method method, Map<String, Object> param, Function<T, HttpResponse> function) {
        HttpRequest httpRequest;
        switch (method) {
            case GET: {
                httpRequest = HttpRequest.get(url);
                break;
            }
            case POST: {
                httpRequest = HttpRequest.post(url);
                break;
            }
            case PUT: {
                httpRequest = HttpRequest.put(url);
                break;
            }
            case DELETE: {
                httpRequest = HttpRequest.delete(url);
                break;
            }
            default: {
                return null;
            }
        }
        //设置通用的参数
        httpRequest.form(param)
                .form("ak", yingYanProperties.getAk())
                .form("service_id", yingYanProperties.getServiceId());
//        log.debug("yingyan API request method is {}, and the request URL is {}", method, httpRequest.getUrl());
//        log.debug("yingyan API request parameter is {}", JSONUtil.toJsonStr(httpRequest.form()));
        try {
            HttpResponse response = httpRequest
                    .header(Header.CONTENT_TYPE, "application/x-www-form-urlencoded")
                    //超时，毫秒
                    .timeout(60000)
                    .execute();
//            log.debug("yingyan API request result is {}", JSONUtil.toJsonStr(response.body()));
            return function.callback(response);
        } catch (Exception e) {
            //出现错误，抛出异常进行重试
            log.error("yingyan API call failed: {}", e.getMessage());
            throw new YingYanServiceException(YingyanResultCode.BAIDU_YINGYAN_API_ERROR);
        }
    }

    public <T> T execute(String url, Method method, Map<String, Object> param, Class<T> tClass) {
        return execute(yingYanProperties.getHost() + url, method, param, response -> {
            if (response.isOk()) {
                //获取返回的数据
                String jsonData = response.body();
                JSONObject jsonObject = JSONUtil.parseObj(jsonData);
                if (jsonObject.getInt(YingYanConstant.STATUS) != 0) {
                    return JSONUtil.toBean(jsonObject, tClass);
                }
                return JSONUtil.toBean(jsonObject, tClass);
            }
            return null;
        });
    }
}
