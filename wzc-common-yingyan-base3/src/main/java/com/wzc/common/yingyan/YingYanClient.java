package com.wzc.common.yingyan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.wzc.common.yingyan.config.YingYanProperties;
import com.wzc.common.yingyan.enums.*;
import com.wzc.common.yingyan.exception.YingYanServiceException;
import com.wzc.common.yingyan.req.ProcessOptionReq;
import com.wzc.common.yingyan.req.TrackReq;
import com.wzc.common.yingyan.res.HistoryTrackRes;
import com.wzc.common.yingyan.res.TrackInfoRes;
import com.wzc.common.yingyan.res.TrackRes;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static java.util.Objects.nonNull;

@Slf4j
@AllArgsConstructor
public class YingYanClient {

    private final YingYanTrackClient trackClient;
    private final YingYanProperties properties;

    /**
     * 获取鹰眼轨迹并纠偏
     * 根据实体标识和时间范围查询鹰眼轨迹
     * 只返回轨迹
     */
    public List<HistoryTrackRes> correctGetTrack(String entityName, Date beginTime, Date endTime) {
        return correctGetTrackInfo(entityName, beginTime, endTime).getPoints();
    }

    /**
     * 获取鹰眼轨迹并纠偏
     * 根据实体标识和时间范围查询鹰眼轨迹
     */
    public TrackInfoRes correctGetTrackInfo(String entityName, Date beginTime, Date endTime) {
        // 最大查询15天
        long betweenSecond = DateUtil.between(beginTime, endTime, DateUnit.SECOND);
        BigDecimal betweenDay = BigDecimal.valueOf(betweenSecond).divide(BigDecimal.valueOf(86400), 2, RoundingMode.HALF_UP);
        if (betweenDay.compareTo(BigDecimal.valueOf(properties.getMaxQueryInterval())) > 0) {
            throw new YingYanServiceException(YingyanResultCode.YINGYAN_QUERY_TIME_INTERVAL_ERROR);
        }
        // 时间分段
        // 存储分段结果
        List<DateTime[]> segments = new ArrayList<>();

        // 生成分段
        DateTime currentStart = DateUtil.date(beginTime);
        while (currentStart.isBefore(endTime)) {
            DateTime currentEnd = DateUtil.offset(currentStart, DateField.HOUR, 12);
            if (currentEnd.isAfter(endTime)) {
                currentEnd = DateUtil.date(endTime);
            }
            segments.add(new DateTime[]{currentStart, currentEnd});
            currentStart = DateUtil.offsetSecond(currentEnd, 1);
        }
        List<TrackInfoRes> allTrackList = new ArrayList<>();
        for (DateTime[] segment : segments) {
            TrackInfoRes segmentTrack = correctGetTrackSegment(entityName, segment[0], segment[1]);
            allTrackList.add(segmentTrack);
        }
        BigDecimal totalDistance = allTrackList.stream()
                .map(TrackInfoRes::getExpectedMileage)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalTime = allTrackList.stream()
                .map(TrackInfoRes::getConsumingTime)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        List<HistoryTrackRes> allTrackPointList = allTrackList.stream()
                .map(TrackInfoRes::getPoints)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        TrackInfoRes resultTrackInfoRes = new TrackInfoRes();
        resultTrackInfoRes.setExpectedMileage(totalDistance);
        resultTrackInfoRes.setConsumingTime(totalTime);
        resultTrackInfoRes.setPoints(allTrackPointList);
        return resultTrackInfoRes;
    }

    private TrackInfoRes correctGetTrackSegment(String entityName, Date beginTime, Date endTime) {
//        log.info("correctGetTrackSegment entityName:{} beginTime:{} endTime:{}", entityName, DateUtil.formatDateTime(beginTime), DateUtil.formatDateTime(endTime));
        int maxLoop = 500;
        int loop = 0;
        int pageIndex = 1;
        int pageSize = 1000;

        BigDecimal totalDistance = BigDecimal.ZERO;
        BigDecimal totalSpeed = BigDecimal.ZERO;
        int speedCount = 0;
        TrackInfoRes trackInfoRes = new TrackInfoRes();
        List<HistoryTrackRes> resultTrackList = new ArrayList<>();
        trackInfoRes.setPoints(resultTrackList);
        while (true) {
//            log.info("pageIndex:{}", pageIndex);
            ProcessOptionReq processOptionReq = new ProcessOptionReq();
            processOptionReq.setDenoiseGrade(DenoiseGradeEnum.LEVEL_1.getCode());
            processOptionReq.setNeedMapmatch(NeedMapmatchEnum.MATCH.getCode());
            processOptionReq.setTransportMode(TransportModeEnum.AUTO.getCode());
            TrackReq req = new TrackReq();
            req.setEntityName(entityName);
            req.setStartTime(beginTime.getTime() / 1000);
            req.setEndTime(endTime.getTime() / 1000);
            req.setIsProcessed(IsProcessedEnum.OPEN.getCode());
            req.setProcessOption(processOptionReq);
//            req.setSupplementMode(SupplementModeEnum.DRIVING.getCode());
//            req.setSupplementContent(SupplementContentEnum.DISTANCE_AND_POINTS.getCode());
            req.setPageSize(pageSize);
            req.setPageIndex(pageIndex++);
            TrackRes trackRes;
            try {
                trackRes = trackClient.getTrack(req);
            } catch (YingYanServiceException e) {
                log.error("查询鹰眼轨迹异常", e);
                throw new RuntimeException("查询鹰眼轨迹异常");
            }
            if (CollectionUtil.isEmpty(trackRes.getPoints())) {
                break;
            }
            // 超过最大循环次数
            if (loop++ >= maxLoop) {
                break;
            }
            if (totalDistance.compareTo(BigDecimal.ZERO) == 0) {
                totalDistance = NumberUtil.toBigDecimal(trackRes.getDistance());
            }
            for (HistoryTrackRes track : trackRes.getPoints()) {
                if (nonNull(track.getSpeed())) {
                    totalSpeed = totalSpeed.add(NumberUtil.toBigDecimal(track.getSpeed()));
                    speedCount ++;
                }
            }
            resultTrackList.addAll(trackRes.getPoints());
            // 尾页判断
            if (trackRes.getPoints().size() < pageSize) {
                break;
            }
        }
        trackInfoRes.setExpectedMileage(totalDistance.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP));
        if (speedCount > 0) {
            BigDecimal speed = totalSpeed.divide(BigDecimal.valueOf(speedCount), 2, RoundingMode.HALF_UP) ;
            BigDecimal totalDistanceKm = totalDistance.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
            BigDecimal consumingHours = BigDecimal.ZERO;
            if (speed.compareTo(BigDecimal.ZERO) > 0) {
                consumingHours = totalDistanceKm.divide(speed, 2, RoundingMode.HALF_UP);
            }
            trackInfoRes.setConsumingTime(consumingHours);
        }
        return trackInfoRes;
    }
}
